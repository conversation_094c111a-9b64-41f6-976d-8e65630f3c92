{% extends "admin_base.html" %}

{% block title %}AI Analytics Dashboard{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment"></script>

    <!-- Leaflet CSS and JS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Leaflet MarkerCluster plugin for clustering markers -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

    <!-- Load our geolocation configuration module -->
    <script src="{{ url_for('static', filename='js/geo-config.js') }}"></script>

    <style>
        #userLocationMap {
            height: 400px;
            width: 100%;
            border-radius: 0.5rem;
        }

        /* Make Leaflet attribution more visible */
        .leaflet-control-attribution {
            background-color: rgba(255, 255, 255, 0.8) !important;
            padding: 5px 8px !important;
            font-size: 11px !important;
            line-height: 1.4 !important;
            border-radius: 4px !important;
            box-shadow: 0 1px 5px rgba(0,0,0,0.15) !important;
        }

        /* Additional text contrast fixes specific to analytics page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Chart container fixes */
        .bg-white canvas {
            background-color: #ffffff !important;
        }

        /* Card title fixes */
        .bg-white h3.text-lg {
            color: #1a202c !important;
        }

        /* Stat number fixes */
        .bg-white .text-3xl,
        .bg-white .text-4xl,
        .bg-white .text-5xl {
            color: #1a202c !important;
        }

        .leaflet-control-attribution a {
            color: #0078A8 !important;
            text-decoration: none !important;
        }

        .leaflet-control-attribution a:hover {
            text-decoration: underline !important;
        }

        /* Custom marker cluster styles */
        .marker-cluster-small {
            background-color: rgba(181, 226, 140, 0.8);
        }
        .marker-cluster-small div {
            background-color: rgba(110, 204, 57, 0.8);
        }
        .marker-cluster-medium {
            background-color: rgba(241, 211, 87, 0.8);
        }
        .marker-cluster-medium div {
            background-color: rgba(240, 194, 12, 0.8);
        }
        .marker-cluster-large {
            background-color: rgba(253, 156, 115, 0.8);
        }
        .marker-cluster-large div {
            background-color: rgba(241, 128, 23, 0.8);
        }

        /* Custom marker styles */
        .custom-marker {
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 5px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* Dark mode styles */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #374151 !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }
        .dark .text-gray-900 { color: #f3f4f6 !important; }

        /* Chart container fixes in dark mode */
        .dark .bg-white canvas {
            background-color: #1f2937 !important;
        }

        /* Card title fixes in dark mode */
        .dark .bg-white h3.text-lg {
            color: #f3f4f6 !important;
        }

        /* Stat number fixes in dark mode */
        .dark .bg-white .text-3xl,
        .dark .bg-white .text-4xl,
        .dark .bg-white .text-5xl {
            color: #f3f4f6 !important;
        }

        /* Metric cards in dark mode */
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .bg-green-50 { background-color: #065f46 !important; }
        .dark .bg-purple-50 { background-color: #5b21b6 !important; }
        .dark .bg-yellow-50 { background-color: #92400e !important; }

        .dark .border-blue-100 { border-color: #1e40af !important; }
        .dark .border-green-100 { border-color: #047857 !important; }
        .dark .border-purple-100 { border-color: #7c3aed !important; }
        .dark .border-yellow-100 { border-color: #b45309 !important; }

        .dark .text-blue-800 { color: #bfdbfe !important; }
        .dark .text-green-800 { color: #a7f3d0 !important; }
        .dark .text-purple-800 { color: #ddd6fe !important; }
        .dark .text-yellow-800 { color: #fef3c7 !important; }

        .dark .text-blue-600 { color: #60a5fa !important; }
        .dark .text-green-600 { color: #34d399 !important; }
        .dark .text-purple-600 { color: #a78bfa !important; }
        .dark .text-yellow-600 { color: #fcd34d !important; }

        /* Leaflet map in dark mode */
        .dark .leaflet-control-attribution {
            background-color: rgba(31, 41, 55, 0.8) !important;
            color: #e5e7eb !important;
        }

        .dark .leaflet-control-attribution a {
            color: #60a5fa !important;
        }

        /* Table styles in dark mode */
        .dark .divide-gray-200 > * { border-color: #4b5563 !important; }
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }

        /* Form elements in dark mode */
        .dark input[type="date"] {
            background-color: #374151 !important;
            border-color: #6b7280 !important;
            color: #f3f4f6 !important;
        }

        /* Custom marker styles in dark mode */
        .dark .custom-marker {
            border-color: #1f2937 !important;
        }

        /* Legend in dark mode */
        .dark .info.legend {
            background-color: #1f2937 !important;
            color: #e5e7eb !important;
            border-color: #4b5563 !important;
        }

        /* Model Performance Analysis dark mode styles */
        .dark .bg-indigo-50 { background-color: #312e81 !important; }
        .dark .bg-orange-50 { background-color: #9a3412 !important; }
        .dark .bg-red-50 { background-color: #7f1d1d !important; }

        .dark .border-indigo-100 { border-color: #4338ca !important; }
        .dark .border-orange-100 { border-color: #c2410c !important; }
        .dark .border-red-100 { border-color: #b91c1c !important; }

        .dark .text-indigo-800 { color: #c7d2fe !important; }
        .dark .text-orange-800 { color: #fed7aa !important; }
        .dark .text-red-800 { color: #fecaca !important; }

        .dark .text-indigo-600 { color: #a5b4fc !important; }
        .dark .text-orange-600 { color: #fb923c !important; }
        .dark .text-red-600 { color: #f87171 !important; }

        /* Performance score badges in dark mode */
        .dark .bg-green-100 { background-color: #14532d !important; }
        .dark .bg-yellow-100 { background-color: #713f12 !important; }
        .dark .text-green-800 { color: #bbf7d0 !important; }
        .dark .text-yellow-800 { color: #fef3c7 !important; }

        /* Blue recommendation box in dark mode */
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .border-blue-200 { border-color: #1d4ed8 !important; }
        .dark .text-blue-800 { color: #bfdbfe !important; }
        .dark .text-blue-700 { color: #93c5fd !important; }
        .dark .text-blue-600 { color: #60a5fa !important; }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">AI Analytics Dashboard</h1>
            <div class="flex space-x-4 items-center">
<!--                 <a href="{{ url_for('view_sessions') }}" class="text-blue-600 dark:text-blue-400 hover:underline">View Chat Sessions</a>
                <button id="theme-toggle" class="ml-2 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <span id="theme-icon" class="text-xl">🌙</span>
                </button> -->
            </div>
        </div>

            <!-- Filters -->
            <div class="mb-6">
                <form id="filtersForm" class="flex flex-wrap items-end gap-4" method="get">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="start_date" name="start_date" value="{{ start_date }}"
                               class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="end_date" name="end_date" value="{{ end_date }}"
                               class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Apply Filter
                        </button>
                    </div>
                    <div>
                        <a href="{{ url_for('analytics_dashboard') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Reset
                        </a>
                    </div>
                </form>
            </div>

            <!-- Device List -->
            {% if summary.devices %}
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Device Analytics</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for device in summary.devices %}
                        <a href="{{ url_for('device_analytics', device_fingerprint=device.fingerprint) }}" class="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 hover:shadow-sm transition-all">
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-500 font-bold">
                                    {{ loop.index }}
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900">
                                        {% if device.client_name %}
                                            {{ device.client_name }}
                                        {% else %}
                                            Anonymous
                                        {% endif %}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <span class="px-1.5 py-0.5 bg-blue-50 text-blue-700 rounded">ID: {{ device.fingerprint[:8] }}...</span>
                                        <span>{{ device.query_count }} queries</span>
                                    </div>
                                </div>
                            </div>
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Summary Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-blue-50 p-6 rounded-lg border border-blue-100">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Total Queries</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ summary.total_queries|default(0) }}</p>
                </div>
                <div class="bg-green-50 p-6 rounded-lg border border-green-100">
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Total Sessions</h3>
                    <p class="text-3xl font-bold text-green-600">{{ summary.total_sessions|default(0) }}</p>
                </div>
                <div class="bg-purple-50 p-6 rounded-lg border border-purple-100">
                    <h3 class="text-lg font-semibold text-purple-800 mb-2">Total Devices</h3>
                    <p class="text-3xl font-bold text-purple-600">{{ summary.total_devices|default(0) }}</p>
                </div>
                <div class="bg-yellow-50 p-6 rounded-lg border border-yellow-100">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Avg. Processing Time</h3>
                    <p class="text-3xl font-bold text-yellow-600">
                        {% if summary.avg_processing_time is not none %}
                            {{ "%.2f"|format(summary.avg_processing_time) }}s
                        {% else %}
                            0.00s
                        {% endif %}
                    </p>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Processing Time Chart -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Processing Time Trend</h3>
                    <div class="h-64">
                        <canvas id="processingTimeChart"></canvas>
                    </div>
                </div>

                <!-- Query Volume Chart -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Query Volume by Day</h3>
                    <div class="h-64">
                        <canvas id="queryVolumeChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- User Location Map -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Geographic Distribution of Visitors</h3>
                <div id="userLocationMap"></div>
                <div class="mt-4 text-sm text-gray-500">
                    <p>This map shows the approximate locations of visitors who have accessed the system. Each marker represents a unique location.</p>
                    <p class="mt-1"><strong>Note:</strong> Location data is derived from IP addresses using MaxMind's GeoLite2 Web Service and may not be 100% accurate.</p>
                    <div class="mt-2 p-3 bg-blue-50 rounded-md">
                        <p class="text-xs text-blue-700">
                            <strong>Data Attribution:</strong> Geolocation data is provided by
                            <a href="https://www.maxmind.com/" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">MaxMind</a>.
                            Map data &copy; <a href="https://www.openstreetmap.org/copyright" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">OpenStreetMap</a> contributors.
                        </p>
                    </div>
                </div>

                <!-- Geolocation Statistics -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Top Countries -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Top Countries</h4>
                        {% if summary.top_countries %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Queries</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unique Devices</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for country in summary.top_countries %}
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ country.country }}</td>
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ country.count }}</td>
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ country.unique_devices }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-gray-500 italic">No country data available</p>
                        {% endif %}
                    </div>

                    <!-- Top Cities -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Top Cities</h4>
                        {% if summary.top_cities %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Queries</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for city in summary.top_cities %}
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ city.city }}</td>
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ city.region }}</td>
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ city.country }}</td>
                                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ city.count }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-gray-500 italic">No city data available</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- More Stats Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Top Categories -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Top Categories</h3>
                    {% if summary.top_categories %}
                        <div class="space-y-4">
                            {% for category in summary.top_categories %}
                                <div class="flex items-center">
                                    <div class="w-40 truncate">{{ category.category }}</div>
                                    <div class="flex-grow">
                                        <div class="bg-gray-200 h-4 rounded-full overflow-hidden">
                                            {% set percentage = (category.count / summary.total_queries * 100)|round %}
                                            <div class="bg-blue-500 h-full" style="width: {{ percentage }}%"></div>
                                        </div>
                                    </div>
                                    <div class="w-16 text-right">{{ category.count }}</div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-gray-500 italic">No category data available</p>
                    {% endif %}
                </div>

                <!-- Top Models -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Top Models</h3>
                    {% if summary.top_models %}
                        <div class="space-y-4">
                            {% for model in summary.top_models %}
                                <div class="flex items-center">
                                    <div class="w-40 truncate">{{ model.model }}</div>
                                    <div class="flex-grow">
                                        <div class="bg-gray-200 h-4 rounded-full overflow-hidden">
                                            {% set percentage = (model.count / summary.total_queries * 100)|round %}
                                            <div class="bg-green-500 h-full" style="width: {{ percentage }}%"></div>
                                        </div>
                                    </div>
                                    <div class="w-16 text-right">{{ model.count }}</div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-gray-500 italic">No model data available</p>
                    {% endif %}
                </div>
            </div>

            <!-- Common Questions Section -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Common Questions</h3>
                {% if summary.common_questions_global %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frequency</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for question in summary.common_questions_global %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-900">{{ question.question }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ question.category }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                {{ question.count }} times
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-gray-500 italic">No common questions data available</p>
                {% endif %}
            </div>

            <!-- Phase 2: Greeting Analytics Section -->
            {% if summary.greeting_analytics %}
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-handshake mr-2 text-purple-500"></i>
                    Greeting & Session Analytics
                    <span class="text-sm font-normal text-gray-500 ml-2">(Phase 2 Enhancement)</span>
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Total Greetings -->
                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-100">
                        <h4 class="text-sm font-semibold text-purple-800 mb-1">Total Greetings</h4>
                        <p class="text-2xl font-bold text-purple-600">{{ summary.greeting_analytics.total_greetings|default(0) }}</p>
                    </div>

                    <!-- Session Stats -->
                    {% if summary.greeting_analytics.session_stats %}
                    <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
                        <h4 class="text-sm font-semibold text-indigo-800 mb-1">Unique Devices</h4>
                        <p class="text-2xl font-bold text-indigo-600">{{ summary.greeting_analytics.session_stats.unique_devices|default(0) }}</p>
                        <p class="text-xs text-indigo-600 mt-1">
                            Avg: {{ summary.greeting_analytics.session_stats.avg_sessions_per_device|default(0) }} sessions/device
                        </p>
                    </div>
                    {% endif %}

                    <!-- Most Active Time -->
                    {% if summary.greeting_analytics.greetings_by_time %}
                    <div class="bg-teal-50 p-4 rounded-lg border border-teal-100">
                        <h4 class="text-sm font-semibold text-teal-800 mb-1">Most Active Time</h4>
                        {% set most_active = summary.greeting_analytics.greetings_by_time|sort(attribute='count', reverse=true)|first %}
                        {% if most_active %}
                        <p class="text-2xl font-bold text-teal-600 capitalize">{{ most_active.time }}</p>
                        <p class="text-xs text-teal-600 mt-1">{{ most_active.count }} sessions</p>
                        {% else %}
                        <p class="text-2xl font-bold text-teal-600">N/A</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Greeting Types Distribution -->
                    {% if summary.greeting_analytics.greetings_by_type %}
                    <div>
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Greeting Types</h4>
                        <div class="space-y-3">
                            {% for greeting_type in summary.greeting_analytics.greetings_by_type %}
                                <div class="flex items-center">
                                    <div class="w-32 truncate capitalize">{{ greeting_type.type }}</div>
                                    <div class="flex-grow">
                                        <div class="bg-gray-200 h-3 rounded-full overflow-hidden">
                                            {% set total_greetings = summary.greeting_analytics.total_greetings %}
                                            {% if total_greetings > 0 %}
                                                {% set percentage = (greeting_type.count / total_greetings * 100)|round %}
                                                <div class="bg-purple-500 h-full" style="width: {{ percentage }}%"></div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="w-12 text-right text-sm">{{ greeting_type.count }}</div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Time-based Engagement -->
                    {% if summary.greeting_analytics.greetings_by_time %}
                    <div>
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Engagement by Time of Day</h4>
                        <div class="space-y-3">
                            {% for time_data in summary.greeting_analytics.greetings_by_time %}
                                <div class="flex items-center">
                                    <div class="w-20 truncate capitalize">
                                        {% if time_data.time == 'morning' %}
                                            🌅 {{ time_data.time }}
                                        {% elif time_data.time == 'afternoon' %}
                                            ☀️ {{ time_data.time }}
                                        {% elif time_data.time == 'evening' %}
                                            🌙 {{ time_data.time }}
                                        {% else %}
                                            {{ time_data.time }}
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow">
                                        <div class="bg-gray-200 h-3 rounded-full overflow-hidden">
                                            {% set max_count = summary.greeting_analytics.greetings_by_time|map(attribute='count')|max %}
                                            {% if max_count > 0 %}
                                                {% set percentage = (time_data.count / max_count * 100)|round %}
                                                <div class="bg-gradient-to-r from-blue-400 to-purple-500 h-full" style="width: {{ percentage }}%"></div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="w-12 text-right text-sm">{{ time_data.count }}</div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Weekly Engagement Patterns -->
                {% if summary.engagement_patterns and summary.engagement_patterns.by_day_of_week %}
                <div class="mt-6">
                    <h4 class="text-md font-semibold text-gray-700 mb-3">Weekly Engagement Patterns</h4>
                    <div class="grid grid-cols-7 gap-2">
                        {% for day_data in summary.engagement_patterns.by_day_of_week %}
                            <div class="text-center">
                                <div class="text-xs text-gray-500 mb-1">{{ day_data.day[:3] }}</div>
                                <div class="bg-gray-200 h-16 rounded flex items-end justify-center">
                                    {% set max_day_count = summary.engagement_patterns.by_day_of_week|map(attribute='count')|max %}
                                    {% if max_day_count > 0 %}
                                        {% set height_percentage = (day_data.count / max_day_count * 100)|round %}
                                        <div class="bg-gradient-to-t from-green-400 to-blue-500 w-full rounded"
                                             style="height: {{ height_percentage }}%"></div>
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-600 mt-1">{{ day_data.count }}</div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Model Performance Analysis Section -->
            {% if model_performance_data and current_user.is_authenticated and current_user.has_dashboard_permission('model_performance_analysis') %}
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-brain mr-2 text-indigo-500"></i>
                    Model Performance Analysis
                    <span class="text-sm font-normal text-gray-500 ml-2">(Development Research)</span>
                </h3>

                <!-- Summary Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
                        <h4 class="text-sm font-semibold text-indigo-800 mb-1">Total Configurations</h4>
                        <p class="text-2xl font-bold text-indigo-600">{{ model_performance_data.model_comparisons|length }}</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg border border-green-100">
                        <h4 class="text-sm font-semibold text-green-800 mb-1">Models Tested</h4>
                        <p class="text-2xl font-bold text-green-600">{{ model_performance_data.summary.models_tested|length if model_performance_data.summary.models_tested else 0 }}</p>
                    </div>
                    <div class="bg-orange-50 p-4 rounded-lg border border-orange-100">
                        <h4 class="text-sm font-semibold text-orange-800 mb-1">Categories Analyzed</h4>
                        <p class="text-2xl font-bold text-orange-600">{{ model_performance_data.summary.categories_tested|length if model_performance_data.summary.categories_tested else 0 }}</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg border border-red-100">
                        <h4 class="text-sm font-semibold text-red-800 mb-1">Total Queries</h4>
                        <p class="text-2xl font-bold text-red-600">{{ model_performance_data.summary.total_queries|default(0) }}</p>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Hallucination Rate by Mode -->
                    <div class="bg-white p-4 rounded-lg border border-gray-100">
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Anti-Hallucination Mode Effectiveness</h4>
                        <div class="h-64">
                            <canvas id="hallucinationModeChart"></canvas>
                        </div>
                    </div>

                    <!-- Model Performance Comparison -->
                    <div class="bg-white p-4 rounded-lg border border-gray-100">
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Model Processing Time Comparison</h4>
                        <div class="h-64">
                            <canvas id="modelPerformanceChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Top Performing Configurations -->
                {% if model_performance_data.model_comparisons %}
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-700 mb-3">Top Performing Configurations</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mode</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Queries</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hallucination Rate</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Processing Time</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance Score</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for config in model_performance_data.model_comparisons[:10] %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-2 text-sm text-gray-900">{{ config.model_name|truncate(20) }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-500">
                                            <span class="px-2 py-1 text-xs rounded-full
                                                {% if config.anti_hallucination_mode == 'strict' %}bg-red-100 text-red-800
                                                {% elif config.anti_hallucination_mode == 'balanced' %}bg-yellow-100 text-yellow-800
                                                {% else %}bg-green-100 text-green-800{% endif %}">
                                                {{ config.anti_hallucination_mode|title }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-2 text-sm text-gray-500">{{ config.category|truncate(15) }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-500">{{ config.query_count }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-500">
                                            <span class="{% if config.hallucination_rate <= 5 %}text-green-600{% elif config.hallucination_rate <= 15 %}text-yellow-600{% else %}text-red-600{% endif %}">
                                                {{ "%.1f"|format(config.hallucination_rate) }}%
                                            </span>
                                        </td>
                                        <td class="px-4 py-2 text-sm text-gray-500">{{ "%.2f"|format(config.avg_processing_time) }}s</td>
                                        <td class="px-4 py-2 text-sm text-gray-500">
                                            {% set performance_score = (100 - config.hallucination_rate) * 0.7 + (10 / (config.avg_processing_time + 1)) * 0.3 %}
                                            <span class="px-2 py-1 text-xs rounded-full
                                                {% if performance_score >= 80 %}bg-green-100 text-green-800
                                                {% elif performance_score >= 60 %}bg-yellow-100 text-yellow-800
                                                {% else %}bg-red-100 text-red-800{% endif %}">
                                                {{ "%.1f"|format(performance_score) }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- Category Performance Breakdown -->
                {% if model_performance_data.category_performance %}
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-700 mb-3">Performance by Category</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {% for category, configs in model_performance_data.category_performance.items() %}
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h5 class="font-semibold text-gray-800 mb-2">{{ category|title }}</h5>
                                {% if configs %}
                                    {% set best_config = configs|sort(attribute='hallucination_rate')|first %}
                                    <div class="text-sm text-gray-600">
                                        <div>Best Model: <span class="font-medium">{{ best_config.model_name|truncate(15) }}</span></div>
                                        <div>Mode: <span class="font-medium">{{ best_config.anti_hallucination_mode|title }}</span></div>
                                        <div>Hallucination Rate: <span class="font-medium text-green-600">{{ "%.1f"|format(best_config.hallucination_rate) }}%</span></div>
                                        <div>Avg Time: <span class="font-medium">{{ "%.2f"|format(best_config.avg_processing_time) }}s</span></div>
                                    </div>
                                {% else %}
                                    <div class="text-sm text-gray-500">No data available</div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Recommendations -->
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h4 class="text-md font-semibold text-blue-800 mb-2">
                        <i class="fas fa-lightbulb mr-1"></i>
                        Configuration Recommendations
                    </h4>
                    <div class="text-sm text-blue-700">
                        {% if model_performance_data.model_comparisons %}
                            {% set best_overall = model_performance_data.model_comparisons|sort(attribute='hallucination_rate')|first %}
                            <div class="mb-2">
                                <strong>Best Overall Configuration:</strong> {{ best_overall.model_name }} with {{ best_overall.anti_hallucination_mode }} mode
                                ({{ "%.1f"|format(best_overall.hallucination_rate) }}% hallucination rate, {{ "%.2f"|format(best_overall.avg_processing_time) }}s avg time)
                            </div>

                            {% if model_performance_data.anti_hallucination_effectiveness %}
                                {% set best_mode = model_performance_data.anti_hallucination_effectiveness|sort(attribute='hallucination_rate')|first %}
                                <div class="mb-2">
                                    <strong>Most Effective Anti-Hallucination Mode:</strong> {{ best_mode.mode|title }}
                                    ({{ "%.1f"|format(best_mode.hallucination_rate) }}% hallucination rate across {{ best_mode.total_queries }} queries)
                                </div>
                            {% endif %}

                            <div class="text-xs mt-2 text-blue-600">
                                💡 Use this data to optimize your model configurations for better performance and accuracy.
                            </div>
                        {% else %}
                            <div>No performance data available yet. Start using the chat with different model configurations to generate analysis data.</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Recent Analytics Table -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Queries</h3>
                {% if analytics %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User / Device</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processing Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sources</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for entry in analytics[:10] %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.timestamp }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {% if entry.client_name %}
                                                <div>{{ entry.client_name }}</div>
                                            {% else %}
                                                <div>Anonymous</div>
                                            {% endif %}
                                            {% if entry.device_fingerprint %}
                                                <div class="text-xs text-gray-500 mt-1">
                                                    <span class="px-1.5 py-0.5 bg-blue-50 text-blue-700 rounded">ID: {{ entry.device_fingerprint[:8] }}...</span>
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.category }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {% if entry.processing_time is not none %}
                                                {{ "%.2f"|format(entry.processing_time) }}s
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.source_count }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.model_name }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    No analytics data available. Start using the chat to generate analytics.
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Chart data from server
        const processingTimeData = {{ processing_time_data|tojson }};
        const queryVolumeData = {{ query_volume_data|tojson }};
        const locationData = {{ location_data|tojson }};
        const modelPerformanceData = {{ model_performance_data|tojson }};

        // Debug output with timestamp
        console.log("Analytics page loaded - version 2.1 - " + new Date().toISOString());
        console.log("Location data received:", locationData);

        // Force clear any cached errors
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            console.error('JavaScript Error:', {
                message: msg,
                source: url,
                line: lineNo,
                column: columnNo,
                error: error
            });
            return false;
        };

        // Initialize GeoConfig with server-provided values
        GeoConfig.init({
            devLocation: {{ dev_location|tojson }}
        });

        // Initialize charts when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme using utilities.js
            DMSUtils.initDarkMode();

            // Add theme toggle button event listener
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                      document.documentElement.classList.contains('dark');
                    DMSUtils.toggleDarkMode(!isDarkMode);

                    // Update charts with new theme
                    updateChartsForTheme(!isDarkMode);

                    // Update map tiles for dark/light mode
                    updateMapForTheme(!isDarkMode);
                });
            }

            // Set initial theme icon based on current theme
            const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                              document.documentElement.classList.contains('dark');
            // Processing Time Chart
            const processingTimeCtx = document.getElementById('processingTimeChart').getContext('2d');
            new Chart(processingTimeCtx, {
                type: 'line',
                data: {
                    labels: processingTimeData.labels,
                    datasets: [{
                        label: 'Processing Time (seconds)',
                        data: processingTimeData.values,
                        borderColor: 'rgb(79, 70, 229)',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Seconds'
                            }
                        }
                    }
                }
            });

            // Query Volume Chart
            const queryVolumeCtx = document.getElementById('queryVolumeChart').getContext('2d');
            new Chart(queryVolumeCtx, {
                type: 'bar',
                data: {
                    labels: queryVolumeData.labels,
                    datasets: [{
                        label: 'Number of Queries',
                        data: queryVolumeData.values,
                        backgroundColor: 'rgba(16, 185, 129, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    }
                }
            });

            // Model Performance Charts
            if (modelPerformanceData && document.getElementById('hallucinationModeChart')) {
                // Anti-Hallucination Mode Effectiveness Chart
                const hallucinationModeCtx = document.getElementById('hallucinationModeChart').getContext('2d');

                const modeLabels = [];
                const modeHallucinationRates = [];
                const modeQueryCounts = [];

                if (modelPerformanceData.anti_hallucination_effectiveness) {
                    modelPerformanceData.anti_hallucination_effectiveness.forEach(mode => {
                        // Add null check for mode.mode before calling charAt
                        const modeText = mode.mode && typeof mode.mode === 'string' ? mode.mode : 'unknown';
                        modeLabels.push(modeText.charAt(0).toUpperCase() + modeText.slice(1));
                        modeHallucinationRates.push(mode.hallucination_rate || 0);
                        modeQueryCounts.push(mode.total_queries || 0);
                    });
                }

                new Chart(hallucinationModeCtx, {
                    type: 'bar',
                    data: {
                        labels: modeLabels,
                        datasets: [{
                            label: 'Hallucination Rate (%)',
                            data: modeHallucinationRates,
                            backgroundColor: [
                                'rgba(239, 68, 68, 0.7)',   // Red for strict
                                'rgba(245, 158, 11, 0.7)',  // Yellow for balanced
                                'rgba(34, 197, 94, 0.7)'    // Green for off
                            ],
                            borderColor: [
                                'rgba(239, 68, 68, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(34, 197, 94, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Hallucination Rate (%)'
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    afterLabel: function(context) {
                                        const index = context.dataIndex;
                                        return `Total Queries: ${modeQueryCounts[index]}`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            if (modelPerformanceData && document.getElementById('modelPerformanceChart')) {
                // Model Processing Time Comparison Chart
                const modelPerformanceCtx = document.getElementById('modelPerformanceChart').getContext('2d');

                const modelLabels = [];
                const modelProcessingTimes = [];
                const modelHallucinationRates = [];

                if (modelPerformanceData.model_comparisons) {
                    // Group by model and calculate averages
                    const modelGroups = {};
                    modelPerformanceData.model_comparisons.forEach(config => {
                        const modelName = config.model_name.split(':')[0]; // Simplify model name
                        if (!modelGroups[modelName]) {
                            modelGroups[modelName] = {
                                totalTime: 0,
                                totalHallucinations: 0,
                                totalQueries: 0,
                                count: 0
                            };
                        }
                        modelGroups[modelName].totalTime += config.avg_processing_time || 0;
                        modelGroups[modelName].totalHallucinations += config.hallucination_rate || 0;
                        modelGroups[modelName].totalQueries += config.query_count || 0;
                        modelGroups[modelName].count += 1;
                    });

                    Object.entries(modelGroups).forEach(([modelName, data]) => {
                        modelLabels.push(modelName);
                        modelProcessingTimes.push((data.totalTime / data.count).toFixed(2));
                        modelHallucinationRates.push((data.totalHallucinations / data.count).toFixed(1));
                    });
                }

                new Chart(modelPerformanceCtx, {
                    type: 'scatter',
                    data: {
                        datasets: [{
                            label: 'Model Performance',
                            data: modelLabels.map((label, index) => ({
                                x: parseFloat(modelProcessingTimes[index]),
                                y: parseFloat(modelHallucinationRates[index]),
                                label: label
                            })),
                            backgroundColor: 'rgba(99, 102, 241, 0.7)',
                            borderColor: 'rgba(99, 102, 241, 1)',
                            borderWidth: 2,
                            pointRadius: 8,
                            pointHoverRadius: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Average Processing Time (seconds)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Average Hallucination Rate (%)'
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        return context[0].raw.label;
                                    },
                                    label: function(context) {
                                        return [
                                            `Processing Time: ${context.parsed.x}s`,
                                            `Hallucination Rate: ${context.parsed.y}%`
                                        ];
                                    }
                                }
                            },
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Initialize the map with comprehensive error handling
            if (document.getElementById('userLocationMap')) {
                console.log("Initializing map...");

                try {
                    // Validate location data first
                    if (locationData) {
                        console.log("Location data validation:");
                        locationData.forEach((location, index) => {
                            if (!location.country || typeof location.country !== 'string') {
                                console.warn(`Invalid country data at index ${index}:`, location);
                            }
                        });
                    }
                    const map = L.map('userLocationMap').setView([0, 0], 2);
                    console.log("Map created successfully");

                    // Store map reference globally for theme updates
                    window.locationMap = map;

                // Add the appropriate map tiles based on current theme
                const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                  document.documentElement.classList.contains('dark');

                    // Add the appropriate tile layer based on theme
                    if (isDarkMode) {
                        // Dark theme map tiles
                        console.log("Loading dark theme tiles...");
                        window.darkTileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a> | Geocoding by <a href="https://nominatim.org/">Nominatim</a> | GeoIP data by <a href="https://www.maxmind.com/">MaxMind</a>',
                            subdomains: 'abcd',
                            maxZoom: 19
                        }).addTo(map);
                    } else {
                        // Light theme map tiles (default)
                        console.log("Loading light theme tiles...");
                        window.lightTileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors | Geocoding by <a href="https://nominatim.org/">Nominatim</a> | GeoIP data by <a href="https://www.maxmind.com/">MaxMind</a>',
                            maxZoom: 18
                        }).addTo(map);
                    }

                // Make attribution control more visible
                map.attributionControl.setPosition('bottomright');
                map.attributionControl.addAttribution('<a href="https://operations.osmfoundation.org/policies/nominatim/">Nominatim Usage Policy</a>');

                // Create a marker cluster group for normal markers
                const markerClusterGroup = L.markerClusterGroup({
                    maxClusterRadius: 30,
                    spiderfyOnMaxZoom: true,
                    showCoverageOnHover: true,
                    zoomToBoundsOnClick: true,
                    disableClusteringAtZoom: 16
                });

                // Array for all markers (including local markers that won't be clustered)
                const markers = [];
                const locationCounts = {};
                let hasLocalEntries = false;

                // Debug the location data
                console.log("Location data:", locationData);
                console.log("Location data length:", locationData ? locationData.length : 0);
                console.log("GeoConfig:", GeoConfig);
                console.log("Dev location:", GeoConfig.getDevLocation());

                // Group locations by coordinates
                if (locationData && locationData.length > 0) {
                    // Extract timestamps for each location
                    const locationTimestamps = {};

                    locationData.forEach(location => {
                        // Check if this is a local entry (latitude and longitude are both 0 or null)
                        const isLocalEntry = (location.latitude === 0 && location.longitude === 0) ||
                                           (location.city === 'Local' || location.country === 'Local');

                        if (isLocalEntry) {
                            hasLocalEntries = true;
                            // We'll handle local entries separately
                            if (!locationCounts['local']) {
                                locationCounts['local'] = {
                                    count: 0,
                                    city: location.city || 'Local',
                                    region: location.region || 'Development',
                                    country: location.country || 'Local',
                                    isLocal: true,
                                    timestamps: [],
                                    client_names: new Set()
                                };
                            }
                            locationCounts['local'].count++;

                            // Store timestamp and client name if available
                            if (location.timestamp) {
                                locationCounts['local'].timestamps.push(location.timestamp);
                            }
                            if (location.client_name) {
                                locationCounts['local'].client_names.add(location.client_name);
                            }
                        }
                        // Handle normal entries with valid coordinates
                        else if (location.latitude !== null && location.longitude !== null) {
                            const key = `${location.latitude},${location.longitude}`;

                            if (!locationCounts[key]) {
                                locationCounts[key] = {
                                    count: 0,
                                    city: location.city || 'Unknown',
                                    region: location.region || 'Unknown',
                                    country: location.country || 'Unknown',
                                    isLocal: false,
                                    latitude: location.latitude,
                                    longitude: location.longitude,
                                    timestamps: [],
                                    client_names: new Set()
                                };
                            }

                            locationCounts[key].count++;

                            // Store timestamp and client name if available
                            if (location.timestamp) {
                                locationCounts[key].timestamps.push(location.timestamp);
                            }
                            if (location.client_name) {
                                locationCounts[key].client_names.add(location.client_name);
                            }
                        }
                    });

                    // Add markers to the map
                    for (const [coords, data] of Object.entries(locationCounts)) {
                        let marker;

                        if (data.isLocal) {
                            // For local entries, use a special marker at the development location (Los Baños)
                            // Get coordinates from the GeoConfig module
                            const devCoords = GeoConfig.getDevCoordinates();
                            const localLat = devCoords.latitude;
                            const localLng = devCoords.longitude;

                            // Create a custom icon for local entries - more visually distinct
                            const localIcon = L.divIcon({
                                html: `<div style="background-color: #4B5563; width: 24px; height: 24px; border-radius: 50%; border: 3px solid white; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">${data.count}</div>`,
                                className: 'custom-marker local-marker',
                                iconSize: [24, 24],
                                iconAnchor: [12, 12],
                                popupAnchor: [0, -12]
                            });

                            marker = L.marker([localLat, localLng], {icon: localIcon}).addTo(map);
                        } else {
                            // For normal entries, use custom markers at their actual coordinates
                            const lat = data.latitude || parseFloat(coords.split(',')[0]);
                            const lng = data.longitude || parseFloat(coords.split(',')[1]);

                            // Use different marker colors based on country
                            let markerColor = 'blue'; // Default color

                            // Simple hash function to generate consistent colors for countries
                            const getColorForCountry = (country) => {
                                const colors = ['#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#3498db', '#1abc9c', '#d35400'];

                                // Check for null, undefined, or empty string
                                if (!country || country === 'Unknown' || typeof country !== 'string') {
                                    return '#3498db'; // Default blue
                                }

                                // Simple hash to pick a color
                                let hash = 0;
                                for (let i = 0; i < country.length; i++) {
                                    hash = country.charCodeAt(i) + ((hash << 5) - hash);
                                }
                                return colors[Math.abs(hash) % colors.length];
                            };

                            // Ensure country data is valid before getting color
                            markerColor = getColorForCountry(data.country || 'Unknown');

                            // Create a custom colored marker icon
                            const markerIcon = L.divIcon({
                                html: `<div style="background-color: ${markerColor}; width: 22px; height: 22px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">${data.count}</div>`,
                                className: 'custom-marker',
                                iconSize: [22, 22],
                                iconAnchor: [11, 11],
                                popupAnchor: [0, -11]
                            });

                            marker = L.marker([lat, lng], {icon: markerIcon});
                            markerClusterGroup.addLayer(marker);
                        }

                        // Get the most recent timestamp
                        let lastVisitTime = "Unknown";
                        if (data.timestamps && data.timestamps.length > 0) {
                            // Sort timestamps in descending order
                            const sortedTimestamps = [...data.timestamps].sort().reverse();
                            lastVisitTime = sortedTimestamps[0];
                        }

                        // Format client names
                        let clientNamesStr = "Anonymous";
                        if (data.client_names && data.client_names.size > 0) {
                            clientNamesStr = Array.from(data.client_names).join(", ");
                        }

                        // Create enhanced popup content with more visitor details
                        let locationName = data.isLocal ? GeoConfig.getDevLocationName() : data;
                        const popupContent = `
                            <div class="text-sm">
                                <div class="font-semibold text-lg">${data.isLocal ? locationName.city : data.city}, ${data.isLocal ? locationName.region : data.region}</div>
                                <div class="mb-2">${data.isLocal ? locationName.country : data.country}</div>

                                <div class="grid grid-cols-2 gap-1 mt-2">
                                    <div class="font-semibold">Visitor count:</div>
                                    <div>${data.count}</div>

                                    <div class="font-semibold">Last visit:</div>
                                    <div>${lastVisitTime}</div>

                                    <div class="font-semibold">Client name(s):</div>
                                    <div>${clientNamesStr}</div>
                                </div>

                                ${data.isLocal ? '<div class="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-700">This represents local development activity at Los Baños, Laguna, Philippines.</div>' : ''}

                                <div class="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
                                    <div>GeoIP data by <a href="https://www.maxmind.com/" target="_blank" rel="noopener noreferrer">MaxMind</a></div>
                                    <div>Map data &copy; <a href="https://www.openstreetmap.org/copyright" target="_blank" rel="noopener noreferrer">OpenStreetMap</a> contributors</div>
                                </div>
                            </div>
                        `;

                        // Add popup to marker
                        marker.bindPopup(popupContent, {
                            maxWidth: 300,
                            minWidth: 200,
                            className: 'visitor-popup'
                        });

                        markers.push(marker);
                    }

                    // Add the marker cluster group to the map
                    map.addLayer(markerClusterGroup);

                    // If we have markers, fit the map to show all of them
                    if (markers.length > 0) {
                        // Create a feature group with all markers (including clustered ones)
                        const allMarkers = [...markers];
                        markerClusterGroup.getLayers().forEach(layer => {
                            if (!markers.includes(layer)) {
                                allMarkers.push(layer);
                            }
                        });

                        const group = L.featureGroup(allMarkers);
                        map.fitBounds(group.getBounds().pad(0.1));

                        // Add an improved legend to the map
                        const legend = L.control({position: 'bottomleft'});
                        legend.onAdd = function(map) {
                            const div = L.DomUtil.create('div', 'info legend');
                            div.style.backgroundColor = 'white';
                            div.style.padding = '10px 12px';
                            div.style.border = '1px solid #ccc';
                            div.style.borderRadius = '6px';
                            div.style.lineHeight = '20px';
                            div.style.color = '#333';
                            div.style.fontFamily = 'Arial, sans-serif';
                            div.style.fontSize = '12px';
                            div.style.boxShadow = '0 1px 5px rgba(0,0,0,0.2)';
                            div.style.maxWidth = '220px';

                            div.innerHTML = '<h4 style="margin: 0 0 8px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 5px;">Map Legend</h4>';

                            // Add legend items
                            if (hasLocalEntries) {
                                div.innerHTML += `
                                    <div style="margin-bottom: 6px; display: flex; align-items: center;">
                                        <div style="background-color: #4B5563; width: 18px; height: 18px; border-radius: 50%; border: 2px solid white; margin-right: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.2);"></div>
                                        <span>Los Baños, Philippines (Local Development)</span>
                                    </div>`;
                            }

                            // Add marker cluster legend item
                            div.innerHTML += `
                                <div style="margin-bottom: 6px; display: flex; align-items: center;">
                                    <div style="background-color: rgba(241, 128, 23, 0.8); width: 18px; height: 18px; border-radius: 50%; border: 2px solid rgba(241, 211, 87, 0.8); margin-right: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.2);"></div>
                                    <span>Clustered Markers (click to expand)</span>
                                </div>`;

                            // Get unique countries from the location data
                            const countries = new Set();
                            Object.values(locationCounts).forEach(data => {
                                // Additional safety checks for country data
                                if (!data.isLocal &&
                                    data.country &&
                                    data.country !== 'Unknown' &&
                                    typeof data.country === 'string' &&
                                    data.country.trim() !== '') {
                                    countries.add(data.country);
                                }
                            });

                            // Add legend entries for each country
                            const getColorForCountry = (country) => {
                                const colors = ['#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#3498db', '#1abc9c', '#d35400'];

                                // Check for null, undefined, or empty string
                                if (!country || country === 'Unknown' || typeof country !== 'string') {
                                    return '#3498db'; // Default blue
                                }

                                // Simple hash to pick a color
                                let hash = 0;
                                for (let i = 0; i < country.length; i++) {
                                    hash = country.charCodeAt(i) + ((hash << 5) - hash);
                                }
                                return colors[Math.abs(hash) % colors.length];
                            };

                            if (countries.size > 0) {
                                div.innerHTML += '<div style="margin-top: 8px; margin-bottom: 5px; font-weight: bold;">Countries:</div>';

                                countries.forEach(country => {
                                    // Additional safety check
                                    if (!country || typeof country !== 'string') {
                                        console.warn('Invalid country data:', country);
                                        return;
                                    }

                                    const color = getColorForCountry(country);
                                    div.innerHTML += `
                                        <div style="margin-bottom: 4px; display: flex; align-items: center;">
                                            <div style="background-color: ${color}; width: 16px; height: 16px; border-radius: 50%; border: 2px solid white; margin-right: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.2);"></div>
                                            <span>${country}</span>
                                        </div>`;
                                });
                            }

                            // Add a default entry for unknown locations
                            if (!countries.size) {
                                div.innerHTML += `
                                    <div style="margin-bottom: 4px; display: flex; align-items: center;">
                                        <div style="background-color: #3498db; width: 16px; height: 16px; border-radius: 50%; border: 2px solid white; margin-right: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.2);"></div>
                                        <span>User Locations</span>
                                    </div>`;
                            }

                            // Add attribution note
                            div.innerHTML += `
                                <div style="margin-top: 10px; font-size: 10px; color: #666; border-top: 1px solid #eee; padding-top: 5px;">
                                    Map data &copy; <a href="https://www.openstreetmap.org/copyright" target="_blank" rel="noopener noreferrer">OpenStreetMap</a> contributors<br>
                                    Geocoding by <a href="https://nominatim.org/" target="_blank" rel="noopener noreferrer">Nominatim</a><br>
                                    GeoIP data by <a href="https://www.maxmind.com/" target="_blank" rel="noopener noreferrer">MaxMind</a>
                                </div>`;

                            return div;
                        };
                        legend.addTo(map);
                    }
                } else {
                    // Display a message if no location data is available
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1000] shadow-md max-w-md';
                    noDataMessage.innerHTML = `
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    No location data available yet. Location data will appear as users interact with the system.
                                </p>
                                <p class="text-xs text-gray-600 mt-1">
                                    Note: If you see data in the "Top Countries" or "Top Cities" tables but no markers on the map,
                                    try refreshing the page. The map should display markers for all locations including "Local" development entries.
                                </p>
                                <div class="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
                                    <div>Map powered by <a href="https://www.openstreetmap.org/copyright" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">OpenStreetMap</a> and <a href="https://nominatim.org/" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">Nominatim</a></div>
                                    <div>GeoIP data by <a href="https://www.maxmind.com/" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">MaxMind</a></div>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('userLocationMap').appendChild(noDataMessage);

                    // Add proper attribution even when no data is available
                    const attribution = L.control.attribution({position: 'bottomright'});
                    attribution.addTo(map);
                    attribution.setPrefix('');
                    attribution.addAttribution('&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors | <a href="https://operations.osmfoundation.org/policies/nominatim/">Nominatim Usage Policy</a>');
                }

                } catch (error) {
                    console.error("Error initializing map:", error);

                    // Display error message in the map container
                    const mapContainer = document.getElementById('userLocationMap');
                    if (mapContainer) {
                        mapContainer.innerHTML = `
                            <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1000] shadow-md max-w-md">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700">
                                            Failed to load the map. This might be due to network connectivity issues or blocked external resources.
                                        </p>
                                        <p class="text-xs text-gray-600 mt-1">
                                            Please check your internet connection and try refreshing the page. If the problem persists, contact your administrator.
                                        </p>
                                        <div class="mt-2 text-xs text-gray-500">
                                            Error: ${error.message}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }
            }

            // Initialize charts with current theme
            updateChartsForTheme(isDarkMode);
        });

        // Function to update chart colors based on theme
        function updateChartsForTheme(isDark) {
            // Get all chart instances
            const charts = Object.values(Chart.instances);

            // Update each chart with theme-appropriate colors
            charts.forEach(chart => {
                if (chart.config.type === 'line') {
                    // Update line chart colors
                    chart.data.datasets.forEach(dataset => {
                        if (isDark) {
                            dataset.borderColor = 'rgb(96, 165, 250)';  // Lighter blue in dark mode
                            dataset.backgroundColor = 'rgba(96, 165, 250, 0.2)';
                        } else {
                            dataset.borderColor = 'rgb(79, 70, 229)';  // Original color
                            dataset.backgroundColor = 'rgba(79, 70, 229, 0.1)';
                        }
                    });
                } else if (chart.config.type === 'bar') {
                    // Update bar chart colors
                    chart.data.datasets.forEach((dataset, datasetIndex) => {
                        // Check if this is the hallucination mode chart (has specific colors)
                        if (dataset.label === 'Hallucination Rate (%)') {
                            // Keep the specific colors for hallucination modes
                            if (isDark) {
                                dataset.backgroundColor = [
                                    'rgba(248, 113, 113, 0.8)',   // Lighter red for strict
                                    'rgba(251, 191, 36, 0.8)',   // Lighter yellow for balanced
                                    'rgba(52, 211, 153, 0.8)'    // Lighter green for off
                                ];
                                dataset.borderColor = [
                                    'rgba(248, 113, 113, 1)',
                                    'rgba(251, 191, 36, 1)',
                                    'rgba(52, 211, 153, 1)'
                                ];
                            } else {
                                dataset.backgroundColor = [
                                    'rgba(239, 68, 68, 0.7)',   // Red for strict
                                    'rgba(245, 158, 11, 0.7)',  // Yellow for balanced
                                    'rgba(34, 197, 94, 0.7)'    // Green for off
                                ];
                                dataset.borderColor = [
                                    'rgba(239, 68, 68, 1)',
                                    'rgba(245, 158, 11, 1)',
                                    'rgba(34, 197, 94, 1)'
                                ];
                            }
                        } else {
                            // Default bar chart colors
                            if (isDark) {
                                dataset.backgroundColor = 'rgba(52, 211, 153, 0.8)';  // Lighter green in dark mode
                            } else {
                                dataset.backgroundColor = 'rgba(16, 185, 129, 0.7)';  // Original color
                            }
                        }
                    });
                } else if (chart.config.type === 'scatter') {
                    // Update scatter chart colors for model performance
                    chart.data.datasets.forEach(dataset => {
                        if (isDark) {
                            dataset.backgroundColor = 'rgba(129, 140, 248, 0.8)';  // Lighter indigo in dark mode
                            dataset.borderColor = 'rgba(129, 140, 248, 1)';
                        } else {
                            dataset.backgroundColor = 'rgba(99, 102, 241, 0.7)';   // Original indigo
                            dataset.borderColor = 'rgba(99, 102, 241, 1)';
                        }
                    });
                } else if (chart.config.type === 'doughnut') {
                    // Update doughnut chart colors
                    if (isDark) {
                        chart.data.datasets[0].backgroundColor = [
                            'rgba(96, 165, 250, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(252, 211, 77, 0.8)',
                            'rgba(52, 211, 153, 0.8)',
                            'rgba(167, 139, 250, 0.8)',
                            'rgba(251, 146, 60, 0.8)',
                            'rgba(156, 163, 175, 0.8)'
                        ];
                    } else {
                        chart.data.datasets[0].backgroundColor = [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)'
                        ];
                    }
                }

                // Update chart options for better visibility in dark mode
                if (chart.options.scales && chart.options.scales.y) {
                    chart.options.scales.y.grid = {
                        color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    };

                    chart.options.scales.y.ticks = {
                        color: isDark ? '#e5e7eb' : '#4b5563'
                    };

                    if (chart.options.scales.y.title) {
                        chart.options.scales.y.title.color = isDark ? '#e5e7eb' : '#4b5563';
                    }
                }

                if (chart.options.scales && chart.options.scales.x) {
                    chart.options.scales.x.grid = {
                        color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    };

                    chart.options.scales.x.ticks = {
                        color: isDark ? '#e5e7eb' : '#4b5563'
                    };

                    if (chart.options.scales.x.title) {
                        chart.options.scales.x.title.color = isDark ? '#e5e7eb' : '#4b5563';
                    }
                }

                // Update legend colors
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.labels = {
                        color: isDark ? '#e5e7eb' : '#4b5563'
                    };
                }

                // Update the chart
                chart.update();
            });
        }

        // Function to update map tiles based on theme
        function updateMapForTheme(isDark) {
            // Check if map exists
            if (!window.locationMap) return;

            const map = window.locationMap;

            // Remove existing tile layers
            if (window.darkTileLayer) {
                map.removeLayer(window.darkTileLayer);
            }

            if (window.lightTileLayer) {
                map.removeLayer(window.lightTileLayer);
            }

            // Add appropriate tile layer based on theme
            if (isDark) {
                // Dark theme map tiles
                window.darkTileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a> | Geocoding by <a href="https://nominatim.org/">Nominatim</a> | GeoIP data by <a href="https://www.maxmind.com/">MaxMind</a>',
                    subdomains: 'abcd',
                    maxZoom: 19
                }).addTo(map);
            } else {
                // Light theme map tiles
                window.lightTileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors | Geocoding by <a href="https://nominatim.org/">Nominatim</a> | GeoIP data by <a href="https://www.maxmind.com/">MaxMind</a>',
                    maxZoom: 18
                }).addTo(map);
            }

            // Update legend styles
            const legend = document.querySelector('.info.legend');
            if (legend) {
                if (isDark) {
                    legend.style.backgroundColor = '#1f2937';
                    legend.style.color = '#e5e7eb';
                    legend.style.borderColor = '#4b5563';
                } else {
                    legend.style.backgroundColor = 'white';
                    legend.style.color = '#333';
                    legend.style.borderColor = '#ccc';
                }

                // Update legend links
                const links = legend.querySelectorAll('a');
                links.forEach(link => {
                    link.style.color = isDark ? '#60a5fa' : '#2563eb';
                });
            }

            // Update popup styles
            document.querySelectorAll('.leaflet-popup-content-wrapper').forEach(popup => {
                if (isDark) {
                    popup.style.backgroundColor = '#1f2937';
                    popup.style.color = '#f3f4f6';
                } else {
                    popup.style.backgroundColor = '#ffffff';
                    popup.style.color = '#1f2937';
                }
            });

            // Update no-data message if it exists
            const noDataMessage = document.querySelector('.bg-yellow-50');
            if (noDataMessage && noDataMessage.closest('#userLocationMap')) {
                if (isDark) {
                    noDataMessage.classList.remove('bg-yellow-50');
                    noDataMessage.classList.add('bg-yellow-900');

                    const textElements = noDataMessage.querySelectorAll('.text-yellow-700, .text-gray-600, .text-gray-500');
                    textElements.forEach(el => {
                        if (el.classList.contains('text-yellow-700')) {
                            el.classList.remove('text-yellow-700');
                            el.classList.add('text-yellow-200');
                        } else if (el.classList.contains('text-gray-600')) {
                            el.classList.remove('text-gray-600');
                            el.classList.add('text-gray-400');
                        } else if (el.classList.contains('text-gray-500')) {
                            el.classList.remove('text-gray-500');
                            el.classList.add('text-gray-400');
                        }
                    });
                } else {
                    noDataMessage.classList.remove('bg-yellow-900');
                    noDataMessage.classList.add('bg-yellow-50');

                    const textElements = noDataMessage.querySelectorAll('.text-yellow-200, .text-gray-400');
                    textElements.forEach(el => {
                        if (el.classList.contains('text-yellow-200')) {
                            el.classList.remove('text-yellow-200');
                            el.classList.add('text-yellow-700');
                        } else if (el.classList.contains('text-gray-400')) {
                            if (el.closest('.border-t')) {
                                el.classList.remove('text-gray-400');
                                el.classList.add('text-gray-500');
                            } else {
                                el.classList.remove('text-gray-400');
                                el.classList.add('text-gray-600');
                            }
                        }
                    });
                }
            }
        }
    </script>
{% endblock %}
