# Module Documentation

## Overview

This document provides detailed documentation for all Python modules, their functions, classes, and API endpoints in the Document Management System.

## Core Application Modules

### 1. `app.py` - Main Application Server

**Primary Flask Application**
- **Purpose**: Main application entry point and route definitions
- **Dependencies**: Flask, user management, AI models, database utilities

**Key Routes:**

#### Public Routes
```python
@app.route('/')
def index():
    """Main chat interface for end users"""
    # Returns: Template with available categories

@app.route('/query', methods=['POST'])
def query():
    """Process user queries and return AI responses"""
    # Input: category, question, anti_hallucination_mode
    # Returns: JSON response with answer, sources, images
```

#### Admin Routes
```python
@app.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    """Administrative dashboard interface"""
    # Returns: Admin dashboard template

@app.route('/admin/upload', methods=['GET', 'POST'])
@admin_required
def upload_file():
    """File upload interface and processing"""
    # Handles: PDF uploads, URL scraping, category management

@app.route('/admin/analytics')
@admin_required
def analytics_dashboard():
    """System analytics and usage statistics"""
    # Returns: Analytics dashboard with charts and metrics
```

#### API Endpoints
```python
@app.route('/api/models', methods=['GET', 'POST'])
def manage_models():
    """AI model configuration management"""
    # GET: Returns current model settings
    # POST: Updates model configuration

@app.route('/api/query_config', methods=['GET', 'POST'])
def query_config():
    """Query processing configuration"""
    # Manages: retrieval settings, thresholds, display limits
```

**Configuration Management:**
- Environment variable handling
- Default model loading
- Temporary directory management
- Database initialization

### 2. `query.py` - AI Query Processing

**Core Query Engine**
- **Purpose**: Process user queries using AI models and vector search
- **Dependencies**: LangChain, Ollama, ChromaDB, vision processing

**Main Function:**
```python
def query_category(category: str, question: str, 
                  anti_hallucination_mode: str = 'strict',
                  client_name: str = None, 
                  session_id: str = None,
                  device_fingerprint: str = None) -> dict:
    """
    Process a user query within a specific category
    
    Args:
        category: Document category to search
        question: User's natural language question
        anti_hallucination_mode: Detection sensitivity level
        client_name: User identification
        session_id: Session tracking ID
        device_fingerprint: Device identification
    
    Returns:
        dict: Complete response with answer, sources, images, metadata
    """
```

**Key Components:**

#### Document Retrieval
```python
def filter_relevant_documents(docs, question, threshold=0.15):
    """Filter documents by relevance threshold"""
    # Uses embedding similarity scoring
    # Returns scored and filtered document list

def get_document_thumbnails(docs, category):
    """Extract representative images from documents"""
    # Hierarchical image selection
    # Returns formatted thumbnail data
```

#### Response Generation
```python
def generate_followup_questions(category, question, answer):
    """Generate contextual follow-up questions"""
    # AI-powered question generation
    # Category-specific question templates

def detect_hallucination(answer, sources, mode='strict'):
    """Detect potential hallucinations in AI responses"""
    # Configurable detection thresholds
    # Source verification and confidence scoring
```

### 3. `embed.py` & `db_embed.py` - Document Processing

**Document Embedding Pipeline**
- **Purpose**: Process and embed documents into vector database
- **Dependencies**: PyMuPDF, BeautifulSoup, ChromaDB, vision models

**PDF Processing:**
```python
def embed_file(file, category, source_url=None, 
               use_vision=None, filter_sensitivity=None, 
               max_images=None) -> dict:
    """
    Process and embed a PDF file
    
    Args:
        file: Uploaded file object
        category: Target category
        source_url: Original URL source
        use_vision: Enable vision model analysis
        filter_sensitivity: Image filtering level
        max_images: Maximum images to process
    
    Returns:
        dict: Processing results with chunk count, images, tables
    """
```

**Web Scraping:**
```python
def scrape_url(url, depth=0, max_pages=10) -> dict:
    """
    Scrape web content with depth-based crawling
    
    Args:
        url: Target URL to scrape
        depth: Crawling depth level
        max_pages: Maximum pages to process
    
    Returns:
        dict: Scraped content with text, images, links
    """
```

**Database-First Processing:**
```python
def embed_file_db_first(file, category, source_url=None) -> dict:
    """Database-first PDF processing with content caching"""

def scrape_url_db_first(url, category, depth=0) -> dict:
    """Database-first URL scraping with content storage"""
```

### 4. `user_management.py` - User System

**Authentication and Authorization**
- **Purpose**: Complete user management system with RBAC
- **Dependencies**: Flask-Login, bcrypt, Flask-WTF, Flask-Limiter

**User Class:**
```python
class User(UserMixin):
    """User model for Flask-Login integration"""
    
    def __init__(self, user_id, username, email, role, account_status, ...):
        """Initialize user with comprehensive attributes"""
    
    @property
    def is_active(self):
        """Check if user account is active"""
    
    @property
    def password_expired(self):
        """Check if password has expired"""
```

**Authentication Functions:**
```python
def authenticate_user(username, password) -> tuple:
    """Authenticate user credentials"""
    # Returns: (User object or None, error message)

def create_user(username, email, password, role, full_name=None) -> tuple:
    """Create new user account"""
    # Includes: password validation, duplicate checking

def update_user_role(user_id, new_role) -> bool:
    """Update user role with automatic group assignment"""
```

**Permission Management:**
```python
def has_permission(user_id, function_name) -> bool:
    """Check if user has specific function permission"""

def get_user_permissions(user_id) -> dict:
    """Get comprehensive user permission set"""

def update_permission_override(user_id, function_name, enabled) -> bool:
    """Update individual permission override"""
```

### 5. `db_utils.py` - Database Operations

**Database Utility Functions**
- **Purpose**: Centralized database operations and management
- **Dependencies**: SQLite3, JSON handling

**Chat History Management:**
```python
def save_chat_history(category, question, answer, sources, 
                     images, pdf_links, metadata=None, 
                     url_images=None, pdf_images=None, 
                     client_name=None, session_id=None, 
                     session_start=None, device_fingerprint=None,
                     document_thumbnails=None) -> int:
    """Save complete chat interaction to database"""
    # Returns: Chat ID for reference

def get_chat_history(limit=100, offset=0) -> list:
    """Retrieve chat history with pagination"""
```

**Analytics Functions:**
```python
def save_analytics(chat_id, session_id, category, client_name,
                  question_length, answer_length, processing_time,
                  source_count, image_count, **kwargs) -> bool:
    """Save comprehensive analytics data"""

def get_analytics(start_date=None, end_date=None) -> dict:
    """Retrieve analytics data with date filtering"""

def get_analytics_summary() -> dict:
    """Get summary statistics for dashboard"""
```

### 6. `vision_processor.py` - Image Analysis

**AI Vision Processing**
- **Purpose**: Image analysis using vision models
- **Dependencies**: Ollama, base64 encoding, requests

**Core Functions:**
```python
def analyze_image(image_path_or_url: str, 
                 prompt: str = "Describe this image in detail.",
                 document_context: str = None,
                 query: str = None) -> dict:
    """
    Analyze image using selected vision model
    
    Args:
        image_path_or_url: Image location
        prompt: Analysis prompt
        document_context: Related document text
        query: User query context
    
    Returns:
        dict: Analysis results with description and metadata
    """

def generate_contextual_caption(image_path_or_url: str,
                               document_context: str = None,
                               user_query: str = None) -> dict:
    """Generate context-aware image captions"""

def filter_images_by_relevance(images: list, 
                              document_context: str,
                              sensitivity: str = 'medium') -> list:
    """Filter images based on relevance to document content"""
```

### 7. `geo_utils.py` & `geoip_analytics.py` - Location Services

**Geolocation and Analytics**
- **Purpose**: Location tracking and geographic analytics
- **Dependencies**: MaxMind GeoLite2, requests

**Location Functions:**
```python
def get_client_ip() -> str:
    """Get client IP address from request"""

def get_location_from_ip(ip_address: str) -> tuple:
    """Get location data from IP address"""
    # Returns: (city, region, country, latitude, longitude)

def get_location_for_analytics() -> tuple:
    """Get complete location data for analytics storage"""
```

**GeoIP Analytics:**
```python
def track_user_location(ip_address: str, 
                       user_agent: str = None,
                       session_id: str = None) -> bool:
    """Track user location for analytics"""

def get_location_analytics(start_date=None, end_date=None) -> dict:
    """Retrieve location-based analytics"""
```

## Configuration Modules

### 8. `config/query_config.py` - Query Configuration

**Query Processing Configuration**
- **Purpose**: Centralized configuration for query processing
- **Features**: Dataclass-based configuration with validation

```python
@dataclass
class QueryConfiguration:
    """Centralized configuration for query processing"""
    
    # Document Retrieval Settings
    retrieval_k: int = 12
    relevance_threshold: float = 0.15
    min_documents: int = 3
    max_documents: int = 8
    
    # Response Display Limits
    max_pdf_images_display: int = 5
    max_url_images_display: int = 5
    max_tables_display: int = 3
    
    # Hallucination Detection
    hallucination_threshold_strict: float = 0.6
    hallucination_threshold_balanced: float = 0.4
    enable_hallucination_detection: bool = True
    
    def validate(self) -> bool:
        """Validate configuration parameters"""
    
    def to_dict(self) -> dict:
        """Convert configuration to dictionary"""
    
    @classmethod
    def from_dict(cls, config_dict: dict):
        """Create configuration from dictionary"""
```

## Database Schema Modules

### 9. `db_schema.py` - Database Schema Management

**Database Schema Initialization**
- **Purpose**: Database schema creation and migration
- **Features**: Version control, automatic migrations

```python
def initialize_database() -> bool:
    """Initialize complete database schema"""
    # Creates all required tables and indexes
    # Handles schema migrations
    # Returns success status

def create_indexes() -> bool:
    """Create optimized database indexes"""

def migrate_schema(from_version: int, to_version: int) -> bool:
    """Perform schema migration between versions"""
```

## Utility Modules

### 10. `utils.py` - General Utilities

**Common Utility Functions**
- **Purpose**: Shared utility functions across the application

```python
def list_categories() -> list:
    """List all available document categories"""

def delete_file(category: str, filename: str) -> bool:
    """Delete file and associated resources"""

def check_duplicate_pdf(file_content: bytes, category: str) -> bool:
    """Check for duplicate PDF uploads"""

def clean_filename(filename: str) -> str:
    """Clean and sanitize filenames"""
```

### 11. `security.py` - Security Utilities

**Security and Validation Functions**
- **Purpose**: Security-related utilities and validation

```python
def validate_password(password: str) -> tuple:
    """Validate password strength"""
    # Returns: (is_valid, error_messages)

def generate_secure_token(length: int = 32) -> str:
    """Generate cryptographically secure tokens"""

def sanitize_input(input_string: str) -> str:
    """Sanitize user input for security"""
```

## API Response Formats

### Standard Response Format
```json
{
    "answer": "AI-generated response text",
    "sources": [
        {
            "document": "document_name.pdf",
            "page": 1,
            "content": "relevant_text_excerpt"
        }
    ],
    "images": ["image_url_1", "image_url_2"],
    "url_images": ["url_image_1"],
    "pdf_images": ["pdf_image_1"],
    "document_thumbnails": [
        {
            "document": "doc_name",
            "image_url": "thumbnail_url",
            "source": "pdf_first_page"
        }
    ],
    "tables": ["table_html_1"],
    "pdf_links": ["link_1", "link_2"],
    "followup_questions": ["question_1", "question_2"],
    "metadata": {
        "hallucination_detected": false,
        "document_count": 5,
        "processing_time": 2.34,
        "model_used": "llama3.1:8b-instruct-q4_K_M"
    },
    "session_id": "unique_session_id",
    "session_start": "2024-01-01T12:00:00"
}
```

This comprehensive module documentation provides developers with detailed information about the system's architecture, enabling efficient development, maintenance, and extension of the Document Management System.
