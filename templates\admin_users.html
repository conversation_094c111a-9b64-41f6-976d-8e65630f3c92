{% extends "admin_base.html" %}

{% block title %}User Management{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">User Management</h1>
            <div class="flex space-x-4">
                <a href="{{ url_for('user.admin_activity_logs') }}" class="text-blue-600 hover:underline">Activity Logs</a>
            </div>
        </div>

            <div class="flex justify-between items-center mb-4">
                <div class="flex">
                    <form action="{{ url_for('user.admin_users') }}" method="GET" class="flex">
                        <input type="text" name="search" value="{{ search }}" placeholder="Search users..." class="px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            Search
                        </button>
                    </form>
                </div>
                <a href="{{ url_for('user.admin_new_user') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                    Add New User
                </a>
            </div>

            <div class="mb-4">
                <div class="flex items-center space-x-2">
                    <select id="bulkAction" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Bulk Actions</option>
                        <option value="status:active">Set Active</option>
                        <option value="status:pending">Set Pending</option>
                        <option value="status:locked">Set Locked</option>
                        <option value="status:disabled">Set Disabled</option>
                        <option value="role:admin">Set as Admin</option>
                        <option value="role:editor">Set as Editor</option>
                        <option value="role:viewer">Set as Viewer</option>
                        <option value="delete">Delete Selected</option>
                    </select>
                    <button id="applyBulkAction" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        Apply
                    </button>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ url_for('user.admin_users', search=search, sort_by='username', sort_order='asc' if sort_by == 'username' and sort_order == 'desc' else 'desc') }}" class="flex items-center">
                                    Username
                                    {% if sort_by == 'username' %}
                                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {% if sort_order == 'asc' %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                            {% else %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            {% endif %}
                                        </svg>
                                    {% endif %}
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ url_for('user.admin_users', search=search, sort_by='email', sort_order='asc' if sort_by == 'email' and sort_order == 'desc' else 'desc') }}" class="flex items-center">
                                    Email
                                    {% if sort_by == 'email' %}
                                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {% if sort_order == 'asc' %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                            {% else %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            {% endif %}
                                        </svg>
                                    {% endif %}
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ url_for('user.admin_users', search=search, sort_by='role', sort_order='asc' if sort_by == 'role' and sort_order == 'desc' else 'desc') }}" class="flex items-center">
                                    Role
                                    {% if sort_by == 'role' %}
                                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {% if sort_order == 'asc' %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                            {% else %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            {% endif %}
                                        </svg>
                                    {% endif %}
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ url_for('user.admin_users', search=search, sort_by='account_status', sort_order='asc' if sort_by == 'account_status' and sort_order == 'desc' else 'desc') }}" class="flex items-center">
                                    Status
                                    {% if sort_by == 'account_status' %}
                                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {% if sort_order == 'asc' %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                            {% else %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            {% endif %}
                                        </svg>
                                    {% endif %}
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="{{ url_for('user.admin_users', search=search, sort_by='created_at', sort_order='asc' if sort_by == 'created_at' and sort_order == 'desc' else 'desc') }}" class="flex items-center">
                                    Created
                                    {% if sort_by == 'created_at' %}
                                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {% if sort_order == 'asc' %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                            {% else %}
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            {% endif %}
                                        </svg>
                                    {% endif %}
                                </a>
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for user in users %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="user-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" data-user-id="{{ user.user_id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ user.username }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ user.email }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if user.role == 'admin' %}bg-purple-100 text-purple-800
                                        {% elif user.role == 'editor' %}bg-blue-100 text-blue-800
                                        {% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ user.role.capitalize() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if user.account_status == 'active' %}bg-green-100 text-green-800
                                        {% elif user.account_status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif user.account_status == 'locked' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ user.account_status.capitalize() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ user.created_at.split('T')[0] if user.created_at else 'N/A' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{{ url_for('user.admin_user_details', user_id=user.user_id) }}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                                    <a href="{{ url_for('user.admin_edit_user', user_id=user.user_id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                                    {% if user.user_id != current_user.user_id %}
                                        <button onclick="deleteUser({{ user.user_id }}, '{{ user.username }}')" class="text-red-600 hover:text-red-900">Delete</button>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pages > 1 %}
                <div class="flex justify-between items-center mt-4">
                    <div class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ (page - 1) * per_page + 1 }}</span> to <span class="font-medium">{{ min(page * per_page, total) }}</span> of <span class="font-medium">{{ total }}</span> users
                    </div>
                    <div class="flex space-x-2">
                        {% if page > 1 %}
                            <a href="{{ url_for('user.admin_users', page=page-1, search=search, sort_by=sort_by, sort_order=sort_order) }}" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}

                        {% for p in range(max(1, page-2), min(pages+1, page+3)) %}
                            <a href="{{ url_for('user.admin_users', page=p, search=search, sort_by=sort_by, sort_order=sort_order) }}" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium {% if p == page %}bg-blue-600 text-white{% else %}text-gray-700 bg-white hover:bg-gray-50{% endif %}">
                                {{ p }}
                            </a>
                        {% endfor %}

                        {% if page < pages %}
                            <a href="{{ url_for('user.admin_users', page=page+1, search=search, sort_by=sort_by, sort_order=sort_order) }}" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Show toast message
        function showToast(message, type) {
            Toastify({
                text: message,
                duration: 3000,
                close: true,
                gravity: "top",
                position: "right",
                backgroundColor: type === "error" ? "#ff4444" : "#00C851",
            }).showToast();
        }

        // Delete user
        function deleteUser(userId, username) {
            if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                fetch(`/admin/users/${userId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(`User "${username}" deleted successfully.`, 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showToast(`Error: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showToast(`Error: ${error}`, 'error');
                });
            }
        }

        // Select all checkboxes
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Apply bulk action
        document.getElementById('applyBulkAction').addEventListener('click', function() {
            const action = document.getElementById('bulkAction').value;
            if (!action) {
                showToast('Please select an action.', 'error');
                return;
            }

            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            if (checkboxes.length === 0) {
                showToast('Please select at least one user.', 'error');
                return;
            }

            const userIds = Array.from(checkboxes).map(checkbox => parseInt(checkbox.dataset.userId));

            // Parse action and value
            const [actionType, actionValue] = action.split(':');

            // Confirm deletion
            if (actionType === 'delete') {
                if (!confirm(`Are you sure you want to delete ${userIds.length} selected users?`)) {
                    return;
                }
            }

            // Send request
            fetch('/admin/users/bulk_action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    user_ids: userIds,
                    action: actionType,
                    value: actionValue
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Create a more detailed message for role changes that include group assignment
                    let message = `Bulk action applied successfully to ${userIds.length} users.`;

                    // If this was a role change to editor or viewer, add info about group assignment
                    if (actionType === 'role' && (actionValue === 'editor' || actionValue === 'viewer')) {
                        const groupName = actionValue === 'editor' ? 'Editor' : 'Viewer';
                        message = `${userIds.length} users set as ${actionValue.charAt(0).toUpperCase() + actionValue.slice(1)} and assigned to ${groupName} Group with all group permissions.`;
                    }

                    showToast(message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(`Error: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'error');
            });
        });
    </script>
{% endblock %}
