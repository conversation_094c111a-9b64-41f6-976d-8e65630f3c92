{% extends "admin_base.html" %}

{% block title %}Permission Audit Logs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">Permission Audit Logs</h1>
            <p class="text-muted">Track all permission changes in the system.</p>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Filter Logs</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{{ url_for('user.admin_permission_audit') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="admin_user_id">Admin User</label>
                                    <select class="form-control" id="admin_user_id" name="admin_user_id">
                                        <option value="">All Admins</option>
                                        {% for user in users %}
                                            {% if user.role == 'admin' %}
                                            <option value="{{ user.user_id }}" {% if admin_user_id == user.user_id %}selected{% endif %}>
                                                {{ user.username }}
                                            </option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="target_user_id">Target User</label>
                                    <select class="form-control" id="target_user_id" name="target_user_id">
                                        <option value="">All Users</option>
                                        {% for user in users %}
                                        <option value="{{ user.user_id }}" {% if target_user_id == user.user_id %}selected{% endif %}>
                                            {{ user.username }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="change_type">Change Type</label>
                                    <select class="form-control" id="change_type" name="change_type">
                                        <option value="">All Types</option>
                                        {% for type in change_types %}
                                        <option value="{{ type }}" {% if change_type == type %}selected{% endif %}>
                                            {{ type|replace('_', ' ')|title }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="end_date">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <div class="form-group mb-0">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Apply Filters
                                    </button>
                                    <a href="{{ url_for('user.admin_permission_audit') }}" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Permission Audit Logs</h5>
                </div>
                <div class="card-body">
                    {% if logs %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Admin User</th>
                                    <th>Target User</th>
                                    <th>Change Type</th>
                                    <th>Entity Changed</th>
                                    <th>Old Value</th>
                                    <th>New Value</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.timestamp }}</td>
                                    <td>
                                        {% if log.admin_username %}
                                        <a href="{{ url_for('user.admin_user_details', user_id=log.admin_user_id) }}">
                                            {{ log.admin_username }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.target_username %}
                                        <a href="{{ url_for('user.admin_user_details', user_id=log.target_user_id) }}">
                                            {{ log.target_username }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ log.change_type|replace('_', ' ')|title }}
                                        </span>
                                    </td>
                                    <td>{{ log.entity_changed }}</td>
                                    <td>
                                        {% if log.old_value == 'enabled' %}
                                        <span class="badge badge-success">Enabled</span>
                                        {% elif log.old_value == 'disabled' %}
                                        <span class="badge badge-danger">Disabled</span>
                                        {% else %}
                                        {{ log.old_value }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.new_value == 'enabled' %}
                                        <span class="badge badge-success">Enabled</span>
                                        {% elif log.new_value == 'disabled' %}
                                        <span class="badge badge-danger">Disabled</span>
                                        {% else %}
                                        {{ log.new_value }}
                                        {% endif %}
                                    </td>
                                    <td>{{ log.ip_address }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mt-4">
                            {% if page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user.admin_permission_audit', page=page-1, admin_user_id=admin_user_id, target_user_id=target_user_id, change_type=change_type, start_date=start_date, end_date=end_date) }}">
                                    Previous
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            {% endif %}

                            {% for p in range(1, pages + 1) %}
                            <li class="page-item {% if p == page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('user.admin_permission_audit', page=p, admin_user_id=admin_user_id, target_user_id=target_user_id, change_type=change_type, start_date=start_date, end_date=end_date) }}">
                                    {{ p }}
                                </a>
                            </li>
                            {% endfor %}

                            {% if page < pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user.admin_permission_audit', page=page+1, admin_user_id=admin_user_id, target_user_id=target_user_id, change_type=change_type, start_date=start_date, end_date=end_date) }}">
                                    Next
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No permission audit logs found.
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted">
                    Showing {{ logs|length }} of {{ total }} logs
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
