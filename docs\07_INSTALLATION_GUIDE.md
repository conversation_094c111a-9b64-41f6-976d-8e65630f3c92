# Installation and Setup Guide

## Overview

This guide provides complete instructions for installing and deploying the Document Management System in various environments, from development to production.

## System Requirements

### Hardware Requirements

**Minimum Requirements:**
- **CPU**: 4-core processor (Intel i5 or AMD Ryzen 5 equivalent)
- **RAM**: 8GB (16GB recommended)
- **Storage**: 50GB available space (SSD recommended)
- **Network**: Stable internet connection for model downloads

**Recommended Requirements:**
- **CPU**: 8-core processor (Intel i7 or AMD Ryzen 7)
- **RAM**: 16GB or more
- **Storage**: 100GB+ SSD storage
- **GPU**: NVIDIA GPU with 8GB+ VRAM (optional, for enhanced performance)

**Production Requirements:**
- **CPU**: 16+ cores
- **RAM**: 32GB or more
- **Storage**: 500GB+ enterprise SSD
- **Network**: High-bandwidth connection
- **Backup**: Redundant storage solution

### Software Requirements

**Operating System:**
- Windows 10/11 (64-bit)
- macOS 10.15+ (Intel or Apple Silicon)
- Linux (Ubuntu 20.04+, CentOS 8+, or equivalent)

**Required Software:**
- Python 3.8 or higher
- Git (for source code management)
- Ollama (AI model serving platform)

**Additional Dependencies:**
- Ghostscript (PDF processing)
- Tesseract OCR (text extraction from images)
- Visual C++ Redistributable (Windows only)

## Development Environment Setup

### 1. Prerequisites Installation

#### Windows Setup

**Install Python:**
1. Download Python 3.8+ from [python.org](https://python.org)
2. Run installer with "Add Python to PATH" checked
3. Verify installation: `python --version`

**Install Git:**
1. Download Git from [git-scm.com](https://git-scm.com)
2. Install with default settings
3. Verify: `git --version`

**Install Ollama:**
1. Download from [ollama.ai](https://ollama.ai)
2. Run the installer
3. Verify: `ollama --version`

**Install Ghostscript:**
1. Download from [ghostscript.com](https://ghostscript.com)
2. Install to default location
3. Add to PATH environment variable

**Install Tesseract:**
1. Download from [github.com/UB-Mannheim/tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
2. Install with additional language packs if needed
3. Add to PATH environment variable

#### macOS Setup

**Using Homebrew (recommended):**
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required packages
brew install python@3.11
brew install git
brew install ghostscript
brew install tesseract

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh
```

#### Linux Setup (Ubuntu/Debian)

```bash
# Update package list
sudo apt update

# Install Python and pip
sudo apt install python3 python3-pip python3-venv git

# Install system dependencies
sudo apt install ghostscript tesseract-ocr tesseract-ocr-eng

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Install additional dependencies for PDF processing
sudo apt install poppler-utils
```

### 2. Project Setup

#### Clone Repository

```bash
# Clone the project repository
git clone <repository-url>
cd localairag

# Or if you have the source code locally
cd /path/to/localairag
```

#### Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate

# macOS/Linux:
source venv/bin/activate

# Verify activation (should show venv path)
which python
```

#### Install Python Dependencies

```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install project dependencies
pip install -r requirements.txt

# Verify installation
pip list
```

### 3. Environment Configuration

#### Create Environment File

Create a `.env` file in the project root:

```bash
# Copy example environment file
cp .env.example .env

# Edit with your preferred editor
# Windows:
notepad .env

# macOS/Linux:
nano .env
```

#### Basic Environment Configuration

```bash
# Flask Configuration
FLASK_SECRET_KEY=your-development-secret-key
FLASK_ENV=development
FLASK_DEBUG=true

# Database Paths (development)
DB_PATH=./database.db
USER_DB_PATH=./user_management.db
SCRAPED_DB_PATH=./scraped_pages.db
CHROMA_PATH=./chroma
TEMP_FOLDER=./_temp

# AI Model Configuration
LLM_MODEL=llama3.1:8b-instruct-q4_K_M
TEXT_EMBEDDING_MODEL=mxbai-embed-large:latest
VISION_MODEL=llama3.2-vision:11b-instruct-q4_K_M

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_API_HOST=http://localhost:11434

# Feature Toggles
USE_VISION_MODEL=true
FILTER_PDF_IMAGES=true
ANTI_HALLUCINATION_MODE=strict

# External Tool Paths (adjust as needed)
# Windows example:
GHOSTSCRIPT_PATH=C:\Program Files\gs\gs10.02.1\bin\gswin64c.exe
TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe

# macOS/Linux example:
GHOSTSCRIPT_PATH=/usr/local/bin/gs
TESSERACT_CMD=/usr/local/bin/tesseract
```

### 4. AI Model Setup

#### Download Required Models

```bash
# Start Ollama service (if not already running)
ollama serve

# In a new terminal, download models
# Language model (required)
ollama pull llama3.1:8b-instruct-q4_K_M

# Embedding model (required)
ollama pull mxbai-embed-large:latest

# Vision model (optional but recommended)
ollama pull llama3.2-vision:11b-instruct-q4_K_M

# Alternative lightweight models
ollama pull gemma3:1b
ollama pull gemma3:4b-it-q4_K_M

# Verify models are downloaded
ollama list
```

### 5. Database Initialization

#### Initialize Application Databases

```bash
# Ensure virtual environment is activated
# Run the application once to initialize databases
python app.py

# The application will automatically create:
# - database.db (main application data)
# - user_management.db (user accounts and permissions)
# - scraped_pages.db (web content cache)
# - chroma/ directory (vector database)
```

#### Create Admin User

```bash
# Run the application
python app.py

# In a web browser, navigate to:
http://localhost:8080/admin

# Create the first admin user through the registration interface
# Or use the Python console:
python -c "
from user_management import create_user
create_user('admin', '<EMAIL>', 'secure_password', 'admin', 'Administrator')
print('Admin user created successfully')
"
```

### 6. Verification and Testing

#### Test Basic Functionality

```bash
# Start the application
python app.py

# The application should start on port 8080
# You should see output similar to:
# * Running on http://127.0.0.1:8080
# * Debug mode: on
```

#### Verify Components

**Test Web Interface:**
1. Open browser to `http://localhost:8080`
2. Verify the chat interface loads
3. Test theme toggle functionality

**Test Admin Interface:**
1. Navigate to `http://localhost:8080/admin`
2. Log in with admin credentials
3. Verify dashboard loads correctly

**Test AI Models:**
1. Upload a test PDF document
2. Ask a question about the content
3. Verify AI response generation

**Test Vision Models:**
1. Upload a PDF with images
2. Enable vision model processing
3. Verify image analysis functionality

## Production Deployment

### 1. Production Environment Setup

#### Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install production dependencies
sudo apt install nginx supervisor postgresql-client

# Create application user
sudo useradd -m -s /bin/bash dmsapp
sudo usermod -aG sudo dmsapp

# Create application directories
sudo mkdir -p /var/lib/dms
sudo mkdir -p /var/log/dms
sudo chown -R dmsapp:dmsapp /var/lib/dms
sudo chown -R dmsapp:dmsapp /var/log/dms
```

#### Production Environment Variables

```bash
# Production .env file
FLASK_ENV=production
FLASK_DEBUG=false
FLASK_SECRET_KEY=generate-strong-random-key-here

# Production database paths
DB_PATH=/var/lib/dms/database.db
USER_DB_PATH=/var/lib/dms/user_management.db
SCRAPED_DB_PATH=/var/lib/dms/scraped_pages.db
CHROMA_PATH=/var/lib/dms/chroma
TEMP_FOLDER=/var/lib/dms/temp

# Security settings
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=60
SESSION_TIMEOUT_MINUTES=30

# Performance optimization
OLLAMA_NUM_PARALLEL=4
OLLAMA_MAX_LOADED_MODELS=3
```

### 2. Web Server Configuration

#### Nginx Configuration

Create `/etc/nginx/sites-available/dms`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # File upload size limit
    client_max_body_size 25M;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Static files
    location /static/ {
        alias /path/to/dms/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/dms /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. Process Management

#### Supervisor Configuration

Create `/etc/supervisor/conf.d/dms.conf`:

```ini
[program:dms]
command=/path/to/dms/venv/bin/python app.py
directory=/path/to/dms
user=dmsapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/dms/app.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/path/to/dms/venv/bin"

[program:ollama]
command=/usr/local/bin/ollama serve
user=dmsapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/dms/ollama.log
```

Start services:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start dms
sudo supervisorctl start ollama
```

### 4. Security Hardening

#### Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

#### SSL/TLS Setup

```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

### 5. Backup and Monitoring

#### Backup Script

Create `/usr/local/bin/dms-backup.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/dms"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Backup databases
cp /var/lib/dms/database.db "$BACKUP_DIR/database_$DATE.db"
cp /var/lib/dms/user_management.db "$BACKUP_DIR/users_$DATE.db"
cp /var/lib/dms/scraped_pages.db "$BACKUP_DIR/scraped_$DATE.db"

# Backup ChromaDB
tar -czf "$BACKUP_DIR/chroma_$DATE.tar.gz" /var/lib/dms/chroma/

# Cleanup old backups (keep 30 days)
find "$BACKUP_DIR" -name "*.db" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

Make executable and schedule:
```bash
sudo chmod +x /usr/local/bin/dms-backup.sh

# Add to crontab for daily backups at 2 AM
sudo crontab -e
# Add line: 0 2 * * * /usr/local/bin/dms-backup.sh
```

## Troubleshooting

### Common Installation Issues

**Python Version Issues:**
```bash
# Check Python version
python --version

# If using wrong version, specify Python 3
python3 --version
python3 -m venv venv
```

**Permission Issues:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER /path/to/project
chmod +x scripts/*.sh
```

**Port Conflicts:**
```bash
# Check if port 8080 is in use
netstat -tulpn | grep :8080

# Kill process using port
sudo kill -9 $(lsof -t -i:8080)
```

**Ollama Connection Issues:**
```bash
# Check Ollama status
ollama list

# Restart Ollama service
sudo systemctl restart ollama

# Check Ollama logs
journalctl -u ollama -f
```

### Performance Optimization

**Memory Issues:**
- Reduce model context size in configuration
- Limit concurrent processing threads
- Monitor system resources with `htop`

**Storage Issues:**
- Clean temporary files regularly
- Implement log rotation
- Monitor disk usage with `df -h`

**Network Issues:**
- Check firewall settings
- Verify DNS resolution
- Test connectivity to external services

This installation guide provides comprehensive instructions for deploying the Document Management System in various environments, ensuring reliable and secure operation.
