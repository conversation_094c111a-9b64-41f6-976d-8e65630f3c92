"""
Generate test geolocation data for the Geographic Distribution of Visitors map.

This script creates sample geolocation data entries to demonstrate the
functionality of the Geographic Distribution map in the AI Analytics Dashboard.
"""

import sqlite3
import random
import uuid
from datetime import datetime, timedelta

# Import DEV_LOCATION from geo_utils to ensure consistency
from geo_utils import DEV_LOCATION

# Sample data
SAMPLE_LOCATIONS = [
    # Real locations
    {
        "ip_address": "*******",
        "city": "Mountain View",
        "region": "California",
        "country": "United States",
        "latitude": 37.4056,
        "longitude": -122.0775
    },
    {
        "ip_address": "*************",
        "city": "San Francisco",
        "region": "California",
        "country": "United States",
        "latitude": 37.7749,
        "longitude": -122.4194
    },
    {
        "ip_address": "*************",
        "city": "Redmond",
        "region": "Washington",
        "country": "United States",
        "latitude": 47.6740,
        "longitude": -122.1215
    },
    {
        "ip_address": "************",
        "city": "London",
        "region": "England",
        "country": "United Kingdom",
        "latitude": 51.5074,
        "longitude": -0.1278
    },
    {
        "ip_address": "***************",
        "city": "Tokyo",
        "region": "Tokyo",
        "country": "Japan",
        "latitude": 35.6762,
        "longitude": 139.6503
    },
    {
        "ip_address": "*************",
        "city": "Sydney",
        "region": "New South Wales",
        "country": "Australia",
        "latitude": -33.8688,
        "longitude": 151.2093
    },
    {
        "ip_address": "************",
        "city": "Berlin",
        "region": "Berlin",
        "country": "Germany",
        "latitude": 52.5200,
        "longitude": 13.4050
    },
    {
        "ip_address": "**************",
        "city": "Paris",
        "region": "Île-de-France",
        "country": "France",
        "latitude": 48.8566,
        "longitude": 2.3522
    },
    {
        "ip_address": "**************",
        "city": "Toronto",
        "region": "Ontario",
        "country": "Canada",
        "latitude": 43.6532,
        "longitude": -79.3832
    },
    # Local development entries - using centralized DEV_LOCATION
    {
        "ip_address": "127.0.0.1",
        "city": DEV_LOCATION["city"],
        "region": DEV_LOCATION["region"],
        "country": DEV_LOCATION["country"],
        "latitude": DEV_LOCATION["latitude"],
        "longitude": DEV_LOCATION["longitude"]
    },
    # Alternative local entry (using the traditional format for backward compatibility)
    {
        "ip_address": "127.0.0.1",
        "city": "Local",
        "region": "Development",
        "country": "Local",
        "latitude": 0,
        "longitude": 0
    }
]

# Sample user agents
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
]

# Sample page URLs
PAGE_URLS = [
    "/",
    "/admin/dashboard",
    "/admin/analytics",
    "/chat"
]

# Sample client names
CLIENT_NAMES = [
    "John Doe",
    "Jane Smith",
    "Robert Johnson",
    "Emily Davis",
    "Michael Wilson",
    None  # Some entries without client name
]

def create_geoip_table():
    """Create the geoip_analytics table if it doesn't exist."""
    conn = sqlite3.connect("chat_history.db")
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS geoip_analytics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_address TEXT,
            device_fingerprint TEXT,
            client_name TEXT,
            city TEXT,
            region TEXT,
            country TEXT,
            latitude REAL,
            longitude REAL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            user_agent TEXT,
            page_url TEXT,
            session_id TEXT
        )
    ''')

    conn.commit()
    conn.close()
    print("Created geoip_analytics table")

def generate_test_data(num_entries=50):
    """Generate test geolocation data entries."""
    conn = sqlite3.connect("chat_history.db")
    cursor = conn.cursor()

    # Generate random timestamps within the last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    # Generate device fingerprints (5 unique devices)
    device_fingerprints = [str(uuid.uuid4())[:12] for _ in range(5)]

    # Generate session IDs (10 unique sessions)
    session_ids = [str(uuid.uuid4()) for _ in range(10)]

    # Insert test data
    for _ in range(num_entries):
        # Select a random location
        location = random.choice(SAMPLE_LOCATIONS)

        # Generate a random timestamp
        random_seconds = random.randint(0, int((end_date - start_date).total_seconds()))
        timestamp = start_date + timedelta(seconds=random_seconds)

        # Select random values for other fields
        device_fingerprint = random.choice(device_fingerprints)
        client_name = random.choice(CLIENT_NAMES)
        user_agent = random.choice(USER_AGENTS)
        page_url = random.choice(PAGE_URLS)
        session_id = random.choice(session_ids)

        # Insert the data
        cursor.execute('''
            INSERT INTO geoip_analytics (
                ip_address, device_fingerprint, client_name, city, region, country,
                latitude, longitude, timestamp, user_agent, page_url, session_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            location["ip_address"],
            device_fingerprint,
            client_name,
            location["city"],
            location["region"],
            location["country"],
            location["latitude"],
            location["longitude"],
            timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            user_agent,
            page_url,
            session_id
        ))

    conn.commit()
    conn.close()
    print(f"Generated {num_entries} test geolocation data entries")

if __name__ == "__main__":
    create_geoip_table()
    generate_test_data(50)
    print("Test data generation complete")
