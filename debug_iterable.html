<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Iterable Error</title>
</head>
<body>
    <h1>Debug Test - No External Scripts</h1>
    <p>This page has no external scripts to test if the Iterable error persists.</p>
    
    <script>
        console.log('Debug page loaded - checking for Iterable errors');
        
        // Check if there are any global Iterable objects
        if (typeof window.iterable !== 'undefined') {
            console.log('Iterable object found:', window.iterable);
        } else {
            console.log('No Iterable object found');
        }
        
        // Listen for any console errors
        window.addEventListener('error', function(e) {
            if (e.message.includes('iterable') || e.message.includes('Iterable')) {
                console.log('Iterable-related error detected:', e.message);
            }
        });
    </script>
</body>
</html>
