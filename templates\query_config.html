<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Query Configuration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Query Configuration</h1>
                <a href="{{ url_for('admin_dashboard') }}" class="text-blue-600 hover:underline">&larr; Back to Dashboard</a>
            </div>

            <div id="statusMessage" class="mb-6 hidden"></div>

            <form id="queryConfigForm" class="space-y-8">
                <!-- Preamble Text Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Preamble Text</h2>
                    <p class="text-sm text-gray-600 mb-4">This text is included at the beginning of each prompt to the LLM, providing context about how to use the retrieved information.</p>
                    <div>
                        <textarea id="preamble" name="preamble" rows="8" 
                            class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                            placeholder="Enter preamble text...">{{ preamble }}</textarea>
                    </div>
                </div>

                <!-- Anti-Hallucination Modes Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Anti-Hallucination Modes</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure how the system handles potential hallucinations or fabricated information.</p>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Default Mode</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="default_mode" value="strict" class="form-radio" 
                                    {% if anti_hallucination_modes.default_mode == 'strict' %}checked{% endif %}>
                                <span class="ml-2">Strict</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="default_mode" value="balanced" class="form-radio"
                                    {% if anti_hallucination_modes.default_mode == 'balanced' %}checked{% endif %}>
                                <span class="ml-2">Balanced</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="default_mode" value="off" class="form-radio"
                                    {% if anti_hallucination_modes.default_mode == 'off' %}checked{% endif %}>
                                <span class="ml-2">Off</span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="custom_instructions">
                            Custom Instructions
                        </label>
                        <textarea id="custom_instructions" name="custom_instructions" rows="4" 
                            class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                            placeholder="Enter custom instructions for handling uncertainty...">{{ anti_hallucination_modes.custom_instructions }}</textarea>
                    </div>
                </div>

                <!-- Prompt Templates Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Prompt Templates</h2>
                    <p class="text-sm text-gray-600 mb-4">Customize the prompt templates for different query types.</p>
                    
                    <div class="mb-6">
                        <div class="flex mb-2">
                            <button type="button" id="templateTabStrict" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-tl-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                Strict
                            </button>
                            <button type="button" id="templateTabBalanced" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                Balanced
                            </button>
                            <button type="button" id="templateTabOff" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                Off
                            </button>
                            <button type="button" id="templateTabGeneral" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                General
                            </button>
                            <button type="button" id="templateTabDocSpecific" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-tr-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                Doc-Specific
                            </button>
                        </div>
                        
                        <div id="templateContentStrict" class="template-content">
                            <textarea id="template_strict" name="template_strict" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter strict mode template...">{{ prompt_templates.strict }}</textarea>
                        </div>
                        <div id="templateContentBalanced" class="template-content hidden">
                            <textarea id="template_balanced" name="template_balanced" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter balanced mode template...">{{ prompt_templates.balanced }}</textarea>
                        </div>
                        <div id="templateContentOff" class="template-content hidden">
                            <textarea id="template_off" name="template_off" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter off mode template...">{{ prompt_templates.off }}</textarea>
                        </div>
                        <div id="templateContentGeneral" class="template-content hidden">
                            <textarea id="template_general" name="template_general" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter general query template...">{{ prompt_templates.general }}</textarea>
                        </div>
                        <div id="templateContentDocSpecific" class="template-content hidden">
                            <textarea id="template_doc_specific" name="template_doc_specific" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter document-specific template...">{{ prompt_templates.document_specific }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Insufficient Information Phrases Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Insufficient Information Phrases</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure phrases used to detect when the system lacks adequate information.</p>
                    
                    <div id="insufficientPhrases" class="space-y-2 mb-4">
                        {% for phrase in insufficient_info_phrases %}
                        <div class="flex items-center">
                            <input type="text" name="insufficient_phrase[]" value="{{ phrase }}" 
                                class="flex-grow px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                            <button type="button" class="remove-phrase ml-2 px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <button type="button" id="addPhrase" 
                        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                        Add Phrase
                    </button>
                </div>

                <!-- Follow-up Question Templates Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Follow-up Question Templates</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure templates for generating follow-up questions.</p>
                    
                    <div class="mb-6">
                        <div class="flex mb-2">
                            <button type="button" id="followupTabDefault" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-tl-lg rounded-tr-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                Default
                            </button>
                            <button type="button" id="followupTabInsufficient" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-tr-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                Insufficient Info
                            </button>
                        </div>
                        
                        <div id="followupContentDefault" class="followup-content">
                            <textarea id="followup_default" name="followup_default" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter default follow-up template...">{{ followup_question_templates.default }}</textarea>
                        </div>
                        <div id="followupContentInsufficient" class="followup-content hidden">
                            <textarea id="followup_insufficient" name="followup_insufficient" rows="10" 
                                class="w-full px-3 py-2 text-gray-700 border rounded-b-lg focus:outline-none focus:shadow-outline"
                                placeholder="Enter insufficient info follow-up template...">{{ followup_question_templates.insufficient_info }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="/static/query_config.js"></script>
</body>
</html>
