<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment \nTreatment</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Labor \nLabor \n(A) \n(A)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Materials \nMaterials \n(B) \n(B)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total per cost Plot \nTotal per cost Plot \n(A+B=C) \n(A+B=C)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total Cost per \nTotal Cost per \nHectare (Cx52*) \nHectare (Cx52*)</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment | \nTreatment I</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">213 \n213</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">330 \n330</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">543 \n543</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">28,210 \n28,210</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment Il \nTreatment II</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">244 \n244</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">330 \n330</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">574 \n574</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">29,835 \n29,835</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment  Il \nTreatment  III</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">294 \n294</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">690 \n690</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">984 \n984</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">51,155 \n51,155</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment IV \nTreatment IV</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">294 \n294</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">474 \n474</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">768 \n768</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">39,923 \n39,923</td>
    </tr>
  </tbody>
</table>