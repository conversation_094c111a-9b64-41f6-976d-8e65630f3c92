#!/usr/bin/env python3
"""
Test script for Phase 2: Contextual Enhancement implementation.
This script tests the time-based greetings and session awareness features.
"""

import os
import sys
import json
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from greeting_manager import GreetingManager
from db_schema import initialize_database

def test_greeting_manager():
    """Test the enhanced GreetingManager functionality."""
    print("🧪 Testing Phase 2: Contextual Enhancement")
    print("=" * 50)
    
    # Initialize database
    print("📊 Initializing database...")
    if initialize_database():
        print("✅ Database initialized successfully")
    else:
        print("❌ Database initialization failed")
        return False
    
    # Initialize greeting manager
    greeting_manager = GreetingManager()
    
    # Test 1: Time-based greetings
    print("\n🌅 Test 1: Time-based greetings")
    test_contexts = [
        {
            'greeting_type': 'time_based',
            'local_time': '2024-01-15T08:30:00Z',  # Morning
            'timezone': 'Asia/Manila',
            'device_fingerprint': 'test-device-001',
            'session_id': 'test-session-001'
        },
        {
            'greeting_type': 'time_based', 
            'local_time': '2024-01-15T14:30:00Z',  # Afternoon
            'timezone': 'Asia/Manila',
            'device_fingerprint': 'test-device-002',
            'session_id': 'test-session-002'
        },
        {
            'greeting_type': 'time_based',
            'local_time': '2024-01-15T20:30:00Z',  # Evening
            'timezone': 'Asia/Manila',
            'device_fingerprint': 'test-device-003',
            'session_id': 'test-session-003'
        }
    ]
    
    for i, context in enumerate(test_contexts):
        print(f"\n  Test 1.{i+1}: {context['local_time']}")
        greeting_data = greeting_manager.get_contextual_greeting("TestUser", context)
        print(f"    Greeting: {greeting_data['greeting']}")
        print(f"    Time of day: {greeting_data.get('time_of_day', 'N/A')}")
        print(f"    Session type: {greeting_data.get('session_type', 'N/A')}")
        print(f"    Source: {greeting_data.get('source', 'N/A')}")
        
        # Log the greeting for analytics
        greeting_manager.log_greeting_usage(
            context['session_id'], 
            "TestUser", 
            greeting_data
        )
    
    # Test 2: Session awareness (new vs returning users)
    print("\n👤 Test 2: Session awareness")
    
    # First visit (new user)
    new_user_context = {
        'greeting_type': 'welcome',
        'device_fingerprint': 'new-user-device',
        'session_id': 'new-user-session-001',
        'client_name': 'NewUser',
        'local_time': '2024-01-15T10:00:00Z',
        'timezone': 'Asia/Manila'
    }
    
    print("\n  Test 2.1: New user greeting")
    greeting_data = greeting_manager.get_contextual_greeting("NewUser", new_user_context)
    print(f"    Greeting: {greeting_data['greeting']}")
    print(f"    Session type: {greeting_data.get('session_type', 'N/A')}")
    
    # Log the greeting
    greeting_manager.log_greeting_usage(
        new_user_context['session_id'],
        "NewUser",
        greeting_data
    )
    
    # Simulate returning user (same device, different session)
    returning_user_context = {
        'greeting_type': 'return_user',
        'device_fingerprint': 'new-user-device',  # Same device
        'session_id': 'returning-user-session-002',
        'client_name': 'NewUser',
        'local_time': '2024-01-15T15:00:00Z',
        'timezone': 'Asia/Manila'
    }
    
    print("\n  Test 2.2: Returning user greeting")
    greeting_data = greeting_manager.get_contextual_greeting("NewUser", returning_user_context)
    print(f"    Greeting: {greeting_data['greeting']}")
    print(f"    Session type: {greeting_data.get('session_type', 'N/A')}")
    
    # Log the greeting
    greeting_manager.log_greeting_usage(
        returning_user_context['session_id'],
        "NewUser", 
        greeting_data
    )
    
    # Test 3: Analytics functionality
    print("\n📈 Test 3: Analytics functionality")
    
    # Get greeting analytics
    analytics = greeting_manager.get_greeting_analytics()
    print(f"    Total greetings: {analytics.get('total_greetings', 0)}")
    print(f"    Greetings by type: {len(analytics.get('greetings_by_type', []))}")
    print(f"    Greetings by time: {len(analytics.get('greetings_by_time', []))}")
    
    # Get engagement patterns
    patterns = greeting_manager.get_time_based_engagement_patterns()
    print(f"    Time patterns: {len(patterns.get('by_time_of_day', []))}")
    print(f"    Weekly patterns: {len(patterns.get('by_day_of_week', []))}")
    
    # Test 4: Greeting templates
    print("\n📝 Test 4: Greeting templates")
    
    # Get time-based templates
    time_templates = greeting_manager.get_greeting_templates('time_based')
    print(f"    Time-based templates: {len(time_templates)}")
    
    # Get return user templates
    return_templates = greeting_manager.get_greeting_templates('return_user')
    print(f"    Return user templates: {len(return_templates)}")
    
    print("\n✅ Phase 2 testing completed successfully!")
    print("\n📋 Summary:")
    print("  ✓ Time-based greetings working")
    print("  ✓ Session awareness implemented")
    print("  ✓ Analytics tracking functional")
    print("  ✓ Database templates loaded")
    
    return True

if __name__ == "__main__":
    try:
        success = test_greeting_manager()
        if success:
            print("\n🎉 All Phase 2 tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
