# Geolocation Development Guide

This guide explains how to use and test the geolocation functionality during development.

## Overview

The application uses MaxMind's GeoLite2 Web Service to provide geolocation services. In production, it detects the user's IP address and looks up their approximate location. In development mode, you can simulate different IP addresses to test the functionality.

## Setting Up Development Mode

### Environment Variables

Add these variables to your `.env` file or set them in your environment:

```
# Enable development mode
DEV_MODE=true

# Set a test IP address to use (examples below)
TEST_IP=*******

# Optional: Set logging level for more detailed output
DEV_LOG_LEVEL=DEBUG

# MaxMind GeoLite2 Web Service credentials (required)
MAXMIND_ACCOUNT_ID=your_account_id
MAXMIND_LICENSE_KEY=your_license_key

# Optional: Configure MaxMind service (default is 'city')
MAXMIND_SERVICE=city  # 'city' or 'country'
MAXMIND_TIMEOUT=5     # Timeout in seconds
```

You can sign up for a free MaxMind GeoLite2 account at: https://www.maxmind.com/en/geolite2/signup

After signing up, generate a license key at: https://www.maxmind.com/en/accounts/current/license-key

### Test IP Addresses

Here are some IP addresses you can use for testing:

- `*******` - Google DNS (Mountain View, CA, USA)
- `*******` - Cloudflare DNS (various locations)
- `**************` - OpenDNS (San Francisco, CA, USA)
- `*********` - Yandex.DNS (Moscow, Russia)
- `***************` - CleanBrowsing (various locations)
- `*************` - Norton ConnectSafe (various locations)

## Testing Geolocation

### Using the Test Script

We've included a test script that demonstrates the geolocation functionality:

```bash
# Test with the IP from your environment variables
python test_geolocation.py

# Test with a specific IP
python test_geolocation.py *********
```

### Using the API Directly

You can also use the geolocation functions directly in your code:

```python
from geo_utils import test_geolocation, get_development_info

# Get information about the development environment
dev_info = get_development_info()
print(dev_info)

# Test geolocation with a specific IP
result = test_geolocation("*******")
print(result)
```

## Debugging

When `DEV_MODE` is enabled and `DEV_LOG_LEVEL` is set to `DEBUG`, the application will output detailed logs about the geolocation process, including:

- IP address detection
- MaxMind API requests and responses
- Data extraction from MaxMind responses
- Cache operations

Check your application logs to see this information.

## Important Notes

### Rate Limiting

MaxMind's GeoLite2 Web Service has usage limits:

- Free accounts are limited to 1000 requests per day
- The implementation includes caching to reduce API calls
- Small random delays are added between requests to avoid rate limiting

### Attribution

When displaying geolocation data from MaxMind, proper attribution is required:

- Include the text: "This product includes GeoLite2 data created by MaxMind, available from https://www.maxmind.com"
- Follow the attribution guidelines: https://dev.maxmind.com/geoip/geolite2-free-geolocation-data

## Troubleshooting

### No Location Data

If you're not getting location data:

1. Check that `DEV_MODE` and `TEST_IP` are properly set
2. Verify that the application can access the internet
3. Try a different IP address
4. Check the logs for specific error messages

### Authentication Errors

If you see MaxMind authentication errors:

1. Check that your `MAXMIND_ACCOUNT_ID` and `MAXMIND_LICENSE_KEY` are correctly set
2. Verify that your MaxMind account is active
3. Make sure you have access to the GeoLite2 Web Service

### Quota Exceeded Errors

If you see MaxMind quota exceeded errors:

1. You have reached your daily limit of 1000 requests (for free accounts)
2. Wait until the next day when your quota resets
3. Consider upgrading to a paid plan if you need more requests

### Service Errors

If you see MaxMind service errors:

1. The service might be temporarily unavailable
2. There might be network connectivity issues
3. Check the MaxMind system status at https://status.maxmind.com/
