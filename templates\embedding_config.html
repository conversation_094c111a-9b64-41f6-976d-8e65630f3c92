<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embedding Configuration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Embedding Configuration</h1>
                <a href="{{ url_for('admin_dashboard') }}" class="text-blue-600 hover:underline">&larr; Back to Dashboard</a>
            </div>

            <div id="statusMessage" class="mb-6 hidden"></div>

            <form id="embeddingConfigForm" class="space-y-8">
                <!-- Text Chunking Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Text Chunking Parameters</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure how documents are split into chunks for embedding.</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="chunk_size" class="block text-sm font-medium text-gray-700 mb-1">Chunk Size (characters)</label>
                            <div class="flex items-center">
                                <input type="number" id="chunk_size" name="chunk_size" min="100" max="2000" step="50" value="{{ embedding_params.chunk_size }}"
                                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Larger chunks provide more context but may reduce retrieval precision.</p>
                        </div>
                        
                        <div>
                            <label for="chunk_overlap" class="block text-sm font-medium text-gray-700 mb-1">Chunk Overlap (characters)</label>
                            <div class="flex items-center">
                                <input type="number" id="chunk_overlap" name="chunk_overlap" min="0" max="500" step="10" value="{{ embedding_params.chunk_overlap }}"
                                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Overlap between chunks helps maintain context across chunk boundaries.</p>
                        </div>
                    </div>
                </div>

                <!-- Document Processing Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Document Processing</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure how different document elements are handled during embedding.</p>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="extract_tables" name="extract_tables" type="checkbox" 
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    {% if embedding_params.extract_tables %}checked{% endif %}>
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="extract_tables" class="font-medium text-gray-700">Extract Tables</label>
                                <p class="text-gray-500">Extract and process tables from PDF documents as separate chunks.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="extract_images" name="extract_images" type="checkbox" 
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    {% if embedding_params.extract_images %}checked{% endif %}>
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="extract_images" class="font-medium text-gray-700">Extract Images</label>
                                <p class="text-gray-500">Extract and save images from PDF documents.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="use_vision_model" name="use_vision_model" type="checkbox" 
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    {% if embedding_params.use_vision_model %}checked{% endif %}>
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="use_vision_model" class="font-medium text-gray-700">Use Vision Model</label>
                                <p class="text-gray-500">Use vision model to analyze and describe images during embedding.</p>
                            </div>
                        </div>
                        
                        <div class="ml-7 {% if not embedding_params.use_vision_model %}opacity-50{% endif %}" id="visionModelOptions">
                            <div class="mb-3">
                                <label for="filter_sensitivity" class="block text-sm font-medium text-gray-700 mb-1">Image Filter Sensitivity</label>
                                <select id="filter_sensitivity" name="filter_sensitivity" 
                                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                                    {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                                    <option value="low" {% if embedding_params.filter_sensitivity == 'low' %}selected{% endif %}>Low - Keep most images</option>
                                    <option value="medium" {% if embedding_params.filter_sensitivity == 'medium' %}selected{% endif %}>Medium - Balanced filtering</option>
                                    <option value="high" {% if embedding_params.filter_sensitivity == 'high' %}selected{% endif %}>High - Only keep very relevant images</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="max_images" class="block text-sm font-medium text-gray-700 mb-1">Maximum Images to Process</label>
                                <input type="number" id="max_images" name="max_images" min="1" max="100" 
                                    value="{{ embedding_params.max_images }}"
                                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                                    {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                                <p class="mt-1 text-xs text-gray-500">Limit the number of images processed per document to improve performance.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Embedding Model Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Embedding Model</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure the embedding model and related parameters.</p>
                    
                    <div class="mb-4">
                        <label for="embedding_model" class="block text-sm font-medium text-gray-700 mb-1">Embedding Model</label>
                        <select id="embedding_model" name="embedding_model" 
                            class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                            {% for model in available_embedding_models %}
                                <option value="{{ model.name }}" {% if model.name == embedding_params.embedding_model %}selected{% endif %}>
                                    {{ model.name }} ({{ model.size | filesizeformat }})
                                </option>
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Select the model used for generating embeddings.</p>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="show_dimensions" name="show_dimensions" type="checkbox" 
                                class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="show_dimensions" class="font-medium text-gray-700">Show Model Dimensions</label>
                            <p class="text-gray-500">Check the dimensions of the selected embedding model.</p>
                        </div>
                    </div>
                    
                    <div id="dimensionsResult" class="mt-3 hidden">
                        <div class="p-3 bg-blue-50 text-blue-700 rounded-md">
                            <p>Checking dimensions...</p>
                        </div>
                    </div>
                </div>

                <!-- Performance Optimization Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Performance Optimization</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure parameters that affect embedding performance and resource usage.</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="batch_size" class="block text-sm font-medium text-gray-700 mb-1">Batch Size</label>
                            <input type="number" id="batch_size" name="batch_size" min="1" max="100" value="{{ embedding_params.batch_size }}"
                                class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                            <p class="mt-1 text-xs text-gray-500">Number of chunks to process in a single batch. Higher values use more memory but may be faster.</p>
                        </div>
                        
                        <div>
                            <label for="processing_threads" class="block text-sm font-medium text-gray-700 mb-1">Processing Threads</label>
                            <input type="number" id="processing_threads" name="processing_threads" min="1" max="16" value="{{ embedding_params.processing_threads }}"
                                class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                            <p class="mt-1 text-xs text-gray-500">Number of parallel threads for document processing. Higher values may improve speed on multi-core systems.</p>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="/static/embedding_config.js"></script>
</body>
</html>
