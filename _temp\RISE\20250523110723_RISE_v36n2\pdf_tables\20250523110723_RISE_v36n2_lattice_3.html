<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">6</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">7</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">8</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">9</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">10</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">11</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">12</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Non-Mist Propagation Method \nNon-Mist Propagation Method</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Species \nSpecies</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average Rooting \nAverage Rooting\n(%) \n(%)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average \nAverage \nNumber of Roots (no.) \nNumber of Roots (no.)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average \nAverage \nLength of roots (cm) \nLength of roots (cm)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Ctrl \nCtrl\n97 \n97</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n300 \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA  | IBA| \nIBA \n300  | 500 | 800 \n500 \nppm | ppm | ppm \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \nIBA \n800  \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">|[Cirl| \nCtrl</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n300 \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA  | IBA  | IBA \nIBA \n300  | 500  | 800 \n500 \nppm | ppm | ppm \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n800  \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">|Ctrl| \nCtrl</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n300 \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA | IBA | IBA \nIBA \n300 | 500 | 800 \n500 \nppm | ppm | ppm \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n800  \nppm</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Bani \nBani</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">99 \n99</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">96 \n96</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">90</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">90  | 43| \n4.3</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">59 \n5.9</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">6.2</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">62  | 63 \n6.3</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">|66 \n6.6</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5.7</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">57 | 55| \n5.5</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">53 \n5.3</td>
    </tr>
  </tbody>
</table>