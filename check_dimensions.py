import os
import logging
import chromadb
import json
from langchain_ollama import ChatOllama
from langchain_ollama.embeddings import OllamaEmbeddings

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")

def check_model_dimensions():
    """Check the dimensions of the current embedding model"""
    try:
        # Initialize the embedding model
        embed_fn = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)
        
        # Generate an embedding for a test string
        test_embedding = embed_fn.embed_query("This is a test")
        
        logger.info(f"Current embedding model: {TEXT_EMBEDDING_MODEL}")
        logger.info(f"Embedding dimensions: {len(test_embedding)}")
        
        return len(test_embedding)
    except Exception as e:
        logger.error(f"Error checking model dimensions: {str(e)}")
        return None

def check_collection_dimensions():
    """Check the dimensions of all collections in the Chroma database"""
    try:
        results = {}
        
        # Get all categories
        if not os.path.exists(CHROMA_PATH):
            logger.error(f"Chroma DB path {CHROMA_PATH} does not exist")
            return results
        
        categories = [d for d in os.listdir(CHROMA_PATH) 
                     if os.path.isdir(os.path.join(CHROMA_PATH, d))]
        
        for category in categories:
            try:
                # Access the Chroma collection directly using chromadb
                persist_dir = os.path.join(CHROMA_PATH, category)
                client = chromadb.PersistentClient(path=persist_dir)
                
                # Get the collection
                collection = client.get_collection(name=category)
                
                # Get collection info
                collection_info = collection._collection.metadata
                
                # Extract dimension information
                if collection_info and "dimension" in collection_info:
                    dimension = collection_info["dimension"]
                    results[category] = dimension
                    logger.info(f"Category {category}: dimension = {dimension}")
                else:
                    # Try to infer from the embeddings
                    try:
                        peek = collection.peek(limit=1)
                        if peek and "embeddings" in peek and peek["embeddings"]:
                            dimension = len(peek["embeddings"][0])
                            results[category] = dimension
                            logger.info(f"Category {category}: inferred dimension = {dimension}")
                        else:
                            logger.warning(f"Category {category}: could not determine dimension (empty collection?)")
                            results[category] = "unknown"
                    except Exception as e2:
                        logger.warning(f"Category {category}: could not determine dimension: {str(e2)}")
                        results[category] = "error"
            except Exception as e:
                logger.error(f"Error checking dimensions for category {category}: {str(e)}")
                results[category] = "error"
        
        return results
    except Exception as e:
        logger.error(f"Error checking collection dimensions: {str(e)}")
        return {}

def main():
    """Main function to check dimensions"""
    logger.info("Checking embedding dimensions")
    
    # Check current model dimensions
    model_dim = check_model_dimensions()
    if model_dim is None:
        logger.error("Failed to determine current model dimensions")
        return
    
    # Check collection dimensions
    collection_dims = check_collection_dimensions()
    
    # Analyze results
    mismatches = []
    for category, dimension in collection_dims.items():
        if dimension != "unknown" and dimension != "error":
            if int(dimension) != model_dim:
                mismatches.append((category, int(dimension)))
    
    # Print summary
    print("\n=== DIMENSION CHECK SUMMARY ===")
    print(f"Current embedding model: {TEXT_EMBEDDING_MODEL}")
    print(f"Current model dimensions: {model_dim}")
    print(f"\nCollections found: {len(collection_dims)}")
    
    if mismatches:
        print(f"\nDIMENSION MISMATCHES FOUND: {len(mismatches)}/{len(collection_dims)}")
        print("\nThe following collections have dimension mismatches:")
        for category, dim in mismatches:
            print(f"  - {category}: {dim} (expected {model_dim})")
        print("\nRecommendation: Run the reembed_documents.py script to fix these mismatches")
    else:
        print("\nNo dimension mismatches found. All collections match the current embedding model.")

if __name__ == "__main__":
    main()
