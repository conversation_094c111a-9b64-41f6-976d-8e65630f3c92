<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment \nTreatment</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Labor \nLabor \n(A) \n(A)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Materials \nMaterials \n(B) \n(B)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total per cost Plot \nTotal per cost Plot \n(A+B=C) \n(A+B=C)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total Cost per \nTotal Cost per \nHectare (Cx52*) \nHectare (Cx52*)</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment | \nTreatment I</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">213 \n213</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">440 \n440</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">653 \n653</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">33,930 \n33,930</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment I \nTreatment II</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">244 \n244</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">440 \n440</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">684 \n684</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">35,555 \n35,555</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment  Il \nTreatment  III</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">294 \n294</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">800 \n800</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1094 \n1094</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">56,875 \n56,875</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Treatment IV \nTreatment IV</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">294 \n294</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">584 \n584</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">878 \n878</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">45,643 \n45,643</td>
    </tr>
  </tbody>
</table>