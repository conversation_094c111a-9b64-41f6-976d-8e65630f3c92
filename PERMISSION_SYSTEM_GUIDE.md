# Permission System Guide

## Overview

The enhanced permission system provides automatic handling of new modules and ensures that permission groups are properly maintained when new admin dashboard features are added.

## Key Features

### 1. Automatic Default Group Creation
- **Admin Group**: Full access to all system functions
- **Editor Group**: Content management and analytics access
- **Viewer Group**: Read-only access to analytics and chat history

### 2. New Module Auto-Assignment
When new modules are added to the system, permissions are automatically assigned based on predefined role mappings:

- **Admin role**: Gets access to all new modules
- **Editor role**: Gets access to modules marked with `'editor'` in `default_roles`
- **Viewer role**: Gets access to modules marked with `'viewer'` in `default_roles`

### 3. Automatic User Group Assignment
Users are automatically assigned to appropriate permission groups when:
- New users are created
- User roles are changed
- Bulk role updates are performed

## How to Add New Modules

### Step 1: Add Function to Dashboard Functions List
Edit `permissions.py` and add your new module to the `get_dashboard_functions()` list:

```python
{
    'name': 'your_new_module',
    'description': 'Description of your new module',
    'category': 'Your Category',
    'default_roles': ['admin', 'editor']  # Specify which roles should have access
}
```

### Step 2: Add Route Protection
Protect your new route with the permission decorator:

```python
from permissions import function_permission_required

@app.route('/admin/your_new_module')
@function_permission_required('your_new_module')
def your_new_module():
    # Your module code here
    pass
```

### Step 3: Sync Permissions (Automatic)
The system will automatically sync permissions on startup, but you can also:

1. **Manual Sync**: Use the "Sync New Module Permissions" button in the admin interface
2. **Check Missing**: Use the "Check Missing Permissions" button to see what's missing
3. **API Endpoints**: Call `/admin/permission_groups/sync` (POST) or `/admin/permission_groups/missing` (GET)

## Permission Resolution Order

The system checks permissions in this order:

1. **Admin role** (always has all permissions)
2. **Special cases** (like `edit_own_profile`)
3. **Individual permission overrides**
4. **Group-based permissions**
5. **Legacy dashboard permissions**
6. **Role-based defaults** (hardcoded fallbacks)
7. **Default to no access**

## API Endpoints

### Sync New Module Permissions
```
POST /admin/permission_groups/sync
```
Automatically adds missing permissions for new modules to all existing groups.

### Check Missing Permissions
```
GET /admin/permission_groups/missing
```
Returns a report of missing permissions for all groups.

### Auto-Assign User to Group
```python
from permissions import auto_assign_user_to_group
auto_assign_user_to_group(user_id, role)
```

## Configuration

### Default Role Mappings
Edit the `default_roles` array in each function definition to control which roles get access to new modules:

- `['admin']`: Only admins
- `['admin', 'editor']`: Admins and editors
- `['admin', 'editor', 'viewer']`: All roles

### Group Names
The system expects these default group names:
- `Admin`
- `Editor` 
- `Viewer`

## Troubleshooting

### Missing Permissions
If users can't access new modules:
1. Check if the function is in `get_dashboard_functions()`
2. Use "Check Missing Permissions" to see what's missing
3. Use "Sync New Module Permissions" to fix automatically
4. Verify the user's group assignment

### Group Assignment Issues
If users aren't in the right groups:
1. Check the user's role in the admin interface
2. Verify the group exists (Admin, Editor, Viewer)
3. Use the auto-assignment function manually if needed

### Permission Cache
The system caches permissions for performance. If changes don't appear immediately:
1. The cache clears automatically on permission changes
2. Users may need to log out and back in
3. Restart the application if needed

## Best Practices

1. **Always use `default_roles`** when adding new functions
2. **Test permission sync** after adding new modules
3. **Use descriptive function names** that match your route names
4. **Group related functions** by category
5. **Document permission requirements** for new features

## Migration from Old System

The new system is backward compatible with the existing permission system. Old permissions will continue to work while the new system provides enhanced functionality for new modules.
