"""
User Management System for Document Management Application

This module serves as a facade for the refactored user management system.
It re-exports functionality from the following modules:
- db: Database utilities
- user_models: User and permission data models
- auth: Authentication and session management
- permissions: Permission management
- security: Security utilities
- user_service: User CRUD operations

This facade maintains backward compatibility with existing code while
using the new modular structure internally.
"""

import os
import logging
from flask import request, session
from flask_login import current_user

# Import from refactored modules
import db
import user_models  # Import the module, not specific classes
import auth
import permissions
import security
import user_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Re-export constants
USER_DB_PATH = db.USER_DB_PATH
PROFILE_PICS_DIR = user_service.PROFILE_PICS_DIR
MAX_PROFILE_PIC_SIZE = user_service.MAX_PROFILE_PIC_SIZE
ALLOWED_EXTENSIONS = user_service.ALLOWED_EXTENSIONS
PASSWORD_RESET_EXPIRY = security.PASSWORD_RESET_EXPIRY
# Re-export functions from db module
init_user_db = db.initialize_database

# Re-export functions from auth module
authenticate_user = auth.authenticate_user
login_user = auth.login
logout_user = auth.logout
init_login_manager = auth.init_login_manager
create_user_session = auth.create_user_session
update_session_end = auth.update_session_end

# Re-export functions from security module
init_csrf = security.init_csrf
init_limiter = security.init_limiter
hash_password = security.hash_password
verify_password = security.verify_password
validate_password_complexity = security.validate_password_complexity
generate_reset_token = security.generate_reset_token
verify_reset_token = security.verify_reset_token
generate_verification_token = security.generate_verification_token
EMAIL_VERIFY_EXPIRY = security.EMAIL_VERIFY_EXPIRY
MAX_LOGIN_ATTEMPTS = security.MAX_LOGIN_ATTEMPTS
LOCKOUT_WINDOW = security.LOCKOUT_WINDOW
PASSWORD_EXPIRY_DAYS = security.PASSWORD_EXPIRY_DAYS

# Re-export functions from user_service module
get_user_by_id = user_service.get_user_by_id
get_user_by_username = user_service.get_user_by_username
get_user_by_email = user_service.get_user_by_email
get_all_users = user_service.get_all_users
register_user = user_service.register_user
log_user_activity = user_service.log_user_activity
send_verification_email = user_service.send_verification_email

# Re-export functions from permissions module
function_permission_required = permissions.function_permission_required
admin_required = permissions.admin_required
get_permission_groups = permissions.get_permission_groups
get_dashboard_functions = permissions.get_dashboard_functions
create_permission_group = permissions.create_permission_group
update_permission_group = permissions.update_permission_group
update_group_permission = permissions.update_group_permission
log_permission_change = permissions.log_permission_change

# Re-export classes from user_models module
User = user_models.User
PermissionGroup = user_models.PermissionGroup

# Stub function to maintain backward compatibility
def init_user_db_legacy():
    """Initialize the user database with all required tables."""
    try:
        # Create profile pictures directory if it doesn't exist
        os.makedirs(PROFILE_PICS_DIR, exist_ok=True)

        # Call the new initialization function
        return db.initialize_database()
    except Exception as e:
        logger.error(f"Database initialization error: {str(e)}")
        return False

# These functions are re-exported from auth and security modules
# They are defined here for backward compatibility but should not be used directly
import os
import logging
import datetime
import re
import sqlite3
import bcrypt
import uuid
from email_validator import validate_email, EmailNotValidError
from flask_login import UserMixin
from flask import request

# Note: Modules are already imported at the top of the file
# No need to re-import them here

# Constants
USER_DB_PATH = 'user_management.db'
PROFILE_PICS_DIR = '_temp/profile_pics'
PASSWORD_EXPIRY_DAYS = 90
MAX_LOGIN_ATTEMPTS = 5
EMAIL_VERIFY_EXPIRY = 86400  # 24 hours in seconds
PASSWORD_RESET_EXPIRY = 3600  # 1 hour in seconds

# Email settings
SMTP_SERVER = os.environ.get('SMTP_SERVER', 'smtp.example.com')
SMTP_PORT = int(os.environ.get('SMTP_PORT', 587))
SMTP_USERNAME = os.environ.get('SMTP_USERNAME', '')
SMTP_PASSWORD = os.environ.get('SMTP_PASSWORD', '')
SMTP_FROM_EMAIL = os.environ.get('SMTP_FROM_EMAIL', '<EMAIL>')

# Set up logging
logger = logging.getLogger(__name__)
# Custom user loader for Flask-Login
def load_user(user_id):
    """Load user by ID for Flask-Login."""
    return user_service.get_user_by_id(user_id)

class User(UserMixin):
    """User class for Flask-Login."""

    def __init__(self, user_id, username, email, role, account_status,
                 password_changed_at=None, failed_login_attempts=0,
                 profile_picture=None, full_name=None, email_verified=False,
                 group_id=None):
        self.id = user_id  # Required by Flask-Login
        self.user_id = user_id
        self.username = username
        self.email = email
        self.role = role
        self.account_status = account_status
        self.password_changed_at = password_changed_at
        self.failed_login_attempts = failed_login_attempts
        self.profile_picture = profile_picture
        self.full_name = full_name
        self.email_verified = email_verified
        self.group_id = group_id

    @property
    def is_active(self):
        """Return True if the user is active."""
        return self.account_status == 'active'

    @property
    def is_authenticated(self):
        """Return True if the user is authenticated."""
        return True

    @property
    def is_anonymous(self):
        """Return False as anonymous users aren't supported."""
        return False

    @property
    def password_expired(self):
        """Check if the user's password has expired."""
        if not self.password_changed_at:
            return True

        # Convert string to datetime if needed
        if isinstance(self.password_changed_at, str):
            password_changed_at = datetime.datetime.fromisoformat(self.password_changed_at)
        else:
            password_changed_at = self.password_changed_at

        # Calculate days since password change
        days_since_change = (datetime.datetime.now() - password_changed_at).days
        return days_since_change > PASSWORD_EXPIRY_DAYS

    def get_id(self):
        """Return the user ID as a string, required by Flask-Login."""
        return str(self.user_id)

    def has_permission(self, category, required_permission='read'):
        """Check if the user has the required permission for a category.

        Note: This method is maintained for backward compatibility.
        It now uses the dashboard permission system internally.

        Category permissions are mapped to dashboard functions as follows:
        - 'read' permission: Maps to 'view_category_{category}' function
        - 'write' permission: Maps to 'edit_category_{category}' function
        - 'admin' permission: Maps to 'manage_category_{category}' function
        """
        # Admins have all permissions
        if self.role == 'admin':
            return True

        # Map category permissions to dashboard functions
        if required_permission == 'read':
            return self.has_dashboard_permission(f'view_category_{category}')
        elif required_permission == 'write':
            return self.has_dashboard_permission(f'edit_category_{category}')
        elif required_permission == 'admin':
            return self.has_dashboard_permission(f'manage_category_{category}')

        return False

    def has_dashboard_permission(self, function_name):
        """Check if the user has permission to access a dashboard function.

        Permission checking order:
        1. Admin role (always has all permissions)
        2. Special cases (edit_own_profile)
        3. Individual permission overrides
        4. Group-based permissions
        5. Legacy dashboard permissions
        6. Role-based defaults
        7. Default to no access
        """
        # 1. Admins have all permissions
        if self.role == 'admin':
            return True

        # 2. Special case: All users can edit their own profile
        if function_name == 'edit_own_profile':
            return True

        # Check for category-based function mapping (for backward compatibility)
        # Format: view_category_{category}, edit_category_{category}, manage_category_{category}
        category_match = re.match(r'^(view|edit|manage)_category_(.+)$', function_name)
        if category_match:
            action, category = category_match.groups()
            permission_map = {
                'view': 'read',
                'edit': 'write',
                'manage': 'admin'
            }
            required_permission = permission_map.get(action, 'read')

            # Check legacy category permissions
            conn = sqlite3.connect(USER_DB_PATH)
            cursor = conn.cursor()
            cursor.execute(
                "SELECT permission FROM category_permissions WHERE user_id = ? AND category = ?",
                (self.user_id, category)
            )
            result = cursor.fetchone()
            conn.close()

            if result:
                permission = result[0]
                if required_permission == 'read':
                    return permission in ('read', 'write', 'admin')
                elif required_permission == 'write':
                    return permission in ('write', 'admin')
                elif required_permission == 'admin':
                    return permission == 'admin'

        # 3. Check for permission overrides first (highest priority)
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        cursor.execute(
            "SELECT enabled FROM permission_overrides WHERE user_id = ? AND function_name = ?",
            (self.user_id, function_name)
        )
        override_result = cursor.fetchone()

        if override_result is not None:
            conn.close()
            return bool(override_result[0])

        # 4. Check for group-based permissions if user belongs to a group
        try:
            if hasattr(self, 'group_id') and self.group_id:
                cursor.execute(
                    "SELECT enabled FROM group_permissions WHERE group_id = ? AND function_name = ?",
                    (self.group_id, function_name)
                )
                group_result = cursor.fetchone()

                if group_result is not None:
                    conn.close()
                    return bool(group_result[0])
        except Exception as e:
            logger.error(f"Error checking group permissions: {str(e)}")
            # Continue to legacy permissions if there's an error

        # 5. Fall back to legacy dashboard permissions
        cursor.execute(
            "SELECT enabled FROM dashboard_permissions WHERE user_id = ? AND function_name = ?",
            (self.user_id, function_name)
        )
        legacy_result = cursor.fetchone()
        conn.close()

        # 6. Role-based defaults if no permission record exists
        if legacy_result is None:
            # Special permissions based on role
            if self.role == 'editor' and function_name in ['upload_content', 'manage_files', 'chat_history', 'chat_sessions', 'ai_analytics', 'clean_urls']:
                return True
            elif self.role == 'viewer' and function_name in ['chat_history', 'chat_sessions', 'ai_analytics']:
                return True
            # 7. Default to no access
            return False

        # Return whether the function is enabled for this user
        return bool(legacy_result[0])

def get_user_by_id(user_id):
    """Get a user by ID."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT user_id, username, email, role, account_status,
                   password_changed_at, failed_login_attempts, profile_picture,
                   full_name, email_verified, group_id
            FROM users
            WHERE user_id = ?
        """, (user_id,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        return User(
            user_id=row['user_id'],
            username=row['username'],
            email=row['email'],
            role=row['role'],
            account_status=row['account_status'],
            password_changed_at=row['password_changed_at'],
            failed_login_attempts=row['failed_login_attempts'],
            profile_picture=row['profile_picture'],
            full_name=row['full_name'],
            email_verified=row['email_verified'],
            group_id=row['group_id']
        )
    except sqlite3.Error as e:
        logger.error(f"Database error in get_user_by_id: {str(e)}")
        return None

def get_user_by_username(username):
    """Get a user by username."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT user_id, username, email, role, account_status,
                   password_changed_at, failed_login_attempts, profile_picture,
                   full_name, email_verified, group_id
            FROM users
            WHERE username = ?
        """, (username,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        return User(
            user_id=row['user_id'],
            username=row['username'],
            email=row['email'],
            role=row['role'],
            account_status=row['account_status'],
            password_changed_at=row['password_changed_at'],
            failed_login_attempts=row['failed_login_attempts'],
            profile_picture=row['profile_picture'],
            full_name=row['full_name'],
            email_verified=row['email_verified'],
            group_id=row['group_id']
        )
    except sqlite3.Error as e:
        logger.error(f"Database error in get_user_by_username: {str(e)}")
        return None

def get_user_by_email(email):
    """Get a user by email."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT user_id, username, email, role, account_status,
                   password_changed_at, failed_login_attempts, profile_picture,
                   full_name, email_verified, group_id
            FROM users
            WHERE email = ?
        """, (email,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        return User(
            user_id=row['user_id'],
            username=row['username'],
            email=row['email'],
            role=row['role'],
            account_status=row['account_status'],
            password_changed_at=row['password_changed_at'],
            failed_login_attempts=row['failed_login_attempts'],
            profile_picture=row['profile_picture'],
            full_name=row['full_name'],
            email_verified=row['email_verified'],
            group_id=row['group_id']
        )
    except sqlite3.Error as e:
        logger.error(f"Database error in get_user_by_email: {str(e)}")
        return None

def authenticate_user(username, password):
    """Authenticate a user with username and password."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get user by username
        cursor.execute("""
            SELECT user_id, username, password_hash, email, role, account_status,
                   failed_login_attempts, last_login, password_changed_at,
                   profile_picture, full_name, email_verified, group_id
            FROM users
            WHERE username = ?
        """, (username,))

        row = cursor.fetchone()

        if not row:
            # User not found
            return None, "Invalid username or password"

        user_id = row['user_id']
        password_hash = row['password_hash']
        account_status = row['account_status']
        failed_attempts = row['failed_login_attempts']

        # Check account status
        if account_status == 'locked':
            return None, "Account is locked. Please contact an administrator."
        elif account_status == 'disabled':
            return None, "Account is disabled. Please contact an administrator."
        elif account_status == 'pending':
            return None, "Account is pending approval. Please check your email or contact an administrator."

        # Check failed login attempts
        if failed_attempts >= MAX_LOGIN_ATTEMPTS:
            # Lock the account
            cursor.execute(
                "UPDATE users SET account_status = 'locked' WHERE user_id = ?",
                (user_id,)
            )
            conn.commit()

            # Log the account lockout
            log_user_activity(
                user_id=user_id,
                action_type="account_lockout",
                details="Account locked due to too many failed login attempts",
                status="warning",
                ip_address=request.remote_addr if request else None
            )

            return None, "Account locked due to too many failed login attempts. Please contact an administrator."

        # Verify password
        if not bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8')):
            # Increment failed login attempts
            cursor.execute(
                "UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE user_id = ?",
                (user_id,)
            )
            conn.commit()

            # Log the failed login attempt
            log_user_activity(
                user_id=user_id,
                action_type="login_failed",
                details="Failed login attempt",
                status="failure",
                ip_address=request.remote_addr if request else None
            )

            return None, "Invalid username or password"

        # Authentication successful - reset failed attempts and update last login
        now = datetime.datetime.now().isoformat()
        cursor.execute(
            "UPDATE users SET failed_login_attempts = 0, last_login = ? WHERE user_id = ?",
            (now, user_id)
        )
        conn.commit()

        # Log the successful login
        log_user_activity(
            user_id=user_id,
            action_type="login",
            details="User logged in successfully",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        # Create user object
        user = User(
            user_id=row['user_id'],
            username=row['username'],
            email=row['email'],
            role=row['role'],
            account_status=row['account_status'],
            password_changed_at=row['password_changed_at'],
            failed_login_attempts=0,  # Reset to 0
            profile_picture=row['profile_picture'],
            full_name=row['full_name'],
            email_verified=row['email_verified'],
            group_id=row['group_id']
        )

        return user, None
    except sqlite3.Error as e:
        logger.error(f"Database error in authenticate_user: {str(e)}")
        return None, f"Database error: {str(e)}"
    finally:
        if 'conn' in locals():
            conn.close()

def register_user(username, email, password, role='viewer', full_name=None, require_approval=True):
    """Register a new user."""
    try:
        # Validate username (3-20 alphanumeric characters)
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
            return False, "Username must be 3-20 alphanumeric characters or underscores"

        # Validate email
        try:
            validate_email(email)
        except EmailNotValidError:
            return False, "Invalid email address"

        # Validate password complexity
        if not validate_password_complexity(password):
            return False, "Password must be at least 8 characters and include uppercase, lowercase, number, and special character"

        # Check if username or email already exists
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return False, "Username already exists"

        cursor.execute("SELECT COUNT(*) FROM users WHERE email = ?", (email,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return False, "Email already exists"

        # Hash the password
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(rounds=12)).decode('utf-8')

        # Generate email verification token
        verification_token = str(uuid.uuid4())
        verification_expiry = (datetime.datetime.now() + datetime.timedelta(seconds=EMAIL_VERIFY_EXPIRY)).isoformat()

        # Set initial account status
        account_status = 'pending' if require_approval else 'active'

        # Insert the new user
        cursor.execute('''
            INSERT INTO users (
                username, password_hash, email, role, account_status,
                verification_token, verification_token_expiry, full_name,
                password_changed_at, email_verified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            username,
            password_hash,
            email,
            role,
            account_status,
            verification_token,
            verification_expiry,
            full_name,
            datetime.datetime.now().isoformat(),  # Set password_changed_at to now
            0  # Not verified yet
        ))

        user_id = cursor.lastrowid

        # Automatically assign user to the appropriate group based on role
        if role in ['editor', 'viewer']:
            try:
                # Get the appropriate group ID
                group_name = 'Editor' if role == 'editor' else 'Viewer'
                cursor.execute("SELECT group_id FROM permission_groups WHERE name = ?", (group_name,))
                group_result = cursor.fetchone()

                if group_result and group_result[0]:
                    group_id = group_result[0]

                    # Update user with the group ID
                    cursor.execute("UPDATE users SET group_id = ? WHERE user_id = ?", (group_id, user_id))

                    # Log the group assignment
                    log_user_activity(
                        user_id=user_id,
                        action_type="group_assignment",
                        details=f"User automatically assigned to {group_name} Group during registration",
                        resource_type="permission_group",
                        resource_id=str(group_id),
                        status="success",
                        ip_address=request.remote_addr if request else None
                    )
                else:
                    logger.warning(f"Could not find {group_name} Group for automatic assignment during registration")
            except Exception as e:
                logger.error(f"Error during automatic group assignment during registration: {str(e)}")
                # Continue with the registration even if group assignment fails

        conn.commit()
        conn.close()

        # Log the registration
        log_user_activity(
            user_id=user_id,
            action_type="registration",
            details=f"User registered with role: {role}, approval required: {require_approval}",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        # Send verification email
        send_verification_email(email, username, verification_token)

        return True, user_id
    except sqlite3.Error as e:
        logger.error(f"Database error in register_user: {str(e)}")
        return False, f"Database error: {str(e)}"

def validate_password_complexity(password):
    """Validate password complexity requirements."""
    # At least 8 characters
    if len(password) < 8:
        return False

    # Check for uppercase, lowercase, number, and special character
    has_uppercase = False
    has_lowercase = False
    has_number = False
    has_special = False

    for char in password:
        if char.isupper():
            has_uppercase = True
        elif char.islower():
            has_lowercase = True
        elif char.isdigit():
            has_number = True
        elif not char.isalnum():
            has_special = True

    return has_uppercase and has_lowercase and has_number and has_special

def log_user_activity(user_id=None, action_type=None, details=None,
                     resource_type=None, resource_id=None, status="info",
                     ip_address=None):
    """Log user activity to the database."""
    try:
        # Use db module's execute_insert function which handles connections and transactions properly
        db.execute_insert('''
            INSERT INTO user_activity_logs (
                user_id, action_type, details, resource_type,
                resource_id, status, ip_address
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id, action_type, details, resource_type,
            resource_id, status, ip_address
        ), USER_DB_PATH)

        return True
    except Exception as e:
        logger.error(f"Failed to log user activity: {str(e)}")
        return False

def send_verification_email(email, username, token):
    """Send email verification link to user."""
    try:
        # Skip sending if SMTP is not configured
        if not SMTP_USERNAME or not SMTP_PASSWORD:
            logger.warning("SMTP not configured. Skipping verification email.")
            return True

        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # Create verification URL
        verification_url = f"{request.host_url.rstrip('/')}/verify_email/{token}"

        # Create email message
        message = MIMEMultipart()
        message["From"] = SMTP_FROM_EMAIL
        message["To"] = email
        message["Subject"] = "Verify Your Email Address"

        # Email body
        body = f"""
        <html>
        <body>
            <h2>Welcome to the Document Management System, {username}!</h2>
            <p>Thank you for registering. Please verify your email address by clicking the link below:</p>
            <p><a href="{verification_url}">Verify Email Address</a></p>
            <p>This link will expire in 24 hours.</p>
            <p>If you did not register for an account, please ignore this email.</p>
        </body>
        </html>
        """

        message.attach(MIMEText(body, "html"))

        # Connect to SMTP server and send email
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.send_message(message)

        logger.info(f"Verification email sent to {email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send verification email: {str(e)}")
        return False

def send_password_reset_email(email, username, token):
    """Send password reset link to user."""
    try:
        # Skip sending if SMTP is not configured
        if not SMTP_USERNAME or not SMTP_PASSWORD:
            logger.warning("SMTP not configured. Skipping password reset email.")
            return True

        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # Create reset URL
        reset_url = f"{request.host_url.rstrip('/')}/reset_password/{token}"

        # Create email message
        message = MIMEMultipart()
        message["From"] = SMTP_FROM_EMAIL
        message["To"] = email
        message["Subject"] = "Reset Your Password"

        # Email body
        body = f"""
        <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>Hello {username},</p>
            <p>We received a request to reset your password. Click the link below to reset it:</p>
            <p><a href="{reset_url}">Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you did not request a password reset, please ignore this email.</p>
        </body>
        </html>
        """

        message.attach(MIMEText(body, "html"))

        # Connect to SMTP server and send email
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.send_message(message)

        logger.info(f"Password reset email sent to {email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send password reset email: {str(e)}")
        return False

def generate_password_reset_token(user_id):
    """Generate a password reset token for a user."""
    try:
        # Generate a unique token
        token = str(uuid.uuid4())
        expiry = (datetime.datetime.now() + datetime.timedelta(seconds=PASSWORD_RESET_EXPIRY)).isoformat()

        # Store the token in the database
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        cursor.execute(
            "UPDATE users SET reset_token = ?, reset_token_expiry = ? WHERE user_id = ?",
            (token, expiry, user_id)
        )

        conn.commit()
        conn.close()

        return token
    except sqlite3.Error as e:
        logger.error(f"Failed to generate password reset token: {str(e)}")
        return None

def verify_password_reset_token(token):
    """Verify a password reset token."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Find user with this token
        cursor.execute(
            "SELECT user_id, username, email, reset_token_expiry FROM users WHERE reset_token = ?",
            (token,)
        )

        row = cursor.fetchone()
        conn.close()

        if not row:
            return None, "Invalid or expired token"

        # Check if token has expired
        expiry = datetime.datetime.fromisoformat(row['reset_token_expiry'])
        if datetime.datetime.now() > expiry:
            return None, "Token has expired"

        return row['user_id'], None
    except sqlite3.Error as e:
        logger.error(f"Failed to verify password reset token: {str(e)}")
        return None, f"Database error: {str(e)}"

def reset_password(user_id, new_password):
    """Reset a user's password."""
    try:
        # Validate password complexity
        if not validate_password_complexity(new_password):
            return False, "Password must be at least 8 characters and include uppercase, lowercase, number, and special character"

        # Hash the new password
        password_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt(rounds=12)).decode('utf-8')

        # Update the user's password
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        now = datetime.datetime.now().isoformat()
        cursor.execute(
            """UPDATE users SET
                password_hash = ?,
                reset_token = NULL,
                reset_token_expiry = NULL,
                password_changed_at = ?,
                failed_login_attempts = 0
               WHERE user_id = ?""",
            (password_hash, now, user_id)
        )

        conn.commit()
        conn.close()

        # Log the password reset
        log_user_activity(
            user_id=user_id,
            action_type="password_reset",
            details="Password reset successfully",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, None
    except sqlite3.Error as e:
        logger.error(f"Failed to reset password: {str(e)}")
        return False, f"Database error: {str(e)}"

def verify_email(token):
    """Verify a user's email address using the verification token."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Find user with this verification token
        cursor.execute(
            """SELECT user_id, username, email, verification_token_expiry, account_status
               FROM users
               WHERE verification_token = ?""",
            (token,)
        )

        row = cursor.fetchone()

        if not row:
            conn.close()
            return False, "Invalid or expired verification token"

        # Check if token has expired
        expiry = datetime.datetime.fromisoformat(row['verification_token_expiry'])
        if datetime.datetime.now() > expiry:
            conn.close()
            return False, "Verification token has expired"

        user_id = row['user_id']
        account_status = row['account_status']

        # Mark email as verified
        cursor.execute(
            """UPDATE users SET
                email_verified = 1,
                verification_token = NULL,
                verification_token_expiry = NULL
               WHERE user_id = ?""",
            (user_id,)
        )

        # If account is pending and doesn't require admin approval, activate it
        if account_status == 'pending':
            # Check if admin approval is required
            require_approval = os.getenv("REQUIRE_ADMIN_APPROVAL", "false").lower() == "true"

            if not require_approval:
                cursor.execute(
                    "UPDATE users SET account_status = 'active' WHERE user_id = ?",
                    (user_id,)
                )

        conn.commit()
        conn.close()

        # Log the email verification
        log_user_activity(
            user_id=user_id,
            action_type="email_verification",
            details="Email verified successfully",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, user_id
    except sqlite3.Error as e:
        logger.error(f"Failed to verify email: {str(e)}")
        return False, f"Database error: {str(e)}"

def get_all_users(page=1, per_page=25, search=None, sort_by='username', sort_order='asc'):
    """Get all users with pagination and search."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Base query
        query = """
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, full_name, profile_picture
            FROM users
        """

        params = []

        # Add search condition if provided
        if search:
            query += " WHERE username LIKE ? OR email LIKE ? OR full_name LIKE ?"
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        # Add sorting
        valid_sort_fields = ['username', 'email', 'role', 'account_status', 'created_at', 'last_login']
        valid_sort_orders = ['asc', 'desc']

        if sort_by not in valid_sort_fields:
            sort_by = 'username'
        if sort_order.lower() not in valid_sort_orders:
            sort_order = 'asc'

        query += f" ORDER BY {sort_by} {sort_order}"

        # Add pagination
        offset = (page - 1) * per_page
        query += " LIMIT ? OFFSET ?"
        params.extend([per_page, offset])

        # Execute query
        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Get total count for pagination
        count_query = "SELECT COUNT(*) FROM users"
        if search:
            count_query += " WHERE username LIKE ? OR email LIKE ? OR full_name LIKE ?"
            cursor.execute(count_query, [search_param, search_param, search_param])
        else:
            cursor.execute(count_query)

        total_count = cursor.fetchone()[0]
        conn.close()

        # Convert rows to dictionaries
        users = []
        for row in rows:
            users.append(dict(row))

        return {
            'users': users,
            'total': total_count,
            'page': page,
            'per_page': per_page,
            'pages': (total_count + per_page - 1) // per_page  # Ceiling division
        }
    except sqlite3.Error as e:
        logger.error(f"Failed to get users: {str(e)}")
        return None

def get_user_details(user_id):
    """Get detailed information about a user."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get user information
        cursor.execute("""
            SELECT * FROM users WHERE user_id = ?
        """, (user_id,))

        user = dict(cursor.fetchone() or {})

        if not user:
            conn.close()
            return None

        # Get user's category permissions (for backward compatibility)
        cursor.execute("""
            SELECT category, permission FROM category_permissions
            WHERE user_id = ?
        """, (user_id,))

        category_permissions = {}
        for row in cursor.fetchall():
            category_permissions[row['category']] = row['permission']

        user['permissions'] = category_permissions

        # Get user's dashboard function permissions
        cursor.execute("""
            SELECT function_name, enabled FROM dashboard_permissions
            WHERE user_id = ?
        """, (user_id,))

        dashboard_permissions = {}
        for row in cursor.fetchall():
            dashboard_permissions[row['function_name']] = bool(row['enabled'])

        # Get user's permission overrides
        cursor.execute("""
            SELECT function_name, enabled FROM permission_overrides
            WHERE user_id = ?
        """, (user_id,))

        permission_overrides = {}
        for row in cursor.fetchall():
            permission_overrides[row['function_name']] = bool(row['enabled'])

        # Get group permissions if user belongs to a group
        group_permissions = {}
        if user.get('group_id'):
            cursor.execute("""
                SELECT pg.name, pg.description,
                       gp.function_name, gp.enabled
                FROM permission_groups pg
                JOIN group_permissions gp ON pg.group_id = gp.group_id
                WHERE pg.group_id = ?
            """, (user['group_id'],))

            group_info = {}
            for row in cursor.fetchall():
                if not group_info:
                    group_info = {
                        'name': row['name'],
                        'description': row['description']
                    }
                group_permissions[row['function_name']] = bool(row['enabled'])

            user['group'] = group_info

        user['dashboard_permissions'] = dashboard_permissions
        user['permission_overrides'] = permission_overrides
        user['group_permissions'] = group_permissions

        # Get user's recent activity
        cursor.execute("""
            SELECT * FROM user_activity_logs
            WHERE user_id = ?
            ORDER BY timestamp DESC
            LIMIT 20
        """, (user_id,))

        activities = [dict(row) for row in cursor.fetchall()]
        user['recent_activity'] = activities

        conn.close()
        return user
    except sqlite3.Error as e:
        logger.error(f"Failed to get user details: {str(e)}")
        return None

def update_user(user_id, data, admin_user_id=None):
    """Update a user's information.

    If the user's role is changed, automatically assigns the user to the appropriate
    permission group and synchronizes permissions.
    """
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        # Fields that can be updated
        valid_fields = {
            'email': str,
            'role': str,
            'account_status': str,
            'full_name': str,
            'profile_picture': str
        }

        # Build update query
        updates = []
        params = []

        for field, value in data.items():
            if field in valid_fields:
                updates.append(f"{field} = ?")
                params.append(value)

        if not updates:
            conn.close()
            return False, "No valid fields to update"

        # Get current user data for comparison
        cursor.execute(
            "SELECT role, group_id FROM users WHERE user_id = ?",
            (user_id,)
        )
        current_user_data = cursor.fetchone()

        if not current_user_data:
            conn.close()
            return False, "User not found"

        current_role = current_user_data[0]
        current_group_id = current_user_data[1]

        # Execute the update
        query = f"UPDATE users SET {', '.join(updates)} WHERE user_id = ?"
        params.append(user_id)
        cursor.execute(query, params)

        # Handle role change and group assignment
        if 'role' in data and data['role'] != current_role:
            new_role = data['role']

            # Determine appropriate group based on role
            if new_role == 'admin':
                group_name = 'Admin'
            elif new_role == 'editor':
                group_name = 'Editor'
            elif new_role == 'viewer':
                group_name = 'Viewer'
            else:
                group_name = None

            if group_name:
                try:
                    # Get the group ID
                    cursor.execute("SELECT group_id FROM permission_groups WHERE name = ?", (group_name,))
                    group_result = cursor.fetchone()

                    if group_result and group_result[0]:
                        group_id = group_result[0]

                        # Only update if group is different
                        if group_id != current_group_id:
                            # Get old group name for logging
                            cursor.execute(
                                "SELECT name FROM permission_groups WHERE group_id = ?",
                                (current_group_id,)
                            )
                            old_group_result = cursor.fetchone()
                            old_group_name = old_group_result[0] if old_group_result else "None"

                            # Update user's group
                            cursor.execute("UPDATE users SET group_id = ? WHERE user_id = ?", (group_id, user_id))

                            # Log the permission change
                            log_permission_change(
                                admin_user_id=admin_user_id,
                                target_user_id=user_id,
                                change_type="group_change",
                                entity_changed="permission_group",
                                old_value=old_group_name,
                                new_value=group_name
                            )

                            # Also log to activity logs
                            log_user_activity(
                                user_id=user_id,
                                action_type="group_change",
                                details=f"User automatically assigned to {group_name} when role changed to {new_role}",
                                resource_type="permission_group",
                                resource_id=str(group_id),
                                status="success",
                                ip_address=request.remote_addr if request else None
                            )

                            # Add to the details log
                            data['group'] = group_name
                    else:
                        logger.warning(f"Could not find {group_name} for automatic assignment")
                except Exception as e:
                    logger.error(f"Error during automatic group assignment: {str(e)}")
                    # Continue with the user update even if group assignment fails

        conn.commit()

        # Log the update
        log_user_activity(
            user_id=user_id,
            action_type="user_update",
            details=f"User information updated: {', '.join(data.keys())}",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        conn.close()
        return True, None
    except sqlite3.Error as e:
        logger.error(f"Failed to update user: {str(e)}")
        return False, f"Database error: {str(e)}"

def update_user_permissions(user_id, category, permission):
    """Update a user's permission for a category.

    Note: This function is maintained for backward compatibility.
    It now updates both category permissions and the corresponding dashboard function permissions.
    """
    try:
        if permission not in ['read', 'write', 'admin', None]:
            return False, "Invalid permission level"

        # Use db module's transaction management to avoid database locks
        with db.db_transaction(USER_DB_PATH) as conn:
            cursor = conn.cursor()

            if permission is None:
                # Remove permission
                cursor.execute(
                    "DELETE FROM category_permissions WHERE user_id = ? AND category = ?",
                    (user_id, category)
                )

                # Also remove corresponding dashboard function permissions
                cursor.execute(
                    "DELETE FROM dashboard_permissions WHERE user_id = ? AND function_name IN (?, ?, ?)",
                    (user_id, f"view_category_{category}", f"edit_category_{category}", f"manage_category_{category}")
                )
            else:
                # Check if permission exists
                cursor.execute(
                    "SELECT COUNT(*) as count FROM category_permissions WHERE user_id = ? AND category = ?",
                    (user_id, category)
                )

                if cursor.fetchone()['count'] > 0:
                    # Update existing permission
                    cursor.execute(
                        "UPDATE category_permissions SET permission = ? WHERE user_id = ? AND category = ?",
                        (permission, user_id, category)
                    )
                else:
                    # Insert new permission
                    cursor.execute(
                        "INSERT INTO category_permissions (user_id, category, permission) VALUES (?, ?, ?)",
                        (user_id, category, permission)
                    )

                # Update corresponding dashboard function permissions
                # First, remove all existing permissions for this category
                cursor.execute(
                    "DELETE FROM dashboard_permissions WHERE user_id = ? AND function_name IN (?, ?, ?)",
                    (user_id, f"view_category_{category}", f"edit_category_{category}", f"manage_category_{category}")
                )

                # Then add the appropriate permissions based on the permission level
                if permission in ['read', 'write', 'admin']:
                    cursor.execute(
                        "INSERT INTO dashboard_permissions (user_id, function_name, enabled) VALUES (?, ?, 1)",
                        (user_id, f"view_category_{category}")
                    )

                if permission in ['write', 'admin']:
                    cursor.execute(
                        "INSERT INTO dashboard_permissions (user_id, function_name, enabled) VALUES (?, ?, 1)",
                        (user_id, f"edit_category_{category}")
                    )

                if permission == 'admin':
                    cursor.execute(
                        "INSERT INTO dashboard_permissions (user_id, function_name, enabled) VALUES (?, ?, 1)",
                        (user_id, f"manage_category_{category}")
                    )

        # Log the permission update - this now uses the db module's functions
        log_user_activity(
            user_id=user_id,
            action_type="permission_update",
            details=f"Permission updated for category '{category}': {permission}",
            resource_type="category",
            resource_id=category,
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, None
    except Exception as e:
        logger.error(f"Failed to update user permissions: {str(e)}")
        return False, f"Database error: {str(e)}"

def log_permission_change(admin_user_id, target_user_id, change_type, entity_changed, old_value, new_value):
    """Log a permission change to the permission_audit_logs table."""
    try:
        # Use db module's execute_insert function which handles connections and transactions properly
        db.execute_insert("""
            INSERT INTO permission_audit_logs (
                admin_user_id, target_user_id, change_type, entity_changed,
                old_value, new_value, ip_address
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            admin_user_id,
            target_user_id,
            change_type,
            entity_changed,
            old_value,
            new_value,
            request.remote_addr if request else None
        ), USER_DB_PATH)

        return True
    except Exception as e:
        logger.error(f"Failed to log permission change: {str(e)}")
        return False

def update_permission_override(user_id, function_name, enabled, admin_user_id=None):
    """Update a user's permission override for a dashboard function."""
    try:
        # Use db module's transaction management to avoid database locks
        with db.db_transaction(USER_DB_PATH) as conn:
            cursor = conn.cursor()

            # Get current override status for logging
            cursor.execute(
                "SELECT enabled FROM permission_overrides WHERE user_id = ? AND function_name = ?",
                (user_id, function_name)
            )
            result = cursor.fetchone()
            old_value = "None" if result is None else "enabled" if result['enabled'] else "disabled"

            # Check if override exists
            if result is not None:
                # Update existing override
                cursor.execute(
                    "UPDATE permission_overrides SET enabled = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND function_name = ?",
                    (1 if enabled else 0, user_id, function_name)
                )
            else:
                # Insert new override
                cursor.execute(
                    "INSERT INTO permission_overrides (user_id, function_name, enabled) VALUES (?, ?, ?)",
                    (user_id, function_name, 1 if enabled else 0)
                )

        # Log the permission override - this now uses the db module's functions
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=user_id,
            change_type="permission_override",
            entity_changed=function_name,
            old_value=old_value,
            new_value="enabled" if enabled else "disabled"
        )

        # Also log to activity logs for backward compatibility
        log_user_activity(
            user_id=user_id,
            action_type="permission_override",
            details=f"Permission override for function '{function_name}': {'enabled' if enabled else 'disabled'}",
            resource_type="dashboard_function",
            resource_id=function_name,
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, None
    except Exception as e:
        logger.error(f"Failed to update permission override: {str(e)}")
        return False, f"Database error: {str(e)}"

def update_user_group(user_id, group_id, admin_user_id=None):
    """Update a user's permission group."""
    try:
        # Use db module's transaction management to avoid database locks
        with db.db_transaction(USER_DB_PATH) as conn:
            cursor = conn.cursor()

            # Get current group for logging
            cursor.execute(
                "SELECT u.group_id, pg.name FROM users u LEFT JOIN permission_groups pg ON u.group_id = pg.group_id WHERE u.user_id = ?",
                (user_id,)
            )
            result = cursor.fetchone()
            old_group_name = result['name'] if result and result['name'] else "None"

            # Get new group name
            cursor.execute("SELECT name FROM permission_groups WHERE group_id = ?", (group_id,))
            new_group_result = cursor.fetchone()
            new_group_name = new_group_result['name'] if new_group_result else "None"

            # Update user's group
            cursor.execute("UPDATE users SET group_id = ? WHERE user_id = ?", (group_id, user_id))

        # Log the group change - this now uses the db module's functions
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=user_id,
            change_type="group_change",
            entity_changed="permission_group",
            old_value=old_group_name,
            new_value=new_group_name
        )

        # Also log to activity logs for backward compatibility
        log_user_activity(
            user_id=user_id,
            action_type="group_change",
            details=f"User group changed from '{old_group_name}' to '{new_group_name}'",
            resource_type="permission_group",
            resource_id=str(group_id),
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, None
    except Exception as e:
        logger.error(f"Failed to update user group: {str(e)}")
        return False, f"Database error: {str(e)}"

def update_dashboard_permission(user_id, function_name, enabled):
    """Update a user's permission for a dashboard function.

    Note: This method is maintained for backward compatibility.
    New code should use update_permission_override instead.
    """
    try:
        # Use db module's transaction management to avoid database locks
        with db.db_transaction(USER_DB_PATH) as conn:
            cursor = conn.cursor()

            # Check if permission exists
            cursor.execute(
                "SELECT COUNT(*) as count FROM dashboard_permissions WHERE user_id = ? AND function_name = ?",
                (user_id, function_name)
            )

            if cursor.fetchone()['count'] > 0:
                # Update existing permission
                cursor.execute(
                    "UPDATE dashboard_permissions SET enabled = ? WHERE user_id = ? AND function_name = ?",
                    (1 if enabled else 0, user_id, function_name)
                )
            else:
                # Insert new permission
                cursor.execute(
                    "INSERT INTO dashboard_permissions (user_id, function_name, enabled) VALUES (?, ?, ?)",
                    (user_id, function_name, 1 if enabled else 0)
                )

        # Log the permission update - this now uses the db module's functions
        log_user_activity(
            user_id=user_id,
            action_type="dashboard_permission_update",
            details=f"Dashboard permission updated for function '{function_name}': {'enabled' if enabled else 'disabled'}",
            resource_type="dashboard_function",
            resource_id=function_name,
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, None
    except Exception as e:
        logger.error(f"Failed to update dashboard permissions: {str(e)}")
        return False, f"Database error: {str(e)}"

def delete_user(user_id):
    """Delete a user."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        # Get user info for logging
        cursor.execute("SELECT username, email FROM users WHERE user_id = ?", (user_id,))
        user_info = cursor.fetchone()

        if not user_info:
            conn.close()
            return False, "User not found"

        # Delete user's permissions
        cursor.execute("DELETE FROM category_permissions WHERE user_id = ?", (user_id,))

        # Delete user
        cursor.execute("DELETE FROM users WHERE user_id = ?", (user_id,))

        conn.commit()

        # Log the deletion
        log_user_activity(
            user_id=None,  # User is deleted, so no user_id
            action_type="user_delete",
            details=f"User deleted: {user_info[0]} ({user_info[1]})",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        conn.close()
        return True, None
    except sqlite3.Error as e:
        logger.error(f"Failed to delete user: {str(e)}")
        return False, f"Database error: {str(e)}"

def bulk_update_users(user_ids, action, value=None, admin_user_id=None):
    """Perform bulk actions on multiple users."""
    try:
        if not user_ids:
            return False, "No users selected"

        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()

        if action == 'delete':
            # Delete users
            placeholders = ', '.join(['?'] * len(user_ids))

            # Delete permissions first (foreign key constraint)
            cursor.execute(f"DELETE FROM dashboard_permissions WHERE user_id IN ({placeholders})", user_ids)
            cursor.execute(f"DELETE FROM category_permissions WHERE user_id IN ({placeholders})", user_ids)

            # Delete users
            cursor.execute(f"DELETE FROM users WHERE user_id IN ({placeholders})", user_ids)

            details = f"Bulk deleted {len(user_ids)} users"

        elif action == 'status':
            # Update account status
            if value not in ['active', 'pending', 'locked', 'disabled']:
                conn.close()
                return False, "Invalid account status"

            placeholders = ', '.join(['?'] * len(user_ids))
            cursor.execute(f"UPDATE users SET account_status = ? WHERE user_id IN ({placeholders})", [value] + user_ids)

            details = f"Bulk updated {len(user_ids)} users to status: {value}"

        elif action == 'role':
            # Update role
            if value not in ['admin', 'editor', 'viewer']:
                conn.close()
                return False, "Invalid role"

            placeholders = ', '.join(['?'] * len(user_ids))
            cursor.execute(f"UPDATE users SET role = ? WHERE user_id IN ({placeholders})", [value] + user_ids)

            details = f"Bulk updated {len(user_ids)} users to role: {value}"

            # Automatically assign users to the corresponding permission group based on role
            if value in ['editor', 'viewer']:
                try:
                    # Get the appropriate group ID
                    group_name = 'Editor' if value == 'editor' else 'Viewer'
                    cursor.execute("SELECT group_id FROM permission_groups WHERE name = ?", (group_name,))
                    group_result = cursor.fetchone()

                    if group_result and group_result[0]:
                        group_id = group_result[0]

                        # Update all selected users with the group ID
                        cursor.execute(f"UPDATE users SET group_id = ? WHERE user_id IN ({placeholders})", [group_id] + user_ids)

                        # Log the group assignment for each user
                        for user_id in user_ids:
                            # Get old group name for logging
                            cursor.execute(
                                "SELECT pg.name FROM permission_groups pg JOIN users u ON pg.group_id = u.group_id WHERE u.user_id = ?",
                                (user_id,)
                            )
                            old_group_result = cursor.fetchone()
                            old_group_name = old_group_result[0] if old_group_result else "None"

                            # Log the permission change
                            log_permission_change(
                                admin_user_id=admin_user_id,
                                target_user_id=user_id,
                                change_type="group_change",
                                entity_changed="permission_group",
                                old_value=old_group_name,
                                new_value=group_name
                            )

                            # Also log to activity logs
                            log_user_activity(
                                user_id=user_id,
                                action_type="group_change",
                                details=f"User automatically assigned to {group_name} Group when role changed to {value}",
                                resource_type="permission_group",
                                resource_id=str(group_id),
                                status="success",
                                ip_address=request.remote_addr if request else None
                            )

                        details += f" and assigned to {group_name} Group"
                    else:
                        logger.warning(f"Could not find {group_name} Group for automatic assignment")
                except Exception as e:
                    logger.error(f"Error during automatic group assignment: {str(e)}")
                    # Continue with the role update even if group assignment fails

        else:
            conn.close()
            return False, "Invalid action"

        conn.commit()

        # Log the bulk action
        log_user_activity(
            user_id=None,
            action_type=f"bulk_{action}",
            details=details,
            status="success",
            ip_address=request.remote_addr if request else None
        )

        conn.close()
        return True, None
    except sqlite3.Error as e:
        logger.error(f"Failed to perform bulk action: {str(e)}")
        return False, f"Database error: {str(e)}"

def get_user_activity_logs(user_id=None, action_type=None, page=1, per_page=25):
    """Get user activity logs with filtering and pagination."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Base query
        query = """
            SELECT l.*, u.username
            FROM user_activity_logs l
            LEFT JOIN users u ON l.user_id = u.user_id
        """

        conditions = []
        params = []

        # Add filters
        if user_id:
            conditions.append("l.user_id = ?")
            params.append(user_id)

        if action_type:
            conditions.append("l.action_type = ?")
            params.append(action_type)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        # Add sorting and pagination
        query += " ORDER BY l.timestamp DESC LIMIT ? OFFSET ?"
        offset = (page - 1) * per_page
        params.extend([per_page, offset])

        # Execute query
        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Get total count for pagination
        count_query = "SELECT COUNT(*) FROM user_activity_logs l"
        if conditions:
            count_query += " WHERE " + " AND ".join(conditions)
            cursor.execute(count_query, params[:-2])  # Exclude pagination params
        else:
            cursor.execute(count_query)

        total_count = cursor.fetchone()[0]
        conn.close()

        # Convert rows to dictionaries
        logs = []
        for row in rows:
            logs.append(dict(row))

        return {
            'logs': logs,
            'total': total_count,
            'page': page,
            'per_page': per_page,
            'pages': (total_count + per_page - 1) // per_page  # Ceiling division
        }
    except sqlite3.Error as e:
        logger.error(f"Failed to get activity logs: {str(e)}")
        return None

def get_permission_audit_logs(admin_user_id=None, target_user_id=None, change_type=None, start_date=None, end_date=None, page=1, per_page=50):
    """Get permission audit logs with pagination and filtering."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Base query
        query = """
            SELECT pal.*,
                   admin.username as admin_username,
                   target.username as target_username
            FROM permission_audit_logs pal
            LEFT JOIN users admin ON pal.admin_user_id = admin.user_id
            LEFT JOIN users target ON pal.target_user_id = target.user_id
        """

        conditions = []
        params = []

        # Add filters
        if admin_user_id:
            conditions.append("pal.admin_user_id = ?")
            params.append(admin_user_id)

        if target_user_id:
            conditions.append("pal.target_user_id = ?")
            params.append(target_user_id)

        if change_type:
            conditions.append("pal.change_type = ?")
            params.append(change_type)

        if start_date:
            conditions.append("pal.timestamp >= ?")
            params.append(start_date)

        if end_date:
            conditions.append("pal.timestamp <= ?")
            params.append(end_date)

        # Add WHERE clause if conditions exist
        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        # Add ORDER BY
        query += " ORDER BY pal.timestamp DESC"

        # Get total count for pagination
        count_query = "SELECT COUNT(*) FROM permission_audit_logs pal"
        if conditions:
            count_query += " WHERE " + " AND ".join(conditions)

        cursor.execute(count_query, params)
        total_count = cursor.fetchone()[0]

        # Add pagination
        query += " LIMIT ? OFFSET ?"
        offset = (page - 1) * per_page
        params.extend([per_page, offset])

        # Execute query
        cursor.execute(query, params)
        logs = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return {
            'logs': logs,
            'total': total_count,
            'page': page,
            'per_page': per_page,
            'pages': (total_count + per_page - 1) // per_page  # Ceiling division
        }
    except sqlite3.Error as e:
        logger.error(f"Failed to get permission audit logs: {str(e)}")
        return None

def get_permission_groups():
    """Get all permission groups with their permissions."""
    try:
        conn = sqlite3.connect(USER_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get all groups
        cursor.execute("SELECT * FROM permission_groups ORDER BY name")
        groups = [dict(row) for row in cursor.fetchall()]

        # Get permissions for each group
        for group in groups:
            cursor.execute("""
                SELECT function_name, enabled
                FROM group_permissions
                WHERE group_id = ?
            """, (group['group_id'],))

            permissions = {}
            for row in cursor.fetchall():
                permissions[row['function_name']] = bool(row['enabled'])

            group['permissions'] = permissions

            # Get count of users in this group
            cursor.execute("SELECT COUNT(*) FROM users WHERE group_id = ?", (group['group_id'],))
            group['user_count'] = cursor.fetchone()[0]

        conn.close()
        return groups
    except sqlite3.Error as e:
        logger.error(f"Failed to get permission groups: {str(e)}")
        return []



# These functions are re-exported from auth and security modules
# They are defined here for backward compatibility but should not be used directly