"""
User Management Routes for Document Management Application

This module provides route handlers for user management functionality including:
- User authentication (login, logout)
- User registration
- Password reset
- Email verification
- User profile management
- Admin user management
"""

import os
import logging

# Set up logger
logger = logging.getLogger(__name__)
import sqlite3
import datetime
from flask import Blueprint, request, render_template, redirect, url_for, flash, jsonify, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
import user_management as um
import db  # Import the db module for database operations
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, PasswordField, SelectField, BooleanField, TextAreaField, HiddenField, EmailField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError, Regexp
from werkzeug.utils import secure_filename
from functools import wraps

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Function permission decorator
def function_permission_required(function_name):
    """Decorator to require specific dashboard function permission."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip permission check for admin dashboard
            if request.endpoint == 'admin_dashboard':
                return f(*args, **kwargs)

            # Require authentication
            if not current_user.is_authenticated:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('admin_dashboard'))

            # Check if user has permission for this function
            if not current_user.has_dashboard_permission(function_name) and current_user.role != 'admin':
                flash(f'You do not have permission to access {function_name.replace("_", " ").title()}.', 'error')
                return redirect(url_for('admin_dashboard'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Create Blueprint
user_bp = Blueprint('user', __name__, url_prefix='')

# Form classes
class LoginForm(FlaskForm):
    """Login form."""
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember = BooleanField('Remember Me')

class RegistrationForm(FlaskForm):
    """Registration form."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=20),
        Regexp('^[a-zA-Z0-9_]+$', message='Username must contain only letters, numbers, and underscores')
    ])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])
    full_name = StringField('Full Name', validators=[Length(max=100)])
    accept_tos = BooleanField('I accept the Terms of Service', validators=[DataRequired()])

class PasswordResetRequestForm(FlaskForm):
    """Password reset request form."""
    email = EmailField('Email', validators=[DataRequired(), Email()])

class PasswordResetForm(FlaskForm):
    """Password reset form."""
    password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm New Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])

class ProfileForm(FlaskForm):
    """User profile form."""
    email = EmailField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Full Name', validators=[Length(max=100)])
    profile_picture = FileField('Profile Picture', validators=[
        FileAllowed(['jpg', 'jpeg', 'png'], 'Images only!')
    ])
    current_password = PasswordField('Current Password', validators=[DataRequired()])

class PasswordChangeForm(FlaskForm):
    """Password change form."""
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    new_password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm New Password', validators=[
        DataRequired(),
        EqualTo('new_password', message='Passwords must match')
    ])

class UserForm(FlaskForm):
    """User form for admin."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=20),
        Regexp('^[a-zA-Z0-9_]+$', message='Username must contain only letters, numbers, and underscores')
    ])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Full Name', validators=[Length(max=100)])
    role = SelectField('Role', choices=[
        ('admin', 'Admin'),
        ('editor', 'Editor'),
        ('viewer', 'Viewer')
    ], validators=[DataRequired()])
    account_status = SelectField('Account Status', choices=[
        ('active', 'Active'),
        ('pending', 'Pending'),
        ('locked', 'Locked'),
        ('disabled', 'Disabled')
    ], validators=[DataRequired()])
    password = PasswordField('Password', validators=[
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        EqualTo('password', message='Passwords must match')
    ])

# Custom decorators
def admin_required(f):
    """Decorator to require admin role."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('You do not have permission to access this page.', 'error')
            return redirect(url_for('user.login'))
        return f(*args, **kwargs)
    return decorated_function

def editor_required(f):
    """Decorator to require editor or admin role."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role not in ['admin', 'editor']:
            flash('You do not have permission to access this page.', 'error')
            return redirect(url_for('user.login'))
        return f(*args, **kwargs)
    return decorated_function

# Route handlers
@user_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    # If user is already logged in, redirect to dashboard
    if current_user.is_authenticated:
        return redirect(url_for('admin_dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        username = form.username.data
        password = form.password.data
        remember = form.remember.data

        # Authenticate user
        user, error = um.authenticate_user(username, password)

        if user:
            # Check if password is expired
            if user.password_expired:
                # Store user ID in session for password change
                session['password_expired_user_id'] = user.user_id
                flash('Your password has expired. Please create a new password.', 'warning')
                return redirect(url_for('user.change_expired_password'))

            # Log in user
            login_user(user, remember=remember)

            # Store device fingerprint if available
            device_fingerprint = request.form.get('device_fingerprint')
            if device_fingerprint:
                session['device_fingerprint'] = device_fingerprint

            # Redirect to requested page or dashboard
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('admin_dashboard'))
        else:
            flash(error, 'error')

    return render_template('login.html', form=form)

@user_bp.route('/logout')
def logout():
    """Handle user logout."""
    if current_user.is_authenticated:
        # Log the logout
        um.log_user_activity(
            user_id=current_user.user_id,
            action_type="logout",
            details="User logged out",
            status="success",
            ip_address=request.remote_addr
        )

        # Clear session data
        session_id = session.get('session_id')
        if session_id:
            try:
                um.update_session_end(session_id)
            except:
                pass

        # Logout user
        logout_user()

    # Clear session
    session.clear()

    flash('You have been logged out.', 'info')
    return redirect(url_for('admin_dashboard'))

@user_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration."""
    # If user is already logged in, redirect to dashboard
    if current_user.is_authenticated:
        return redirect(url_for('admin_dashboard'))

    form = RegistrationForm()
    if form.validate_on_submit():
        username = form.username.data
        email = form.email.data
        password = form.password.data
        full_name = form.full_name.data

        # Check if admin approval is required
        require_approval = os.getenv("REQUIRE_ADMIN_APPROVAL", "false").lower() == "true"

        # Register user
        success, result = um.register_user(
            username=username,
            email=email,
            password=password,
            role='viewer',  # Default role for new users
            full_name=full_name,
            require_approval=require_approval
        )

        if success:
            flash('Registration successful! Please check your email to verify your account.', 'success')
            return redirect(url_for('user.login'))
        else:
            flash(f'Registration failed: {result}', 'error')

    return render_template('register.html', form=form)

@user_bp.route('/verify_email/<token>')
def verify_email(token):
    """Verify user email with token."""
    success, result = um.verify_email(token)

    if success:
        flash('Email verified successfully! You can now log in.', 'success')
        return redirect(url_for('user.login'))
    else:
        flash(f'Email verification failed: {result}', 'error')
        return redirect(url_for('user.login'))

@user_bp.route('/forgot_password', methods=['GET', 'POST'])
def forgot_password():
    """Handle password reset request."""
    form = PasswordResetRequestForm()

    if form.validate_on_submit():
        email = form.email.data

        # Find user by email
        user = um.get_user_by_email(email)

        if user:
            # Generate reset token
            token = um.generate_password_reset_token(user.user_id)

            if token:
                # Send reset email
                um.send_password_reset_email(user.email, user.username, token)

                flash('Password reset instructions have been sent to your email.', 'success')
                return redirect(url_for('user.login'))
        else:
            # Don't reveal that the email doesn't exist
            flash('Password reset instructions have been sent to your email if it exists in our system.', 'info')
            return redirect(url_for('user.login'))

    return render_template('forgot_password.html', form=form)

@user_bp.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Handle password reset with token."""
    # Verify token
    user_id, error = um.verify_password_reset_token(token)

    if not user_id:
        flash(f'Invalid or expired password reset link: {error}', 'error')
        return redirect(url_for('user.login'))

    form = PasswordResetForm()

    if form.validate_on_submit():
        password = form.password.data

        # Reset password
        success, error = um.reset_password(user_id, password)

        if success:
            flash('Password has been reset successfully. You can now log in with your new password.', 'success')
            return redirect(url_for('user.login'))
        else:
            flash(f'Password reset failed: {error}', 'error')

    return render_template('reset_password.html', form=form, token=token)

@user_bp.route('/change_expired_password', methods=['GET', 'POST'])
def change_expired_password():
    """Handle password change for expired passwords."""
    # Check if user has expired password
    user_id = session.get('password_expired_user_id')

    if not user_id:
        flash('Invalid request.', 'error')
        return redirect(url_for('user.login'))

    form = PasswordResetForm()

    if form.validate_on_submit():
        password = form.password.data

        # Reset password
        success, error = um.reset_password(user_id, password)

        if success:
            # Clear session
            session.pop('password_expired_user_id', None)

            flash('Password has been updated successfully. You can now log in with your new password.', 'success')
            return redirect(url_for('user.login'))
        else:
            flash(f'Password update failed: {error}', 'error')

    return render_template('change_expired_password.html', form=form)

@user_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """Handle user profile management."""
    form = ProfileForm(obj=current_user)

    if form.validate_on_submit():
        # Verify current password
        user, _ = um.authenticate_user(current_user.username, form.current_password.data)

        if not user:
            flash('Current password is incorrect.', 'error')
            return render_template('profile.html', form=form)

        # Update profile
        data = {
            'email': form.email.data,
            'full_name': form.full_name.data
        }

        # Handle profile picture upload
        if form.profile_picture.data:
            filename = secure_filename(f"{current_user.username}_{form.profile_picture.data.filename}")
            filepath = os.path.join(um.PROFILE_PICS_DIR, filename)

            # Save the file
            form.profile_picture.data.save(filepath)

            # Update profile picture path
            data['profile_picture'] = filepath

        # Update user
        success, error = um.update_user(current_user.user_id, data)

        if success:
            flash('Profile updated successfully.', 'success')
            return redirect(url_for('user.profile'))
        else:
            flash(f'Profile update failed: {error}', 'error')

    return render_template('profile.html', form=form)

@user_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Handle password change."""
    form = PasswordChangeForm()

    if form.validate_on_submit():
        # Verify current password
        user, _ = um.authenticate_user(current_user.username, form.current_password.data)

        if not user:
            flash('Current password is incorrect.', 'error')
            return render_template('change_password.html', form=form)

        # Reset password
        success, error = um.reset_password(current_user.user_id, form.new_password.data)

        if success:
            flash('Password changed successfully.', 'success')
            return redirect(url_for('user.profile'))
        else:
            flash(f'Password change failed: {error}', 'error')

    return render_template('change_password.html', form=form)

@user_bp.route('/admin/users')
@function_permission_required('user_management')
def admin_users():
    """Admin user management page."""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'username')
    sort_order = request.args.get('sort_order', 'asc')

    # Get users with pagination
    result = um.get_all_users(
        page=page,
        per_page=25,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )

    if not result:
        flash('Failed to retrieve users.', 'error')
        result = {'users': [], 'total': 0, 'page': 1, 'per_page': 25, 'pages': 1}

    return render_template(
        'admin_users.html',
        users=result['users'],
        total=result['total'],
        page=result['page'],
        per_page=result['per_page'],
        pages=result['pages'],
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )

@user_bp.route('/admin/users/<int:user_id>')
@function_permission_required('user_management')
def admin_user_details(user_id):
    """Admin user details page."""
    # Get user details
    user = um.get_user_details(user_id)

    if not user:
        flash('User not found.', 'error')
        return redirect(url_for('user.admin_users'))

    # Define dashboard functions that can have permissions assigned
    dashboard_functions = [
        {
            'id': 'upload_content',
            'name': 'Upload Content',
            'description': 'Upload PDFs or add URLs to create knowledge base content'
        },
        {
            'id': 'manage_files',
            'name': 'Manage Files',
            'description': 'View, explore, and delete uploaded PDFs and URLs'
        },
        {
            'id': 'model_settings',
            'name': 'Model Settings',
            'description': 'Configure AI models, query, and embedding settings'
        },
        {
            'id': 'chat_history',
            'name': 'Chat History',
            'description': 'View all past conversations and their sources'
        },
        {
            'id': 'chat_sessions',
            'name': 'Chat Sessions',
            'description': 'View and manage client chat sessions'
        },
        {
            'id': 'ai_analytics',
            'name': 'AI Analytics',
            'description': 'View AI performance metrics and usage statistics'
        },
        {
            'id': 'clean_urls',
            'name': 'Clean URLs',
            'description': 'Fix malformed URLs in the database'
        },
        {
            'id': 'user_management',
            'name': 'User Management',
            'description': 'Manage users, roles, and permissions for the system'
        },
        {
            'id': 'edit_own_profile',
            'name': 'Edit Own Profile',
            'description': 'Edit own user profile information'
        }
    ]

    # Get permission groups
    permission_groups = um.get_permission_groups()

    # Get categories for permissions (for backward compatibility)
    categories = current_app.config.get('CATEGORIES', [])

    return render_template(
        'admin_user_details.html',
        user=user,
        categories=categories,
        dashboard_functions=dashboard_functions,
        permission_groups=permission_groups
    )

@user_bp.route('/admin/users/new', methods=['GET', 'POST'])
def admin_new_user():
    """Admin new user page."""
    form = UserForm()

    if form.validate_on_submit():
        # Register user
        success, result = um.register_user(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data or 'TemporaryPassword123!',  # Default password if not provided
            role=form.role.data,
            full_name=form.full_name.data,
            require_approval=False  # Admin-created users are automatically approved
        )

        if success:
            flash('User created successfully.', 'success')
            return redirect(url_for('user.admin_users'))
        else:
            flash(f'User creation failed: {result}', 'error')

    return render_template('admin_new_user.html', form=form)

@user_bp.route('/admin/users/<int:user_id>/edit', methods=['GET', 'POST'])
def admin_edit_user(user_id):
    """Admin edit user page."""
    # Get user details
    user_data = um.get_user_details(user_id)

    if not user_data:
        flash('User not found.', 'error')
        return redirect(url_for('user.admin_users'))

    form = UserForm(obj=user_data)

    if form.validate_on_submit():
        # Update user
        data = {
            'email': form.email.data,
            'full_name': form.full_name.data,
            'role': form.role.data,
            'account_status': form.account_status.data
        }

        # Update user
        success, error = um.update_user(user_id, data, admin_user_id=current_user.user_id)

        if success:
            # If password was provided, update it
            if form.password.data:
                password_success, password_error = um.reset_password(user_id, form.password.data)

                if not password_success:
                    flash(f'User updated but password change failed: {password_error}', 'warning')
                    return redirect(url_for('user.admin_user_details', user_id=user_id))

            flash('User updated successfully.', 'success')
            return redirect(url_for('user.admin_user_details', user_id=user_id))
        else:
            flash(f'User update failed: {error}', 'error')

    return render_template('admin_edit_user.html', form=form, user=user_data)

@user_bp.route('/admin/users/<int:user_id>/delete', methods=['POST'])
def admin_delete_user(user_id):
    """Admin delete user."""
    # Prevent deleting self
    if user_id == current_user.user_id:
        return jsonify({'success': False, 'error': 'You cannot delete your own account.'}), 400

    # Delete user
    success, error = um.delete_user(user_id)

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': error}), 400

@user_bp.route('/admin/users/bulk_action', methods=['POST'])
def admin_bulk_action():
    """Admin bulk action on users."""
    data = request.get_json()

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    user_ids = data.get('user_ids', [])
    action = data.get('action')
    value = data.get('value')

    # Prevent actions on self
    if current_user.user_id in user_ids:
        return jsonify({'success': False, 'error': 'You cannot perform bulk actions on your own account.'}), 400

    # Perform bulk action
    success, error = um.bulk_update_users(
        user_ids=user_ids,
        action=action,
        value=value,
        admin_user_id=current_user.user_id  # Pass the admin user ID for proper audit logging
    )

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': error}), 400

@user_bp.route('/admin/users/<int:user_id>/permissions', methods=['POST'])
def admin_update_permissions(user_id):
    """Admin update user permissions (legacy route for category permissions)."""
    data = request.get_json()

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    category = data.get('category')
    permission = data.get('permission')

    if not category:
        return jsonify({'success': False, 'error': 'Category is required.'}), 400

    # Update permission
    success, error = um.update_user_permissions(user_id, category, permission)

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': error}), 400

@user_bp.route('/admin/users/<int:user_id>/dashboard_permissions', methods=['POST'])
def admin_update_dashboard_permissions(user_id):
    """Admin update dashboard function permissions (legacy route)."""
    data = request.get_json()

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    function_name = data.get('function_name')
    enabled = data.get('enabled', False)

    if not function_name:
        return jsonify({'success': False, 'error': 'Function name is required.'}), 400

    # Update dashboard permission
    success, error = um.update_dashboard_permission(user_id, function_name, enabled)

    if success:
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': error}), 400

@user_bp.route('/admin/users/<int:user_id>/permission_override', methods=['POST'])
@function_permission_required('user_management')
def admin_update_permission_override(user_id):
    """Admin update permission override for a user."""
    try:
        # Check if the request is JSON
        if not request.is_json:
            logger.error(f"Permission override request is not JSON: {request.data}")
            return jsonify({'success': False, 'error': 'Request must be JSON.'}), 400

        data = request.get_json()
        logger.info(f"Permission override request data: {data}")

        if not data:
            return jsonify({'success': False, 'error': 'No data provided.'}), 400

        function_name = data.get('function_name')
        enabled = data.get('enabled', False)

        if not function_name:
            return jsonify({'success': False, 'error': 'Function name is required.'}), 400

        # Validate user exists
        user = um.get_user_by_id(user_id)
        if not user:
            return jsonify({'success': False, 'error': f'User with ID {user_id} not found.'}), 404

        # Update permission override
        success, error = um.update_permission_override(
            user_id=user_id,
            function_name=function_name,
            enabled=enabled,
            admin_user_id=current_user.user_id
        )

        if success:
            return jsonify({'success': True})
        else:
            logger.error(f"Failed to update permission override: {error}")
            return jsonify({'success': False, 'error': error}), 400
    except Exception as e:
        logger.error(f"Exception in permission override: {str(e)}")
        return jsonify({'success': False, 'error': f'Server error: {str(e)}'}), 500

@user_bp.route('/admin/users/<int:user_id>/group', methods=['POST'])
@function_permission_required('user_management')
def admin_update_user_group(user_id):
    """Admin update user's permission group."""
    # Check if the request is JSON
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Request must be JSON.'}), 400

    try:
        data = request.get_json()
    except Exception as e:
        logger.error(f"Failed to parse JSON data: {str(e)}")
        return jsonify({'success': False, 'error': 'Invalid JSON data.'}), 400

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    # Check if user exists
    user = um.get_user_by_id(user_id)
    if not user:
        return jsonify({'success': False, 'error': f'User with ID {user_id} not found.'}), 404

    # Get group_id from request data
    group_id = data.get('group_id')

    # Convert empty string to None (for removing group assignment)
    if group_id == '':
        group_id = None

    # If group_id is provided and not None, verify the group exists
    if group_id is not None:
        try:
            conn = sqlite3.connect(um.USER_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM permission_groups WHERE group_id = ?", (group_id,))
            if cursor.fetchone()[0] == 0:
                conn.close()
                return jsonify({'success': False, 'error': f'Group with ID {group_id} not found.'}), 404
            conn.close()
        except sqlite3.Error as e:
            logger.error(f"Database error checking group existence: {str(e)}")
            return jsonify({'success': False, 'error': f'Database error: {str(e)}'}), 500

    # Update user's group
    try:
        success, error = um.update_user_group(
            user_id=user_id,
            group_id=group_id,
            admin_user_id=current_user.user_id
        )

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': error}), 400
    except Exception as e:
        logger.error(f"Error updating user group: {str(e)}")
        return jsonify({'success': False, 'error': f'Error updating user group: {str(e)}'}), 500

@user_bp.route('/admin/permission_groups/<int:group_id>/permissions', methods=['POST'])
@function_permission_required('user_management')
def admin_update_group_permission(group_id):
    """Admin update permission for a group."""
    # Check if the request is JSON
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Request must be JSON.'}), 400

    try:
        data = request.get_json()
    except Exception as e:
        logger.error(f"Failed to parse JSON data: {str(e)}")
        return jsonify({'success': False, 'error': 'Invalid JSON data.'}), 400

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    function_name = data.get('function_name')
    enabled = data.get('enabled', False)

    if not function_name:
        return jsonify({'success': False, 'error': 'Function name is required.'}), 400

    try:
        # Use the permissions module's function which now uses db module's transaction management
        success, message = um.update_group_permission(
            group_id=group_id,
            function_name=function_name,
            enabled=enabled,
            admin_user_id=current_user.user_id
        )

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': message}), 400
    except Exception as e:
        logger.error(f"Failed to update group permission: {str(e)}")
        return jsonify({'success': False, 'error': f"Database error: {str(e)}"}), 400

@user_bp.route('/admin/permission_groups/batch_update', methods=['POST'])
@function_permission_required('user_management')
def admin_batch_update_group_permissions():
    """Batch update permissions for a group."""
    # Check if the request is JSON
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Request must be JSON.'}), 400

    try:
        data = request.get_json()
    except Exception as e:
        logger.error(f"Failed to parse JSON data: {str(e)}")
        return jsonify({'success': False, 'error': 'Invalid JSON data.'}), 400

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    group_id = data.get('group_id')
    permissions = data.get('permissions', {})

    if not group_id:
        return jsonify({'success': False, 'error': 'Group ID is required.'}), 400

    if not permissions:
        return jsonify({'success': False, 'error': 'No permissions to update.'}), 400

    try:
        # First verify the group exists without a transaction
        group_result = db.execute_query(
            "SELECT name FROM permission_groups WHERE group_id = ?",
            (group_id,),
            um.USER_DB_PATH
        )

        if not group_result:
            return jsonify({'success': False, 'error': f'Group with ID {group_id} not found.'}), 404

        # Process each permission individually to avoid long-running transactions
        success_count = 0
        error_messages = []

        for function_name, enabled in permissions.items():
            try:
                # Use the permissions module's function which now uses db module's transaction management
                success, message = um.update_group_permission(
                    group_id=group_id,
                    function_name=function_name,
                    enabled=enabled,
                    admin_user_id=current_user.user_id
                )

                if success:
                    success_count += 1
                else:
                    error_messages.append(f"Failed to update {function_name}: {message}")
                    logger.warning(f"Failed to update permission {function_name} for group {group_id}: {message}")
            except Exception as e:
                error_messages.append(f"Error updating {function_name}: {str(e)}")
                logger.error(f"Exception updating permission {function_name}: {str(e)}")

        # Return results
        if success_count == len(permissions):
            return jsonify({
                'success': True,
                'message': f'Successfully updated all {success_count} permissions.'
            })
        elif success_count > 0:
            return jsonify({
                'success': True,
                'message': f'Updated {success_count} of {len(permissions)} permissions.',
                'warnings': error_messages
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update any permissions.',
                'errors': error_messages
            }), 400
    except Exception as e:
        logger.error(f"Failed to batch update group permissions: {str(e)}")
        return jsonify({'success': False, 'error': f"Database error: {str(e)}"}), 500

@user_bp.route('/admin/group_editor')
@function_permission_required('user_management')
def admin_group_editor():
    """Redirect to the enhanced permission groups page with Editor tab active."""
    flash('The Group Editor functionality has been integrated into the Permission Groups page.', 'info')
    return redirect(url_for('user.admin_permission_groups') + '#editor-group')

@user_bp.route('/admin/group_editor/data')
@function_permission_required('user_management')
def admin_group_editor_data():
    """Redirect to the enhanced permission groups page API endpoint."""
    return redirect(url_for('user.admin_permission_groups') + '?format=json')

@user_bp.route('/admin/group_editor/update_permission', methods=['POST'])
@function_permission_required('user_management')
def admin_group_editor_update_permission():
    """Redirect to the batch update endpoint for backward compatibility."""
    # Check if the request is JSON
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Request must be JSON.'}), 400

    try:
        data = request.get_json()
    except Exception as e:
        logger.error(f"Failed to parse JSON data: {str(e)}")
        return jsonify({'success': False, 'error': 'Invalid JSON data.'}), 400

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    group_id = data.get('group_id')
    function_name = data.get('function_name')
    enabled = data.get('enabled', False)

    if not group_id:
        return jsonify({'success': False, 'error': 'Group ID is required.'}), 400

    if not function_name:
        return jsonify({'success': False, 'error': 'Function name is required.'}), 400

    # Modify the request data to match the batch update format
    request.json = {
        'group_id': group_id,
        'permissions': {function_name: enabled}
    }

    # Forward to the batch update endpoint
    return admin_batch_update_group_permissions()

@user_bp.route('/admin/group_editor/save', methods=['POST'])
@function_permission_required('user_management')
def admin_group_editor_save():
    """Redirect to the batch update endpoint for backward compatibility."""
    # Forward to the batch update endpoint
    return admin_batch_update_group_permissions()

# Helper functions for group editor
def get_group_by_name(name):
    """Get a permission group by name."""
    try:
        # Use db module's execute_query function which handles connections properly
        rows = db.execute_query("""
            SELECT group_id, name, description, created_at, updated_at
            FROM permission_groups
            WHERE name = ?
        """, (name,), um.USER_DB_PATH)

        if not rows:
            return None

        row = rows[0]
        return {
            'group_id': row['group_id'],
            'name': row['name'],
            'description': row['description'],
            'created_at': row['created_at'],
            'updated_at': row['updated_at']
        }
    except Exception as e:
        logger.error(f"Database error in get_group_by_name: {str(e)}")
        return None

def create_default_group(name, description):
    """Create a default permission group."""
    try:
        # Use db module's execute_insert function which handles connections and transactions properly
        group_id = db.execute_insert(
            "INSERT INTO permission_groups (name, description) VALUES (?, ?)",
            (name, description),
            um.USER_DB_PATH
        )

        # Log the group creation
        um.log_permission_change(
            admin_user_id=current_user.user_id if current_user.is_authenticated else None,
            target_user_id=None,
            change_type="group_created",
            entity_changed="permission_group",
            old_value="None",
            new_value=name
        )

        # Return the new group
        return {
            'group_id': group_id,
            'name': name,
            'description': description,
            'created_at': datetime.datetime.now().isoformat(),
            'updated_at': datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to create default group: {str(e)}")
        return None

def get_group_permissions(group_id):
    """Get permissions for a group."""
    try:
        # Use db module's execute_query function which handles connections properly
        rows = db.execute_query("""
            SELECT function_name, enabled
            FROM group_permissions
            WHERE group_id = ?
        """, (group_id,), um.USER_DB_PATH)

        permissions = {}
        for row in rows:
            permissions[row['function_name']] = bool(row['enabled'])

        return permissions
    except Exception as e:
        logger.error(f"Database error in get_group_permissions: {str(e)}")
        return {}

@user_bp.route('/admin/activity_logs')
@function_permission_required('user_management')
def admin_activity_logs():
    """Admin activity logs page."""
    page = request.args.get('page', 1, type=int)
    user_id = request.args.get('user_id', None, type=int)
    action_type = request.args.get('action_type', None)

    # Get logs with pagination
    result = um.get_user_activity_logs(
        user_id=user_id,
        action_type=action_type,
        page=page,
        per_page=50
    )

    if not result:
        flash('Failed to retrieve activity logs.', 'error')
        result = {'logs': [], 'total': 0, 'page': 1, 'per_page': 50, 'pages': 1}

    # Get all users for the filter dropdown
    users = um.get_all_users(per_page=1000)
    user_list = users['users'] if users else []

    # Get common action types for the filter dropdown
    action_types = [
        'login', 'logout', 'password_change', 'profile_update',
        'document_upload', 'document_delete', 'chat_session',
        'group_change', 'permission_change', 'user_created'
    ]

    return render_template(
        'admin_activity_logs.html',
        logs=result['logs'],
        total=result['total'],
        page=result['page'],
        per_page=result['per_page'],
        pages=result['pages'],
        user_id=user_id,
        action_type=action_type,
        users=user_list,
        action_types=action_types
    )

@user_bp.route('/admin/permission_audit')
@function_permission_required('user_management')
def admin_permission_audit():
    """Admin permission audit logs page."""
    page = request.args.get('page', 1, type=int)
    admin_user_id = request.args.get('admin_user_id', None, type=int)
    target_user_id = request.args.get('target_user_id', None, type=int)
    change_type = request.args.get('change_type', None)
    start_date = request.args.get('start_date', None)
    end_date = request.args.get('end_date', None)

    # Get logs with pagination and filtering
    result = um.get_permission_audit_logs(
        admin_user_id=admin_user_id,
        target_user_id=target_user_id,
        change_type=change_type,
        start_date=start_date,
        end_date=end_date,
        page=page,
        per_page=50
    )

    if not result:
        flash('Failed to retrieve permission audit logs.', 'error')
        result = {'logs': [], 'total': 0, 'page': 1, 'per_page': 50, 'pages': 1}

    # Get all users for the filter dropdowns
    users = um.get_all_users(per_page=1000)
    user_list = users['users'] if users else []

    return render_template(
        'admin_permission_audit.html',
        logs=result['logs'],
        total=result['total'],
        page=result['page'],
        per_page=result['per_page'],
        pages=result['pages'],
        admin_user_id=admin_user_id,
        target_user_id=target_user_id,
        change_type=change_type,
        start_date=start_date,
        end_date=end_date,
        users=user_list,
        change_types=['group_change', 'permission_override', 'individual_permission_change']
    )

@user_bp.route('/admin/permission_groups')
@function_permission_required('user_management')
def admin_permission_groups():
    """Admin permission groups page."""
    groups = um.get_permission_groups()

    # Get dashboard functions
    dashboard_functions = get_dashboard_functions()

    # Check if this is an AJAX request
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # Check if format=json parameter is present
        if request.args.get('format') == 'json':
            return jsonify({
                'success': True,
                'groups': groups,
                'dashboard_functions': dashboard_functions
            })
        else:
            return jsonify({
                'success': True,
                'groups': groups
            })

    return render_template(
        'admin_permission_groups.html',
        groups=groups,
        dashboard_functions=dashboard_functions
    )





def get_dashboard_functions():
    """Get list of dashboard functions."""
    return [
        {
            'id': 'upload_content',
            'name': 'Upload Content',
            'description': 'Upload PDFs or add URLs to create knowledge base content'
        },
        {
            'id': 'manage_files',
            'name': 'Manage Files',
            'description': 'View, explore, and delete uploaded PDFs and URLs'
        },
        {
            'id': 'model_settings',
            'name': 'Model Settings',
            'description': 'Configure AI models, query, and embedding settings'
        },
        {
            'id': 'chat_history',
            'name': 'Chat History',
            'description': 'View all past conversations and their sources'
        },
        {
            'id': 'chat_sessions',
            'name': 'Chat Sessions',
            'description': 'View and manage client chat sessions'
        },
        {
            'id': 'ai_analytics',
            'name': 'AI Analytics',
            'description': 'View AI performance metrics and usage statistics'
        },
        {
            'id': 'clean_urls',
            'name': 'Clean URLs',
            'description': 'Fix malformed URLs in the database'
        },
        {
            'id': 'user_management',
            'name': 'User Management',
            'description': 'Manage users, roles, and permissions for the system'
        },
        {
            'id': 'edit_own_profile',
            'name': 'Edit Own Profile',
            'description': 'Edit own user profile information'
        }
    ]

@user_bp.route('/admin/permission_groups', methods=['POST'])
@function_permission_required('user_management')
def admin_create_permission_group():
    """Create a new permission group."""
    data = request.get_json()

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    name = data.get('name')
    description = data.get('description', '')

    if not name:
        return jsonify({'success': False, 'error': 'Group name is required.'}), 400

    try:
        conn = sqlite3.connect(um.USER_DB_PATH)
        cursor = conn.cursor()

        # Check if group already exists
        cursor.execute("SELECT COUNT(*) FROM permission_groups WHERE name = ?", (name,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'success': False, 'error': 'A group with this name already exists.'}), 400

        # Insert new group
        cursor.execute(
            "INSERT INTO permission_groups (name, description) VALUES (?, ?)",
            (name, description)
        )

        group_id = cursor.lastrowid
        conn.commit()

        # Log the group creation
        um.log_permission_change(
            admin_user_id=current_user.user_id,
            target_user_id=None,
            change_type="group_created",
            entity_changed="permission_group",
            old_value="None",
            new_value=name
        )

        conn.close()
        return jsonify({'success': True, 'group_id': group_id})
    except sqlite3.Error as e:
        logger.error(f"Failed to create permission group: {str(e)}")
        return jsonify({'success': False, 'error': f"Database error: {str(e)}"}), 400

@user_bp.route('/admin/permission_groups/<int:group_id>', methods=['POST'])
@function_permission_required('user_management')
def admin_update_permission_group(group_id):
    """Update an existing permission group."""
    data = request.get_json()

    if not data:
        return jsonify({'success': False, 'error': 'No data provided.'}), 400

    name = data.get('name')
    description = data.get('description', '')

    if not name:
        return jsonify({'success': False, 'error': 'Group name is required.'}), 400

    try:
        conn = sqlite3.connect(um.USER_DB_PATH)
        cursor = conn.cursor()

        # Check if group exists
        cursor.execute("SELECT name, description FROM permission_groups WHERE group_id = ?", (group_id,))
        group = cursor.fetchone()

        if not group:
            conn.close()
            return jsonify({'success': False, 'error': 'Group not found.'}), 404

        old_name, old_description = group

        # Check if new name conflicts with another group
        cursor.execute("SELECT COUNT(*) FROM permission_groups WHERE name = ? AND group_id != ?", (name, group_id))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'success': False, 'error': 'A group with this name already exists.'}), 400

        # Update group
        cursor.execute(
            "UPDATE permission_groups SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE group_id = ?",
            (name, description, group_id)
        )

        conn.commit()

        # Log the group update
        um.log_permission_change(
            admin_user_id=current_user.user_id,
            target_user_id=None,
            change_type="group_updated",
            entity_changed="permission_group",
            old_value=f"{old_name}: {old_description}",
            new_value=f"{name}: {description}"
        )

        conn.close()
        return jsonify({'success': True})
    except sqlite3.Error as e:
        logger.error(f"Failed to update permission group: {str(e)}")
        return jsonify({'success': False, 'error': f"Database error: {str(e)}"}), 400

@user_bp.route('/admin/permission_groups/<int:group_id>/delete', methods=['POST'])
@function_permission_required('user_management')
def admin_delete_permission_group(group_id):
    """Delete a permission group."""
    # Handle case where Content-Type is application/json but no body
    if request.content_type == 'application/json' and request.get_data(as_text=True).strip() == '':
        # Continue with empty data, this is fine for delete operations
        pass
    elif request.is_json:
        try:
            # Try to parse JSON if it exists
            request.get_json()
        except Exception as e:
            logger.error(f"Failed to parse JSON data: {str(e)}")
            return jsonify({'success': False, 'error': 'Invalid JSON data.'}), 400

    try:
        # Check if group exists
        conn = sqlite3.connect(um.USER_DB_PATH)
        cursor = conn.cursor()

        # Check if this is a protected group (Admin, Editor, Viewer)
        cursor.execute("SELECT name FROM permission_groups WHERE group_id = ?", (group_id,))
        group_result = cursor.fetchone()

        if not group_result:
            conn.close()
            return jsonify({'success': False, 'error': f'Group with ID {group_id} not found.'}), 404

        group_name = group_result[0]

        if group_name in ['Admin', 'Editor', 'Viewer']:
            conn.close()
            return jsonify({
                'success': False,
                'error': f'Cannot delete the {group_name} group as it is a protected system group.'
            }), 400

        # Begin transaction
        conn.execute("BEGIN TRANSACTION")

        # Get users in this group
        cursor.execute("SELECT user_id, username FROM users WHERE group_id = ?", (group_id,))
        affected_users = cursor.fetchall()

        # Remove group assignment from users
        cursor.execute("UPDATE users SET group_id = NULL WHERE group_id = ?", (group_id,))

        # Delete group permissions
        cursor.execute("DELETE FROM group_permissions WHERE group_id = ?", (group_id,))

        # Delete the group
        cursor.execute("DELETE FROM permission_groups WHERE group_id = ?", (group_id,))

        # Commit transaction
        conn.commit()

        # Log the group deletion
        um.log_permission_change(
            admin_user_id=current_user.user_id,
            target_user_id=None,
            change_type="group_deleted",
            entity_changed="permission_group",
            old_value=group_name,
            new_value="None"
        )

        # Log user group assignment changes
        for user_data in affected_users:
            user_id = user_data[0]  # Extract user_id from the tuple
            um.log_permission_change(
                admin_user_id=current_user.user_id,
                target_user_id=user_id,
                change_type="group_assignment_removed",
                entity_changed="user_group",
                old_value=group_name,
                new_value="None"
            )

        conn.close()
        return jsonify({
            'success': True,
            'message': f'Group "{group_name}" deleted successfully. {len(affected_users)} users were affected.'
        })
    except sqlite3.Error as e:
        # Rollback transaction if there was an error
        if conn:
            conn.rollback()
            conn.close()

        logger.error(f"Failed to delete permission group: {str(e)}")
        return jsonify({'success': False, 'error': f"Database error: {str(e)}"}), 500


@user_bp.route('/admin/permission_groups/sync', methods=['POST'])
@function_permission_required('user_management')
def admin_sync_permissions():
    """Sync permissions for new modules across all permission groups."""
    try:
        from permissions import sync_new_module_permissions, get_missing_permissions_for_groups

        # Get missing permissions report before sync
        missing_before = get_missing_permissions_for_groups()

        # Perform the sync
        success = sync_new_module_permissions()

        if success:
            # Get missing permissions report after sync
            missing_after = get_missing_permissions_for_groups()

            # Calculate what was added
            added_permissions = {}
            for group_name, missing_funcs in missing_before.items():
                if group_name not in missing_after:
                    added_permissions[group_name] = missing_funcs
                else:
                    added_permissions[group_name] = list(set(missing_funcs) - set(missing_after[group_name]))

            return jsonify({
                'success': True,
                'message': 'Permissions synchronized successfully',
                'added_permissions': added_permissions,
                'remaining_missing': missing_after
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to synchronize permissions'
            }), 500

    except Exception as e:
        logger.error(f"Failed to sync permissions: {str(e)}")
        return jsonify({'success': False, 'error': f"Error: {str(e)}"}), 500


@user_bp.route('/admin/permission_groups/missing', methods=['GET'])
@function_permission_required('user_management')
def admin_get_missing_permissions():
    """Get a report of missing permissions for all groups."""
    try:
        from permissions import get_missing_permissions_for_groups

        missing_permissions = get_missing_permissions_for_groups()

        return jsonify({
            'success': True,
            'missing_permissions': missing_permissions
        })

    except Exception as e:
        logger.error(f"Failed to get missing permissions: {str(e)}")
        return jsonify({'success': False, 'error': f"Error: {str(e)}"}), 500
