# Model Performance Analysis for Development Research

## Overview

The Document Management System now captures detailed information about anti-hallucination modes and chat models used in each conversation. This data is stored for development research purposes to help determine which model configurations work best under different conditions.

## What Data is Captured

### Chat History Table
Each chat interaction now stores:
- **anti_hallucination_mode**: The mode used (strict, balanced, off)
- **model_name**: The LLM model used for the conversation
- **embedding_model**: The embedding model used for document retrieval
- **vision_model**: The vision model used (if enabled)

### Analytics Table
The analytics table captures the same model information plus:
- **hallucination_detected**: Whether hallucination was detected in the response
- **processing_time**: How long the query took to process
- **answer_length**: Length of the generated answer
- **source_count**: Number of sources used in the response

## Research Questions You Can Answer

### 1. Which Anti-Hallucination Mode Works Best?
- Compare hallucination rates across different modes (strict, balanced, off)
- Analyze processing time differences between modes
- Determine which mode provides the best balance of accuracy and speed

### 2. Model Performance Comparison
- Compare different LLM models on the same queries
- Analyze which models produce longer/shorter answers
- Identify which models are fastest for your use cases

### 3. Category-Specific Performance
- Determine which models work best for specific document categories
- Identify if certain anti-hallucination modes are more effective for certain types of content

### 4. Configuration Optimization
- Find the optimal combination of model + anti-hallucination mode
- Identify configurations that minimize hallucinations while maintaining speed

## How to Access the Data

### 1. API Endpoint
```
GET /api/model-performance-analysis
```

Optional parameters:
- `start_date`: Filter results from this date (YYYY-MM-DD)
- `end_date`: Filter results to this date (YYYY-MM-DD)

Example:
```
GET /api/model-performance-analysis?start_date=2024-01-01&end_date=2024-01-31
```

### 2. Response Format
```json
{
  "success": true,
  "analysis": {
    "model_comparisons": [
      {
        "model_name": "llama3.1:8b-instruct-q4_K_M",
        "anti_hallucination_mode": "strict",
        "category": "research_papers",
        "query_count": 25,
        "avg_processing_time": 2.345,
        "avg_answer_length": 450.2,
        "avg_source_count": 3.2,
        "hallucination_count": 1,
        "hallucination_rate": 4.0
      }
    ],
    "anti_hallucination_effectiveness": [
      {
        "mode": "strict",
        "total_queries": 100,
        "total_hallucinations": 2,
        "hallucination_rate": 2.0,
        "avg_processing_time": 2.1
      }
    ],
    "category_performance": {
      "research_papers": [...],
      "technical_docs": [...]
    },
    "top_configurations": [
      {
        "model_name": "llama3.1:8b-instruct-q4_K_M",
        "anti_hallucination_mode": "strict",
        "hallucination_rate": 1.5,
        "avg_processing_time": 1.8
      }
    ],
    "summary": {
      "total_queries": 500,
      "models_tested": ["llama3.1:8b-instruct-q4_K_M", "gemma2:9b"],
      "anti_hallucination_modes_tested": ["strict", "balanced", "off"],
      "categories_tested": ["research_papers", "technical_docs"]
    }
  }
}
```

### 3. Direct Database Access
You can also query the database directly:

```sql
-- Compare hallucination rates by anti-hallucination mode
SELECT 
    anti_hallucination_mode,
    COUNT(*) as total_queries,
    SUM(CASE WHEN hallucination_detected = 1 THEN 1 ELSE 0 END) as hallucinations,
    AVG(CASE WHEN hallucination_detected = 1 THEN 1.0 ELSE 0.0 END) * 100 as hallucination_rate,
    AVG(processing_time) as avg_processing_time
FROM chat_analytics 
WHERE anti_hallucination_mode IS NOT NULL
GROUP BY anti_hallucination_mode;

-- Compare model performance
SELECT 
    model_name,
    anti_hallucination_mode,
    category,
    COUNT(*) as queries,
    AVG(processing_time) as avg_time,
    AVG(answer_length) as avg_answer_length,
    SUM(CASE WHEN hallucination_detected = 1 THEN 1 ELSE 0 END) as hallucinations
FROM chat_analytics 
GROUP BY model_name, anti_hallucination_mode, category
ORDER BY hallucinations ASC, avg_time ASC;
```

## Research Workflow

### 1. Baseline Testing
1. Set up your default configuration (model + anti-hallucination mode)
2. Run a set of test queries across different categories
3. Record the baseline performance metrics

### 2. Configuration Testing
1. Change one variable at a time (model OR anti-hallucination mode)
2. Run the same test queries
3. Compare results using the analysis API

### 3. Analysis
1. Use the `/api/model-performance-analysis` endpoint to get comprehensive data
2. Look for patterns in:
   - Hallucination rates by configuration
   - Processing time differences
   - Answer quality (length, source usage)
   - Category-specific performance

### 4. Optimization
1. Identify the best-performing configurations
2. Test edge cases with your top configurations
3. Set your production defaults based on research findings

## Key Metrics to Track

- **Hallucination Rate**: Lower is better for accuracy
- **Processing Time**: Lower is better for user experience
- **Source Count**: Higher usually indicates better grounding
- **Answer Length**: Should be appropriate for the query complexity

## Development Notes

This feature is designed for development and research purposes. The data collection helps you:

1. **Understand Model Behavior**: See how different models respond to the same queries
2. **Optimize Anti-Hallucination Settings**: Find the right balance between accuracy and creativity
3. **Category-Specific Tuning**: Optimize configurations for different types of documents
4. **Performance Monitoring**: Track how configuration changes affect system performance

The captured data provides empirical evidence to make informed decisions about your production configuration.
