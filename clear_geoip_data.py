"""
Clear GeoIP Analytics Data

This script removes all entries from the geoip_analytics table in the database.
Use this to clear test data and start fresh.

Usage:
    python clear_geoip_data.py          # Clears all data
    python clear_geoip_data.py --test   # Shows how many records would be deleted without actually deleting
"""

import os
import sqlite3
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database path - use the same as the main app
CHAT_DB_PATH = os.getenv("CHAT_DB_PATH", "chat_history.db")

def count_geoip_records():
    """Count how many records are in the geoip_analytics table."""
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()
        
        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='geoip_analytics'")
        if cursor.fetchone() is None:
            logger.info("The geoip_analytics table does not exist.")
            return 0
        
        # Count records
        cursor.execute("SELECT COUNT(*) FROM geoip_analytics")
        count = cursor.fetchone()[0]
        
        conn.close()
        return count
    except sqlite3.Error as e:
        logger.error(f"Database error: {str(e)}")
        return 0

def clear_geoip_data():
    """Delete all records from the geoip_analytics table."""
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()
        
        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='geoip_analytics'")
        if cursor.fetchone() is None:
            logger.info("The geoip_analytics table does not exist. Nothing to clear.")
            return False
        
        # Delete all records
        cursor.execute("DELETE FROM geoip_analytics")
        conn.commit()
        
        # Get number of rows affected
        rows_deleted = cursor.rowcount if cursor.rowcount >= 0 else "all"
        
        # Vacuum the database to reclaim space
        cursor.execute("VACUUM")
        conn.commit()
        
        conn.close()
        
        logger.info(f"Successfully deleted {rows_deleted} records from geoip_analytics table.")
        logger.info("Database has been vacuumed to reclaim space.")
        return True
    except sqlite3.Error as e:
        logger.error(f"Failed to clear geoip_analytics table: {str(e)}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Clear GeoIP analytics data from the database.")
    parser.add_argument("--test", action="store_true", help="Count records without deleting them")
    args = parser.parse_args()
    
    # Count records
    record_count = count_geoip_records()
    
    if args.test:
        logger.info(f"Found {record_count} records in the geoip_analytics table.")
        logger.info("Run without the --test flag to delete these records.")
    else:
        if record_count > 0:
            logger.info(f"Preparing to delete {record_count} records from the geoip_analytics table...")
            if clear_geoip_data():
                logger.info("All geolocation data has been cleared successfully.")
            else:
                logger.error("Failed to clear geolocation data.")
        else:
            logger.info("No records found in the geoip_analytics table. Nothing to clear.")
