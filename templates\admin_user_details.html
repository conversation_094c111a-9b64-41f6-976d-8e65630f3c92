{% extends "admin_base.html" %}

{% block title %}User Details{% endblock %}

{% block head %}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">User Details</h1>
            <div class="flex space-x-4">
                <a href="{{ url_for('user.admin_users') }}" class="text-blue-600 hover:underline">&larr; Back to User Management</a>
            </div>
        </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- User Profile Sidebar -->
                <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <div class="flex flex-col items-center text-center">
                        {% if user.profile_picture %}
                            <img src="{{ url_for('static', filename=user.profile_picture) }}" alt="Profile Picture" class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-md">
                        {% else %}
                            <div class="w-32 h-32 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 text-4xl font-bold border-4 border-white shadow-md">
                                {{ user.username[0].upper() }}
                            </div>
                        {% endif %}
                        <h2 class="mt-4 text-xl font-semibold">{{ user.full_name or user.username }}</h2>
                        <p class="text-gray-500">{{ user.email }}</p>

                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user.role == 'admin' %}bg-purple-100 text-purple-800
                                {% elif user.role == 'editor' %}bg-blue-100 text-blue-800
                                {% else %}bg-green-100 text-green-800{% endif %}">
                                {{ user.role.capitalize() }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user.account_status == 'active' %}bg-green-100 text-green-800
                                {% elif user.account_status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif user.account_status == 'locked' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ user.account_status.capitalize() }}
                            </span>
                        </div>
                    </div>

                    <div class="mt-6 border-t border-gray-200 pt-4">
                        <h3 class="text-lg font-semibold mb-2">Account Information</h3>
                        <ul class="space-y-2">
                            <li class="flex justify-between">
                                <span class="text-gray-600">Username:</span>
                                <span class="font-medium">{{ user.username }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">User ID:</span>
                                <span class="font-medium">{{ user.user_id }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Email Verified:</span>
                                <span class="font-medium">
                                    {% if user.email_verified %}
                                        <span class="text-green-600">Yes</span>
                                    {% else %}
                                        <span class="text-red-600">No</span>
                                    {% endif %}
                                </span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Permission Group:</span>
                                <span class="font-medium">
                                    {% if user.group %}
                                        <span class="text-blue-600">{{ user.group.name }}</span>
                                        <button type="button" class="ml-2 text-xs text-blue-500 hover:text-blue-700"
                                                onclick="showChangeGroupModal({{ user.user_id }}, '{{ user.group.name }}')">
                                            Change
                                        </button>
                                    {% else %}
                                        <span class="text-gray-500">None</span>
                                        <button type="button" class="ml-2 text-xs text-blue-500 hover:text-blue-700"
                                                onclick="showChangeGroupModal({{ user.user_id }}, null)">
                                            Assign
                                        </button>
                                    {% endif %}
                                </span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span class="font-medium">{{ user.created_at.split('T')[0] if user.created_at else 'N/A' }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Last Login:</span>
                                <span class="font-medium">{{ user.last_login.split('T')[0] if user.last_login else 'Never' }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Failed Logins:</span>
                                <span class="font-medium">{{ user.failed_login_attempts }}</span>
                            </li>
                        </ul>
                    </div>

                    <div class="mt-6 flex flex-col space-y-2">
                        <a href="{{ url_for('user.admin_edit_user', user_id=user.user_id) }}" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md text-center transition-colors">
                            Edit User
                        </a>
                        {% if user.user_id != current_user.user_id %}
                            <button type="button" onclick="confirmDeleteUser({{ user.user_id }})" class="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md text-center transition-colors">
                                Delete User
                            </button>
                        {% endif %}
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="md:col-span-2 space-y-6">
                    <!-- Dashboard Permissions -->
                    <div class="bg-white p-6 rounded-lg border border-gray-200">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">Dashboard Permissions</h3>
                            <div class="text-sm text-gray-600">
                                <span class="inline-flex items-center mr-4">
                                    <span class="w-3 h-3 inline-block bg-blue-500 rounded-full mr-1"></span> Override
                                </span>
                                <span class="inline-flex items-center mr-4">
                                    <span class="w-3 h-3 inline-block bg-green-500 rounded-full mr-1"></span> Group
                                </span>
                                <span class="inline-flex items-center">
                                    <span class="w-3 h-3 inline-block bg-gray-500 rounded-full mr-1"></span> Legacy
                                </span>
                            </div>
                        </div>

                        {% if dashboard_functions %}
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {% for function in dashboard_functions %}
                                    {% set override = user.permission_overrides.get(function.id) %}
                                    {% set group_permission = user.group_permissions.get(function.id) %}
                                    {% set legacy_permission = user.dashboard_permissions.get(function.id, false) %}

                                    {% if override is not none %}
                                        {% set permission_source = 'override' %}
                                        {% set is_enabled = override %}
                                        {% set bg_color = 'bg-blue-50' %}
                                        {% set border_color = 'border-blue-200' %}
                                    {% elif group_permission is not none and user.group %}
                                        {% set permission_source = 'group' %}
                                        {% set is_enabled = group_permission %}
                                        {% set bg_color = 'bg-green-50' %}
                                        {% set border_color = 'border-green-200' %}
                                    {% else %}
                                        {% set permission_source = 'legacy' %}
                                        {% set is_enabled = legacy_permission %}
                                        {% set bg_color = 'bg-gray-50' %}
                                        {% set border_color = 'border-gray-200' %}
                                    {% endif %}

                                    <div class="{{ bg_color }} p-4 rounded-lg border {{ border_color }}">
                                        <div class="flex items-start justify-between">
                                            <div>
                                                <h4 class="text-md font-semibold text-gray-900">{{ function.name }}</h4>
                                                <p class="text-sm text-gray-600 mt-1">{{ function.description }}</p>
                                                <div class="mt-2 text-xs">
                                                    {% if permission_source == 'override' %}
                                                        <span class="text-blue-600">Individual override</span>
                                                    {% elif permission_source == 'group' %}
                                                        <span class="text-green-600">From group: {{ user.group.name }}</span>
                                                    {% else %}
                                                        <span class="text-gray-500">Legacy permission</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <label class="inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer permission-toggle"
                                                           data-user-id="{{ user.user_id }}"
                                                           data-function-name="{{ function.id }}"
                                                           {% if is_enabled %}checked{% endif %}>
                                                    <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                                    <span class="ml-2 text-sm font-medium text-gray-900 permission-status">
                                                        {% if is_enabled %}
                                                            Enabled
                                                        {% else %}
                                                            Disabled
                                                        {% endif %}
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-gray-500">No dashboard functions available.</p>
                        {% endif %}
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white p-6 rounded-lg border border-gray-200">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">Recent Activity</h3>
                            <a href="{{ url_for('user.admin_activity_logs', user_id=user.user_id) }}" class="text-blue-600 hover:underline text-sm">View All Activity</a>
                        </div>

                        {% if user.recent_activity %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Timestamp
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Action
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Details
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for activity in user.recent_activity %}
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ activity.timestamp.split('T')[0] if activity.timestamp else 'N/A' }}
                                                    {{ activity.timestamp.split('T')[1].split('.')[0] if activity.timestamp and 'T' in activity.timestamp else '' }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {{ activity.action_type.replace('_', ' ').capitalize() }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        {% if activity.status == 'success' %}bg-green-100 text-green-800
                                                        {% elif activity.status == 'failure' %}bg-red-100 text-red-800
                                                        {% elif activity.status == 'warning' %}bg-yellow-100 text-yellow-800
                                                        {% else %}bg-blue-100 text-blue-800{% endif %}">
                                                        {{ activity.status.capitalize() }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                                    {{ activity.details }}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-gray-500">No recent activity found.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block scripts %}
    <!-- Alpine.js for dropdowns -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Change Group Modal -->
    <div id="changeGroupModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-900">Change Permission Group</h3>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeChangeGroupModal()">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="mb-4">
                <label for="groupSelect" class="block text-sm font-medium text-gray-700 mb-2">Select Group</label>
                <select id="groupSelect" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">-- No Group --</option>
                    <!-- Groups will be loaded dynamically -->
                </select>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onclick="closeChangeGroupModal()">
                    Cancel
                </button>
                <button type="button" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" onclick="saveGroupChange()">
                    Save
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for the change group modal
        let currentUserId = null;

        // Function to get CSRF token from meta tag
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        }

        // Add event listeners to permission toggles
        $(document).ready(function() {
            // Permission override toggles
            $('.permission-toggle').change(function() {
                const userId = $(this).data('user-id');
                const functionName = $(this).data('function-name');
                const enabled = $(this).prop('checked');

                // Call the update function with the toggle element
                updatePermissionOverride(userId, functionName, enabled, this);
            });

            // Dashboard permission toggles (for legacy dashboard permissions)
            $('.dashboard-permission-toggle').change(function() {
                const userId = $(this).data('user-id');
                const functionName = $(this).data('function-name');
                const enabled = $(this).prop('checked');

                // Call the update function with the toggle element
                updateDashboardPermission(userId, functionName, enabled, this);
            });
        });

        // Function to update permission overrides
        function updatePermissionOverride(userId, functionName, enabled, element) {
            console.log(`Updating permission override for user ${userId}, function ${functionName}, enabled: ${enabled}`);

            // Find the card element for visual feedback
            const card = element ? element.closest('.rounded-lg') : null;

            // Add visual feedback during update
            if (card) {
                card.classList.add('opacity-75');
                card.style.position = 'relative';

                // Add a loading spinner
                const spinner = document.createElement('div');
                spinner.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-50';
                spinner.innerHTML = '<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>';
                spinner.id = 'permission-spinner';
                card.appendChild(spinner);
            }

            fetch(`/admin/users/${userId}/permission_override`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    function_name: functionName,
                    enabled: enabled
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);

                    return null; // Return null to skip the next then block
                }

                return response.json();
            })
            .then(data => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('permission-spinner');
                    if (spinner) spinner.remove();
                }

                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: `Permission ${enabled ? 'enabled' : 'disabled'} successfully`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#10B981",
                        },
                    }).showToast();

                    // Update the label text without reloading the page
                    if (element) {
                        const statusElement = element.closest('label').querySelector('.permission-status');
                        if (statusElement) {
                            statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
                        }
                    }

                    // Change the card background to blue (override)
                    if (card) {
                        card.className = card.className.replace(/bg-\w+-50/g, 'bg-blue-50');
                        card.className = card.className.replace(/border-\w+-200/g, 'border-blue-200');

                        // Update the permission source text
                        const sourceText = card.querySelector('.text-xs span');
                        if (sourceText) {
                            sourceText.className = 'text-blue-600';
                            sourceText.textContent = 'Individual override';
                        }
                    }
                } else {
                    console.error('Permission override error:', data.error);
                    Toastify({
                        text: `Error: ${data.error}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Revert the toggle if there was an error
                    if (element) {
                        element.checked = !enabled;
                    }
                }
            })
            .catch(error => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('permission-spinner');
                    if (spinner) spinner.remove();
                }

                console.error('Permission override exception:', error);
                Toastify({
                    text: `Error: ${error.message}`,
                    duration: 3000,
                    close: true,
                    gravity: "top",
                    position: "right",
                    style: {
                        background: "#EF4444",
                    },
                }).showToast();

                // Revert the toggle if there was an error
                if (element) {
                    element.checked = !enabled;
                }
            });
        }

        // Function to show the change group modal
        function showChangeGroupModal(userId, currentGroupName) {
            currentUserId = userId;

            // Fetch available groups
            fetch('/admin/permission_groups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const groupSelect = document.getElementById('groupSelect');
                        groupSelect.innerHTML = '<option value="">-- No Group --</option>';

                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.group_id;
                            option.textContent = group.name;
                            option.selected = group.name === currentGroupName;
                            groupSelect.appendChild(option);
                        });

                        // Show the modal
                        document.getElementById('changeGroupModal').classList.remove('hidden');
                    } else {
                        Toastify({
                            text: `Error: ${data.error || 'Failed to load groups'}`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            style: {
                                background: "#EF4444",
                            },
                        }).showToast();
                    }
                })
                .catch(error => {
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                });
        }

        // Function to close the change group modal
        function closeChangeGroupModal() {
            document.getElementById('changeGroupModal').classList.add('hidden');
            currentUserId = null;
        }

        // Function to save the group change
        function saveGroupChange() {
            if (!currentUserId) return;

            const groupId = document.getElementById('groupSelect').value;

            fetch(`/admin/users/${currentUserId}/group`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    group_id: groupId || null
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                if (response.headers.get('content-type') && response.headers.get('content-type').includes('text/html')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                    return null; // Return null to skip the next then block
                }
                return response.json();
            })
            .then(data => {
                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: 'Group updated successfully',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#10B981",
                        },
                    }).showToast();

                    // Close the modal
                    closeChangeGroupModal();

                    // Reload the page to reflect changes
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    Toastify({
                        text: `Error: ${data.error}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                }
            })
            .catch(error => {
                // Check if the error is due to parsing HTML as JSON
                if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                } else {
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                }
            });
        }

        // Legacy function for dashboard permissions (kept for backward compatibility)
        function updateDashboardPermission(userId, functionName, enabled, element) {
            console.log(`Updating dashboard permission for user ${userId}, function ${functionName}, enabled: ${enabled}`);

            // Find the card element for visual feedback
            const card = element ? element.closest('.rounded-lg') : null;

            // Add visual feedback during update
            if (card) {
                card.classList.add('opacity-75');
                card.style.position = 'relative';

                // Add a loading spinner
                const spinner = document.createElement('div');
                spinner.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-50';
                spinner.innerHTML = '<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>';
                spinner.id = 'dashboard-permission-spinner';
                card.appendChild(spinner);
            }

            fetch(`/admin/users/${userId}/dashboard_permissions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    function_name: functionName,
                    enabled: enabled
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    // Remove loading spinner
                    if (card) {
                        card.classList.remove('opacity-75');
                        const spinner = document.getElementById('dashboard-permission-spinner');
                        if (spinner) spinner.remove();
                    }

                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);

                    return null; // Return null to skip the next then block
                }

                return response.json().catch(error => {
                    console.error('Error parsing JSON:', error);
                    return null;
                });
            })
            .then(data => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('dashboard-permission-spinner');
                    if (spinner) spinner.remove();
                }

                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: `Permission ${enabled ? 'enabled' : 'disabled'} successfully`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#10B981",
                        },
                    }).showToast();

                    // Update the label text without reloading the page
                    if (element) {
                        const statusElement = element.closest('label').querySelector('.dashboard-permission-status');
                        if (statusElement) {
                            statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
                        }
                    }
                } else {
                    Toastify({
                        text: `Error: ${data.error || 'Unknown error'}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Revert the toggle if there was an error
                    if (element) {
                        element.checked = !enabled;
                    }
                }
            })
            .catch(error => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('dashboard-permission-spinner');
                    if (spinner) spinner.remove();
                }

                // Check if the error is due to parsing HTML as JSON
                if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                } else {
                    console.error('Detailed error:', error);
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                }

                // Revert the toggle if there was an error
                if (element) {
                    element.checked = !enabled;
                }
            });
        }

        // Legacy function for category permissions (kept for backward compatibility)
        function updatePermission(userId, category, permission) {
            fetch(`/admin/users/${userId}/permissions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    category: category,
                    permission: permission
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);

                    return null; // Return null to skip the next then block
                }

                return response.json();
            })
            .then(data => {
                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: `Permission updated successfully`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#10B981",
                    }).showToast();

                    // Reload the page to reflect changes
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    Toastify({
                        text: `Error: ${data.error || 'Unknown error'}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();
                }
            })
            .catch(error => {
                // Check if the error is due to parsing HTML as JSON
                if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();

                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                } else {
                    console.error('Detailed error:', error);
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();
                }
            });
        }

        // Function to confirm and delete user
        function confirmDeleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                fetch(`/admin/users/${userId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCSRFToken()
                    }
                })
                .then(response => {
                    // Check if the response is HTML (likely a redirect to login page)
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('text/html')) {
                        Toastify({
                            text: 'Your session may have expired. Please refresh the page and try again.',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();

                        // Optionally redirect to login page after a delay
                        setTimeout(() => {
                            window.location.href = '/admin';
                        }, 2000);

                        return null; // Return null to skip the next then block
                    }

                    return response.json();
                })
                .then(data => {
                    if (!data) return; // Skip if we got HTML response

                    if (data.success) {
                        Toastify({
                            text: `User deleted successfully`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#10B981",
                        }).showToast();

                        // Redirect to user list
                        setTimeout(() => {
                            window.location.href = "{{ url_for('user.admin_users') }}";
                        }, 1000);
                    } else {
                        Toastify({
                            text: `Error: ${data.error || 'Unknown error'}`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();
                    }
                })
                .catch(error => {
                    // Check if the error is due to parsing HTML as JSON
                    if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                        Toastify({
                            text: 'Your session may have expired. Please refresh the page and try again.',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();

                        // Redirect to login page after a delay
                        setTimeout(() => {
                            window.location.href = '/admin';
                        }, 2000);
                    } else {
                        console.error('Detailed error:', error);
                        Toastify({
                            text: `Error: ${error.message}`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();
                    }
                });
            }
        }
    </script>
{% endblock %}
