/* 
 * Admin Interface Text Contrast Fixes
 * This CSS file ensures proper text contrast in admin pages
 * for both light and dark modes, meeting WCAG AA requirements
 */

/* 
 * Default (Light Mode) Text Colors
 * These ensure dark text on light backgrounds
 */
.bg-white .text-gray-800,
.bg-gray-50 .text-gray-800,
.bg-gray-100 .text-gray-800,
.bg-blue-50 .text-gray-800,
.bg-green-50 .text-gray-800,
.bg-yellow-50 .text-gray-800,
.bg-red-50 .text-gray-800,
.bg-indigo-50 .text-gray-800,
.bg-purple-50 .text-gray-800,
.bg-pink-50 .text-gray-800 {
    color: #1a202c !important; /* Very dark gray, almost black */
}

.bg-white .text-gray-700,
.bg-gray-50 .text-gray-700,
.bg-gray-100 .text-gray-700,
.bg-blue-50 .text-gray-700,
.bg-green-50 .text-gray-700,
.bg-yellow-50 .text-gray-700,
.bg-red-50 .text-gray-700,
.bg-indigo-50 .text-gray-700,
.bg-purple-50 .text-gray-700,
.bg-pink-50 .text-gray-700 {
    color: #2d3748 !important; /* Dark gray */
}

.bg-white .text-gray-600,
.bg-gray-50 .text-gray-600,
.bg-gray-100 .text-gray-600,
.bg-blue-50 .text-gray-600,
.bg-green-50 .text-gray-600,
.bg-yellow-50 .text-gray-600,
.bg-red-50 .text-gray-600,
.bg-indigo-50 .text-gray-600,
.bg-purple-50 .text-gray-600,
.bg-pink-50 .text-gray-600 {
    color: #4a5568 !important; /* Medium dark gray */
}

/* 
 * Dark Mode Text Colors
 * These ensure light text on dark backgrounds
 */
.light-mode .bg-white .text-gray-800,
.light-mode .bg-gray-50 .text-gray-800,
.light-mode .bg-gray-100 .text-gray-800,
.light-mode .bg-blue-50 .text-gray-800,
.light-mode .bg-green-50 .text-gray-800,
.light-mode .bg-yellow-50 .text-gray-800,
.light-mode .bg-red-50 .text-gray-800,
.light-mode .bg-indigo-50 .text-gray-800,
.light-mode .bg-purple-50 .text-gray-800,
.light-mode .bg-pink-50 .text-gray-800 {
    color: #1a202c !important; /* Very dark gray, almost black */
}

/* 
 * Card and Container Backgrounds
 * Ensure proper background colors in both modes
 */
.bg-white {
    background-color: #ffffff !important;
}

.light-mode .bg-white {
    background-color: #ffffff !important;
}

/* 
 * Table Text Colors
 * Ensure proper contrast in tables
 */
.bg-white table .text-gray-800,
.bg-gray-50 table .text-gray-800 {
    color: #1a202c !important;
}

.bg-white table .text-gray-600,
.bg-gray-50 table .text-gray-600 {
    color: #4a5568 !important;
}

/* 
 * Form Element Text Colors
 * Ensure form elements have proper contrast
 */
.bg-white input,
.bg-white select,
.bg-white textarea,
.bg-gray-50 input,
.bg-gray-50 select,
.bg-gray-50 textarea {
    color: #1a202c !important;
}

/* 
 * Link Colors
 * Ensure links have proper contrast
 */
.bg-white .text-blue-600,
.bg-gray-50 .text-blue-600,
.bg-gray-100 .text-blue-600 {
    color: #2563eb !important; /* Bright blue for better contrast */
}

.bg-white .text-green-600,
.bg-gray-50 .text-green-600,
.bg-gray-100 .text-green-600 {
    color: #059669 !important; /* Bright green for better contrast */
}

/* 
 * Badge/Tag Colors
 * Ensure badges and tags have proper contrast
 */
.bg-blue-100.text-blue-800 {
    background-color: #dbeafe !important;
    color: #1e40af !important;
}

.bg-green-100.text-green-800 {
    background-color: #d1fae5 !important;
    color: #065f46 !important;
}

.bg-yellow-100.text-yellow-800 {
    background-color: #fef3c7 !important;
    color: #92400e !important;
}

.bg-red-100.text-red-800 {
    background-color: #fee2e2 !important;
    color: #991b1b !important;
}

/* 
 * Specific fixes for admin pages
 */
.prose p, 
.prose li, 
.prose h1, 
.prose h2, 
.prose h3, 
.prose h4, 
.prose h5, 
.prose h6 {
    color: #1a202c !important;
}

/* Ensure markdown content has proper contrast */
.markdown-content p,
.markdown-content li,
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    color: #1a202c !important;
}

/* Fix for unified_config.html (model_settings.html) */
.tab-content label,
.tab-content p,
.tab-content h3,
.tab-content h4 {
    color: #1a202c !important;
}

/* Fix for analytics.html */
.bg-white canvas {
    background-color: #ffffff !important;
}
