# ERDB Document Management System

A comprehensive document management system with PDF/URL upload, CRUD capabilities, vectorized data viewing, model selection, category-based chat with citations, and chat history storage.

## Features

- **Document Upload**: Upload PDFs and scrape URLs with automatic text extraction
- **Document Management**: Organize documents by categories
- **AI-Powered Chat Interface**: Query your documents with natural language
- **Multiple Model Support**: Choose from various LLM and embedding models
- **Vision Model Integration**: Analyze images within PDFs
- **PDF Processing**: Extract text, images, tables, and links from PDFs
- **Citation Support**: Get answers with proper citations to source documents
- **Analytics**: Track usage patterns and query statistics
- **Session Management**: Maintain chat history across sessions

## System Requirements

- Python 3.8+
- [Ollama](https://ollama.ai/) for running local LLM models
- Ghostscript (for PDF image extraction)
- Tesseract OCR (optional, for OCR capabilities)

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd localairag
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Install Ollama

Follow the instructions at [ollama.ai](https://ollama.ai/) to install Ollama for your operating system.

### 4. Install Ghostscript (for PDF image extraction)

- **Windows**: Download and install from [Ghostscript Downloads](https://www.ghostscript.com/download/gsdnld.html)
- **macOS**: `brew install ghostscript`
- **Linux**: `sudo apt-get install ghostscript`

### 5. Install Tesseract OCR (Optional)

- **Windows**: Download and install from [UB Mannheim](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt-get install tesseract-ocr`

### 6. Configure Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Security settings
ANONYMIZED_TELEMETRY=False
LANGCHAIN_ENDPOINT=
OLLAMA_DISABLE_TELEMETRY=1
SECRET_KEY=your-secure-secret-key
FLASK_SECRET_KEY=your-flask-secret-key

# Paths and directories
TEMP_FOLDER=./_temp
CHROMA_PATH=./chroma
CHAT_DB_PATH=./chat_history.db
SCRAPED_DB_PATH=./scraped_pages.db
GHOSTSCRIPT_PATH=<path-to-ghostscript-executable>

# Ollama configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_API_HOST=http://localhost:11434

# Model settings
LLM_MODEL=llama3.1:8b-instruct-q4_K_M
TEXT_EMBEDDING_MODEL=mxbai-embed-large:latest
# Vision model options: llama3.2-vision:11b-instruct-q4_K_M, gemma3:4b-it-q4_K_M, or gemma3:12b-it-q4_K_M
VISION_MODEL=llama3.2-vision:11b-instruct-q4_K_M

# Feature toggles
USE_VISION_MODEL=true
USE_VISION_MODEL_DURING_EMBEDDING=true
VISION_CACHE_ENABLED=true
FILTER_PDF_IMAGES=true
SHOW_FILTERED_IMAGES=false

# Configuration settings
PDF_IMAGE_FILTER_SENSITIVITY=medium
MAX_PDF_IMAGES_TO_ANALYZE=30
ANTI_HALLUCINATION_MODE=strict
```

Replace `<path-to-ghostscript-executable>` with the path to your Ghostscript installation.

### 7. Download Required Ollama Models

```bash
# Download LLM model
ollama pull llama3.1:8b-instruct-q4_K_M

# Download embedding model
ollama pull mxbai-embed-large:latest

# Download vision model (optional)
# You can use any of these supported vision models:
ollama pull llama3.2-vision:11b-instruct-q4_K_M
# or
ollama pull gemma3:4b-it-q4_K_M
# or
ollama pull gemma3:12b-it-q4_K_M
```

## Running the Application

Start the application with:

```bash
python app.py
```

The application will be available at http://localhost:8080

## Usage Guide

### Admin Dashboard

Access the admin dashboard at http://localhost:8080/admin/dashboard

1. **Create Categories**: Create categories to organize your documents
2. **Upload Documents**: Upload PDFs or scrape URLs and assign them to categories
3. **Manage Documents**: View, delete, or update documents
4. **Configure Models**: Select LLM, embedding, and vision models

### Chat Interface

Access the chat interface at http://localhost:8080

1. **Select a Category**: Choose which category of documents to query
2. **Enter Your Name**: Provide your name for personalized responses
3. **Ask Questions**: Query your documents using natural language
4. **View Citations**: See which documents were used to answer your question
5. **Follow-up Questions**: Click on suggested follow-up questions

## Advanced Configuration

### Model Selection

You can change the default models in the admin interface at http://localhost:8080/admin/models

### Vision Model Settings

Configure vision model settings for image analysis:

1. **Select Vision Model**: Choose from supported models:
   - Llama 3.2 Vision: `llama3.2-vision:11b-instruct-q4_K_M`
   - Gemma 3 (4B): `gemma3:4b-it-q4_K_M`
   - Gemma 3 (12B): `gemma3:12b-it-q4_K_M`
2. **Enable/Disable Vision Model**: Toggle vision capabilities for chat
3. **Enable/Disable Vision During Embedding**: Toggle vision for document processing
4. **Filter Sensitivity**: Control image filtering (low, medium, high)
5. **Maximum Images**: Limit the number of images to analyze per PDF
6. **Show Filtered Images**: Option to display filtered images

## Troubleshooting

### Common Issues

1. **Ollama Connection Error**: Ensure Ollama is running with `ollama serve`
2. **PDF Processing Issues**: Verify Ghostscript is installed correctly
3. **OCR Not Working**: Check Tesseract OCR installation
4. **Model Not Found**: Ensure you've pulled the required models with Ollama

### Logs

Check the application logs for detailed error information.

## License

[Specify your license here]
