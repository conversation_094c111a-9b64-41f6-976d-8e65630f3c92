/* Dark Mode CSS for Document Management System
 * This file contains styles for dark mode that are applied across all templates
 * It works with the dark-mode, dark, and light-mode classes applied to the html element
 */

/* Base dark mode styles */
html.dark-mode, html.dark {
    color-scheme: dark;
}

/* Form elements in dark mode */
html.dark-mode input:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark-mode select:not(.form-control-dark),
html.dark-mode textarea:not(.form-control-dark),
html.dark input:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark select:not(.form-control-dark),
html.dark textarea:not(.form-control-dark) {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Dark mode form focus states */
html.dark-mode input:focus:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark-mode select:focus:not(.form-control-dark),
html.dark-mode textarea:focus:not(.form-control-dark),
html.dark input:focus:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark select:focus:not(.form-control-dark),
html.dark textarea:focus:not(.form-control-dark) {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 0.2rem rgba(235, 22, 22, 0.25);
}

/* Bootstrap form controls in dark mode */
html.dark-mode .form-control,
html.dark .form-control {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .form-control:focus,
html.dark .form-control:focus {
    background-color: var(--secondary-600);
    border-color: var(--primary-500);
    color: var(--text-primary);
}

/* Cards in dark mode */
html.dark-mode .card,
html.dark .card {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .card-header,
html.dark .card-header {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
}

html.dark-mode .card-footer,
html.dark .card-footer {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
}

/* Tables in dark mode */
html.dark-mode .table,
html.dark .table {
    color: var(--text-primary);
}

html.dark-mode .table thead th,
html.dark .table thead th {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .table-bordered,
html.dark-mode .table-bordered td,
html.dark-mode .table-bordered th,
html.dark .table-bordered,
html.dark .table-bordered td,
html.dark .table-bordered th {
    border-color: var(--secondary-500);
}

html.dark-mode .table-hover tbody tr:hover,
html.dark .table-hover tbody tr:hover {
    background-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Buttons in dark mode */
html.dark-mode .btn-outline-secondary,
html.dark .btn-outline-secondary {
    color: var(--light-500);
    border-color: var(--light-500);
}

html.dark-mode .btn-outline-secondary:hover,
html.dark .btn-outline-secondary:hover {
    background-color: var(--light-500);
    color: var(--secondary-700);
}

/* Modals in dark mode */
html.dark-mode .modal-content,
html.dark .modal-content {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .modal-header,
html.dark-mode .modal-footer,
html.dark .modal-header,
html.dark .modal-footer {
    border-color: var(--secondary-500);
}

/* Dropdowns in dark mode */
html.dark-mode .dropdown-menu,
html.dark .dropdown-menu {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .dropdown-item,
html.dark .dropdown-item {
    color: var(--text-primary);
}

html.dark-mode .dropdown-item:hover,
html.dark-mode .dropdown-item:focus,
html.dark .dropdown-item:hover,
html.dark .dropdown-item:focus {
    background-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .dropdown-divider,
html.dark .dropdown-divider {
    border-color: var(--secondary-500);
}

/* Alerts in dark mode */
html.dark-mode .alert,
html.dark .alert {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Pagination in dark mode */
html.dark-mode .page-link,
html.dark .page-link {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .page-item.active .page-link,
html.dark .page-item.active .page-link {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
    color: #fff;
}

html.dark-mode .page-item.disabled .page-link,
html.dark .page-item.disabled .page-link {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-muted);
}

/* List groups in dark mode */
html.dark-mode .list-group-item,
html.dark .list-group-item {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Tooltips in dark mode */
html.dark-mode .tooltip-inner,
html.dark .tooltip-inner {
    background-color: var(--secondary-800);
    color: var(--text-primary);
}

/* Popovers in dark mode */
html.dark-mode .popover,
html.dark .popover {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .popover-header,
html.dark .popover-header {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .popover-body,
html.dark .popover-body {
    color: var(--text-primary);
}

/* Toasts in dark mode */
html.dark-mode .toast,
html.dark .toast {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .toast-header,
html.dark .toast-header {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .toast-body,
html.dark .toast-body {
    color: var(--text-primary);
}

/* Progress bars in dark mode */
html.dark-mode .progress,
html.dark .progress {
    background-color: var(--secondary-700);
}

/* Badges in dark mode */
html.dark-mode .badge-light,
html.dark .badge-light {
    background-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Breadcrumbs in dark mode */
html.dark-mode .breadcrumb,
html.dark .breadcrumb {
    background-color: var(--secondary-700);
}

html.dark-mode .breadcrumb-item.active,
html.dark .breadcrumb-item.active {
    color: var(--text-muted);
}

/* Navs and tabs in dark mode */
html.dark-mode .nav-tabs,
html.dark .nav-tabs {
    border-color: var(--secondary-500);
}

html.dark-mode .nav-tabs .nav-link.active,
html.dark .nav-tabs .nav-link.active {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .nav-tabs .nav-link:hover,
html.dark .nav-tabs .nav-link:hover {
    border-color: var(--secondary-500);
}

/* Jumbotron in dark mode */
html.dark-mode .jumbotron,
html.dark .jumbotron {
    background-color: var(--secondary-700);
}

/* Code blocks in dark mode */
html.dark-mode pre,
html.dark-mode code,
html.dark pre,
html.dark code {
    background-color: var(--secondary-700);
    color: var(--text-primary);
}
