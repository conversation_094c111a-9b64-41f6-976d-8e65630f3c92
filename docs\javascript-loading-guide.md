# JavaScript Loading Guide - Document Management System

## Overview
This guide explains how to properly manage JavaScript file loading to prevent duplicate declarations and conflicts.

## The Problem
The error "Uncaught SyntaxError: Identifier 'DMSUtils' has already been declared" occurs when:
1. The utilities.js file is loaded multiple times on the same page
2. Templates that extend base templates include utilities.js again
3. Browser caching causes conflicts with previous versions

## Solution Implemented

### 1. IIFE (Immediately Invoked Function Expression) Protection
The utilities.js file now uses an IIFE pattern to prevent multiple declarations:

```javascript
(function() {
    'use strict';
    
    // Check if DMSUtils is already defined
    if (typeof window.DMSUtils !== 'undefined') {
        console.warn('DMSUtils already exists, skipping redefinition');
        return;
    }
    
    // Define DMSUtils...
    window.DMSUtils = {
        // ... utility functions
    };
})();
```

### 2. Script Loading Management
Base templates now include script loading management to track loaded scripts:

```javascript
// Prevent duplicate script loading
window.loadedScripts = window.loadedScripts || new Set();

function loadScriptOnce(src, callback) {
    if (window.loadedScripts.has(src)) {
        if (callback) callback();
        return;
    }
    
    const script = document.createElement('script');
    script.src = src;
    script.onload = function() {
        window.loadedScripts.add(src);
        if (callback) callback();
    };
    document.head.appendChild(script);
}
```

### 3. Template Hierarchy Rules

#### Base Templates (Include utilities.js):
- `admin_base.html` - For admin interface pages
- `chat_base.html` - For chat interface pages  
- `index.html` - For main landing page
- `client_analytics.html` - Standalone analytics page

#### Child Templates (DO NOT include utilities.js):
- `unified_config.html` (extends admin_base.html)
- `vector_data.html` (extends admin_base.html)
- `analytics.html` (extends admin_base.html)
- `upload.html` (extends admin_base.html)
- `chat_history.html` (extends admin_base.html)

## Best Practices

### 1. Template Extension Rules
- If your template extends a base template, DO NOT include utilities.js
- Only standalone templates should include utilities.js directly

### 2. Script Loading Order
1. External libraries (Bootstrap, jQuery, etc.)
2. Script loading management
3. utilities.js
4. Page-specific scripts

### 3. DMSUtils Usage
Always check if DMSUtils exists before using:

```javascript
if (typeof window.DMSUtils !== 'undefined') {
    window.DMSUtils.initDarkMode();
}
```

### 4. Adding New Templates
When creating new templates:
- If extending a base template: DO NOT include utilities.js
- If standalone: Include script loading management + utilities.js
- Add comments explaining the script loading strategy

## Troubleshooting

### Common Issues
1. **DMSUtils already declared**: Check for duplicate script inclusions
2. **DMSUtils is undefined**: Ensure utilities.js is loaded before usage
3. **Theme toggle not working**: Verify DMSUtils.initDarkMode() is called

### Debugging Steps
1. Open browser developer tools
2. Check Console for script loading warnings
3. Verify `window.loadedScripts` contains '/static/js/utilities.js'
4. Confirm `window.DMSUtils` is defined

### Quick Fixes
- Clear browser cache and hard refresh (Ctrl+Shift+R)
- Check template inheritance chain
- Verify script loading order in base templates

## File Locations
- Main utilities: `/static/js/utilities.js`
- Base templates: `/templates/admin_base.html`, `/templates/chat_base.html`
- Documentation: `/docs/javascript-loading-guide.md`
