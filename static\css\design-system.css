/* Document Management System Design System
 * A centralized set of CSS variables and utility classes
 * to ensure consistency across all templates
 * Integrated with DarkP<PERSON>p 5 Admin Dashboard Template
 */

:root {
  /* ERDB Brand Color System */
  --primary: #378C47;      /* ERDB primary dark green */
  --secondary: #0267B6;    /* ERDB secondary dark blue */
  --light: #5BA85B;        /* ERDB light green */
  --dark: #000000;         /* Dark black */
  --bg-dark: #000000;      /* Background dark */

  /* Color System - Primary Colors (ERDB Dark Green) */
  --primary-50: #e8f5ea;
  --primary-100: #c8e6cc;
  --primary-200: #a5d6aa;
  --primary-300: #81c688;
  --primary-400: #67b66f;
  --primary-500: #378C47; /* ERDB primary dark green */
  --primary-600: #2f7a3e;
  --primary-700: #266834;
  --primary-800: #1d562a;
  --primary-900: #144420;

  /* Color System - Secondary Colors (ERDB Dark Blue) */
  --secondary-50: #e6f2ff;
  --secondary-100: #b3d9ff;
  --secondary-200: #80c0ff;
  --secondary-300: #4da7ff;
  --secondary-400: #1a8eff;
  --secondary-500: #0267B6; /* ERDB secondary dark blue */
  --secondary-600: #025a9e;
  --secondary-700: #024d86;
  --secondary-800: #01406e;
  --secondary-900: #013356;

  /* Color System - Light Colors (ERDB Light Green) */
  --light-50: #f0f8f0;
  --light-100: #d4edd4;
  --light-200: #b8e2b8;
  --light-300: #9cd79c;
  --light-400: #80cc80;
  --light-500: #5BA85B; /* ERDB light green */
  --light-600: #4e8f4e;
  --light-700: #417641;
  --light-800: #345d34;
  --light-900: #274427;

  /* Color System - Success Colors */
  --success-50: #e8f5e9;
  --success-100: #c8e6c9;
  --success-200: #a5d6a7;
  --success-300: #81c784;
  --success-400: #66bb6a;
  --success-500: #4caf50;
  --success-600: #198754; /* Bootstrap success */
  --success-700: #388e3c;
  --success-800: #2e7d32;
  --success-900: #1b5e20;

  /* Color System - Danger Colors */
  --danger-50: #ffebee;
  --danger-100: #ffcdd2;
  --danger-200: #ef9a9a;
  --danger-300: #e57373;
  --danger-400: #ef5350;
  --danger-500: #f44336;
  --danger-600: #dc3545; /* Bootstrap danger */
  --danger-700: #d32f2f;
  --danger-800: #c62828;
  --danger-900: #b71c1c;

  /* Color System - Warning Colors */
  --warning-50: #fff8e1;
  --warning-100: #ffecb3;
  --warning-200: #ffe082;
  --warning-300: #ffd54f;
  --warning-400: #ffca28;
  --warning-500: #ffc107; /* Bootstrap warning */
  --warning-600: #ffb300;
  --warning-700: #ffa000;
  --warning-800: #ff8f00;
  --warning-900: #ff6f00;

  /* Color System - Info Colors */
  --info-50: #e1f5fe;
  --info-100: #b3e5fc;
  --info-200: #81d4fa;
  --info-300: #4fc3f7;
  --info-400: #29b6f6;
  --info-500: #03a9f4;
  --info-600: #0dcaf0; /* Bootstrap info */
  --info-700: #0288d1;
  --info-800: #0277bd;
  --info-900: #01579b;

  /* Spacing System (DarkPan) */
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 1rem;     /* 16px */
  --spacing-4: 1.5rem;   /* 24px */
  --spacing-5: 2rem;     /* 32px */
  --spacing-6: 3rem;     /* 48px */
  --spacing-7: 4rem;     /* 64px */
  --spacing-8: 6rem;     /* 96px */

  /* Typography (DarkPan) */
  --font-family-sans: 'Open Sans', sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Border Radius (DarkPan) */
  --border-radius-sm: 2px;
  --border-radius: 4px;
  --border-radius-lg: 6px;
  --border-radius-xl: 8px;
  --border-radius-circle: 50%;

  /* Shadows (DarkPan) */
  --shadow-sm: 0 0 5px rgba(0, 0, 0, 0.1);
  --shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 0 15px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 0 20px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 0 25px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* Z-index scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* Light Mode Theme (Default) */
:root {
  /* Background and Text Colors */
  --bg-primary: #FFFFFF;                   /* White background */
  --bg-secondary: #F3F6F9;                 /* Light gray background */
  --bg-light: #F8F9FA;                     /* Very light gray */
  --bg-card: #FFFFFF;                      /* White card background */

  --text-primary: #1F1F2B;                 /* Dark text */
  --text-secondary: #555555;               /* Medium gray text */
  --text-muted: #999999;                   /* Light gray text */

  /* Border Colors */
  --border-color: #EEEEEE;                 /* Light border */

  /* Component-specific Colors */
  --input-bg: #FFFFFF;                     /* White input background */
  --input-border: #EEEEEE;                 /* Light input border */
  --input-text: #1F1F2B;                   /* Dark input text */

  --sidebar-bg: #FFFFFF;                   /* White sidebar */
  --sidebar-text: #555555;                 /* Gray sidebar text */
  --sidebar-active-bg: var(--primary-500); /* #EB1616 - Red active item */
  --sidebar-active-text: #FFFFFF;          /* White active text */
  --sidebar-hover-bg: #F3F6F9;             /* Light hover background */

  --navbar-bg: #FFFFFF;                    /* White navbar */
  --navbar-text: #1F1F2B;                  /* Dark navbar text */

  /* Table Colors */
  --table-bg: #FFFFFF;                     /* White table background */
  --table-header-bg: #F3F6F9;              /* Light table header */
  --table-border: #EEEEEE;                 /* Light table border */

  /* Button Colors */
  --btn-primary-bg: var(--primary-500);    /* #EB1616 - Red primary button */
  --btn-primary-text: #FFFFFF;             /* White button text */

  /* Chart Colors */
  --chart-primary: var(--primary-500);     /* #EB1616 - Red chart primary */
  --chart-secondary: #555555;              /* Gray chart secondary */
}

/* Dark Mode Variables */
.dark-mode,
.dark {
  /* Background and Text Colors */
  --bg-primary: #1a1a1a;                   /* Dark background */
  --bg-secondary: #2d2d2d;                 /* Slightly lighter dark */
  --bg-light: #404040;                     /* Medium dark */
  --bg-card: #1a1a1a;                      /* Card background */

  --text-primary: #FFFFFF;                 /* White text */
  --text-secondary: var(--light-500);      /* #5BA85B - ERDB light green text */
  --text-muted: var(--light-600);          /* Muted green text */

  /* Border Colors */
  --border-color: #404040;                 /* Dark border color */

  /* Component-specific Colors */
  --input-bg: #2d2d2d;                     /* Dark input background */
  --input-border: #404040;                 /* Dark input border */
  --input-text: #FFFFFF;                   /* White input text */

  --sidebar-bg: #1a1a1a;                   /* Dark sidebar */
  --sidebar-text: var(--light-500);        /* #5BA85B - ERDB light green sidebar text */
  --sidebar-active-bg: var(--primary-500); /* #378C47 - ERDB primary green */
  --sidebar-active-text: #FFFFFF;          /* White active text */
  --sidebar-hover-bg: #2d2d2d;             /* Dark hover background */

  --navbar-bg: #1a1a1a;                    /* Dark navbar */
  --navbar-text: #FFFFFF;                  /* White navbar text */

  /* Table Colors */
  --table-bg: #1a1a1a;                     /* Dark table background */
  --table-header-bg: #2d2d2d;              /* Dark table header */
  --table-border: #404040;                 /* Dark table border */

  /* Button Colors */
  --btn-primary-bg: var(--primary-500);    /* #378C47 - ERDB primary button */
  --btn-primary-text: #FFFFFF;             /* White button text */

  /* Chart Colors */
  --chart-primary: var(--primary-500);     /* #378C47 - ERDB chart primary */
  --chart-secondary: var(--light-500);     /* #5BA85B - ERDB chart secondary */
}

/* DarkPan Utility Classes */
/* Background Colors */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-light { background-color: var(--bg-light) !important; }
.bg-card { background-color: var(--bg-card) !important; }
.bg-dark { background-color: var(--secondary-600) !important; } /* DarkPan dark background */
.bg-dark-100 { background-color: var(--secondary-500) !important; } /* Slightly lighter dark */
.bg-dark-200 { background-color: var(--secondary-600) !important; } /* DarkPan standard dark */
.bg-dark-300 { background-color: var(--secondary-700) !important; } /* Slightly darker */

/* Text Colors */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--light-500) !important; } /* DarkPan light text */
.text-white { color: #FFFFFF !important; }
.text-red { color: var(--primary-500) !important; } /* DarkPan red */

/* Border Utilities */
.border-standard { border: 1px solid var(--border-color) !important; }
.border-dark { border: 1px solid #2A2E3F !important; } /* DarkPan border */
.border-light { border: 1px solid var(--light-500) !important; } /* DarkPan light border */
.border-red { border: 1px solid var(--primary-500) !important; } /* DarkPan red border */

/* DarkPan Specific Utilities */
.rounded { border-radius: var(--border-radius) !important; }
.shadow-dark { box-shadow: 0 0 15px rgba(0, 0, 0, 0.25) !important; }
.darkpan-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-4);
}

/* DarkPan Button Styles */
.btn-darkpan-primary {
  background-color: var(--primary-500);
  color: #FFFFFF;
  border: 1px solid var(--primary-500);
}
.btn-darkpan-primary:hover {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
  color: #FFFFFF;
}

.btn-darkpan-light {
  background-color: var(--light-500);
  color: #FFFFFF;
  border: 1px solid var(--light-500);
}
.btn-darkpan-light:hover {
  background-color: var(--light-600);
  border-color: var(--light-600);
  color: #FFFFFF;
}

.btn-darkpan-dark {
  background-color: var(--secondary-600);
  color: #FFFFFF;
  border: 1px solid #2A2E3F;
}
.btn-darkpan-dark:hover {
  background-color: var(--secondary-700);
  border-color: #2A2E3F;
  color: #FFFFFF;
}

/* Responsive Breakpoints (matching Bootstrap) */
/* These are included as comments for reference
 * --breakpoint-xs: 0;
 * --breakpoint-sm: 576px;
 * --breakpoint-md: 768px;
 * --breakpoint-lg: 992px;
 * --breakpoint-xl: 1200px;
 * --breakpoint-xxl: 1400px;
 */
