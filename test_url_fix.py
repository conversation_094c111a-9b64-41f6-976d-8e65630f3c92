#!/usr/bin/env python3
"""
Test script to verify URL handling fixes work correctly.
This script tests the malformed URL detection and cleanup functionality.
"""

import os
import sys
import json
import urllib.parse
import logging

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modules
import db_content_utils as db

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_malformed_url_detection():
    """Test the malformed URL detection and cleanup functionality"""
    
    print("=== Testing Malformed URL Detection and Cleanup ===\n")
    
    # Test cases for malformed URLs
    test_cases = [
        {
            "name": "Normal URL",
            "url": "https://example.com/image.jpg",
            "expected_result": "https://example.com/image.jpg",
            "should_pass": True
        },
        {
            "name": "URL-encoded dictionary",
            "url": "%7B'url':%20'https://erdbservices.denr.gov.ph/eskris/service_requests_for_client.php?operation=insert&service_id=6&requested_iec_id=61%27,%20%27source%27:%20%27url%27,%20%27metadata%27:%20{%27index%27:%200,%20%27source%27:%20%27url_scrape%27}}",
            "expected_result": "https://erdbservices.denr.gov.ph/eskris/service_requests_for_client.php?operation=insert&service_id=6&requested_iec_id=61",
            "should_pass": True
        },
        {
            "name": "Dictionary string",
            "url": "{'url': 'https://example.com/test.pdf', 'source': 'url', 'metadata': {'index': 0, 'source': 'url_scrape'}}",
            "expected_result": "https://example.com/test.pdf",
            "should_pass": True
        },
        {
            "name": "Simple regex extraction",
            "url": "Some text with https://example.com/file.pdf embedded in it",
            "expected_result": "https://example.com/file.pdf",
            "should_pass": True
        },
        {
            "name": "Invalid URL (no protocol)",
            "url": "example.com/file.pdf",
            "expected_result": None,
            "should_pass": False
        },
        {
            "name": "Invalid dictionary (no URL key)",
            "url": "{'source': 'url', 'metadata': {'index': 0}}",
            "expected_result": None,
            "should_pass": False
        }
    ]
    
    print("Testing URL validation and extraction:\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print(f"Input: {test_case['url'][:100]}{'...' if len(test_case['url']) > 100 else ''}")
        
        # Test the URL validation logic from insert_url_content
        result = test_url_validation(test_case['url'])
        
        if test_case['should_pass']:
            if result == test_case['expected_result']:
                print(f"✓ PASS: Extracted URL: {result}")
            else:
                print(f"✗ FAIL: Expected '{test_case['expected_result']}', got '{result}'")
        else:
            if result is None:
                print(f"✓ PASS: Correctly rejected invalid URL")
            else:
                print(f"✗ FAIL: Should have rejected URL, but got: {result}")
        
        print()

def test_url_validation(content):
    """
    Test the URL validation logic from insert_url_content function.
    This replicates the validation logic to test it independently.
    """
    try:
        # Validate content - ensure it's a string
        if not isinstance(content, str):
            return None

        # Ensure URLs start with http:// or https://
        if not content.startswith(('http://', 'https://')):
            # Check for malformed URLs that might contain Python dict syntax
            if "'" in content or "{" in content or "}" in content or content.startswith("{'"):
                # Try to extract the actual URL if it's embedded in a dictionary-like string
                import re
                
                # First try to extract URL from dictionary-like string
                url_match = re.search(r'https?://[^\s\'"}]+', content)
                if url_match:
                    extracted_url = url_match.group(0)
                    return extracted_url
                else:
                    # If no URL found, try to parse as JSON and extract URL
                    try:
                        # Handle URL-encoded content
                        decoded_content = urllib.parse.unquote(content)
                        
                        # Try to parse as JSON
                        if decoded_content.startswith('{') and decoded_content.endswith('}'):
                            parsed_dict = json.loads(decoded_content)
                            if isinstance(parsed_dict, dict) and 'url' in parsed_dict:
                                extracted_url = parsed_dict['url']
                                if isinstance(extracted_url, str) and extracted_url.startswith(('http://', 'https://')):
                                    return extracted_url
                                else:
                                    return None
                            else:
                                return None
                        else:
                            return None
                    except (json.JSONDecodeError, ValueError) as e:
                        return None
            else:
                return None
        
        # If we get here, it's a valid URL
        return content
        
    except Exception as e:
        logger.error(f"Error in URL validation: {str(e)}")
        return None

def test_database_cleanup():
    """Test the database cleanup functionality"""
    
    print("=== Testing Database Cleanup Functionality ===\n")
    
    try:
        # Run the cleanup function
        results = db.clean_malformed_urls()
        
        print("Cleanup Results:")
        print(f"  Fixed URLs: {results.get('fixed_count', 0)}")
        print(f"  Deleted URLs: {results.get('deleted_count', 0)}")
        print(f"  Total processed: {results.get('total_processed', 0)}")
        
        if 'error' in results:
            print(f"  Error: {results['error']}")
        else:
            print("  ✓ Cleanup completed successfully")
            
    except Exception as e:
        print(f"  ✗ Error during cleanup: {str(e)}")

def main():
    """Main test function"""
    print("URL Handling Fix Test Suite")
    print("=" * 50)
    print()
    
    # Test URL validation and extraction
    test_malformed_url_detection()
    
    # Test database cleanup
    test_database_cleanup()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")

if __name__ == "__main__":
    main()
