"""
Geolocation utilities for the application.

This module provides functions for obtaining and processing geolocation data
using MaxMind's GeoLite2 Web Service API.

Development Mode Usage:
-----------------------
To enable development mode and test geolocation features locally:

1. Set environment variables in your .env file or environment:
   - DEV_MODE=true
   - TEST_IP=<ip_address>  (e.g., TEST_IP=*******)
   - DEV_LOG_LEVEL=DEBUG  (optional, for more detailed logging)

2. Use the test_geolocation() function to test with specific IPs:
   ```python
   from geo_utils import test_geolocation

   # Test with Google's DNS server
   result = test_geolocation("*******")
   print(result)
   ```

3. Get information about the current development environment:
   ```python
   from geo_utils import get_development_info

   info = get_development_info()
   print(info)
   ```

4. When running the application in development mode with TEST_IP set,
   all geolocation lookups will use the specified test IP instead of
   the actual client IP.

MaxMind Configuration:
---------------------
To use MaxMind's GeoLite2 Web Service, you need to set the following environment variables:
   - MAXMIND_ACCOUNT_ID: Your MaxMind account ID
   - MAXMIND_LICENSE_KEY: Your MaxMind license key

You can sign up for a free GeoLite2 account at: https://www.maxmind.com/en/geolite2/signup
After signing up, generate a license key at: https://www.maxmind.com/en/accounts/current/license-key

Note: MaxMind's GeoLite2 Web Service has a limit of 1000 requests per day for free accounts.
The implementation includes caching to reduce the number of API calls.
"""

import os
import logging
import requests
import time
import random
import base64
import ipaddress
import re
from typing import Dict, Any, Optional, Tuple
from flask import request
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
PRIVACY_NOTICE_SHOWN = os.getenv("PRIVACY_NOTICE_SHOWN", "true").lower() == "true"
GEOLOCATION_ENABLED = os.getenv("GEOLOCATION_ENABLED", "true").lower() == "true"
CACHE_TTL_HOURS = int(os.getenv("CACHE_TTL_HOURS", "24"))  # Cache time-to-live in hours

# Private IP handling configuration
# Options: "default_location", "null_response", "server_ip"
PRIVATE_IP_HANDLING = os.getenv("PRIVATE_IP_HANDLING", "default_location")

# MaxMind GeoLite2 Web Service configuration
MAXMIND_ACCOUNT_ID = os.getenv("MAXMIND_ACCOUNT_ID", "")
MAXMIND_LICENSE_KEY = os.getenv("MAXMIND_LICENSE_KEY", "")
MAXMIND_TIMEOUT = int(os.getenv("MAXMIND_TIMEOUT", "5").split()[0])
MAXMIND_SERVICE = os.getenv("MAXMIND_SERVICE", "city")  # 'city' or 'country'
MAXMIND_BASE_URL = "https://geolite.info/geoip/v2.1"

# Development mode settings
DEV_MODE = os.getenv("DEV_MODE", "false").lower() == "true"  # Enable development mode
TEST_IP = os.getenv("TEST_IP", "")  # IP address to use for testing in development mode
DEV_LOG_LEVEL = os.getenv("DEV_LOG_LEVEL", "INFO")  # Logging level for development mode

# Centralized development location configuration
DEV_LOCATION = {
    "city": "Los Baños",
    "region": "Laguna",
    "country": "Philippines",
    "country_display": "Philippines (Local Development)",
    "latitude": 14.1648,
    "longitude": 121.2413,
    "description": "Local Development"
}

# Configure more detailed logging in development mode
if DEV_MODE and DEV_LOG_LEVEL == "DEBUG":
    logging.basicConfig(level=logging.DEBUG)
    logger.setLevel(logging.DEBUG)
    logger.debug("Development mode enabled with DEBUG logging")

# Cache for location lookups to reduce API calls - with timestamps
location_cache = {}  # Format: {"ip": {"data": {...}, "timestamp": datetime}}

# Validate TEST_IP if provided
if TEST_IP:
    try:
        ipaddress.ip_address(TEST_IP)
        logger.info(f"TEST_IP validated: {TEST_IP}")
    except ValueError:
        logger.warning(f"Invalid TEST_IP format: {TEST_IP}. Using default fallback.")
        TEST_IP = "*******"  # Set to a default valid IP

# Check if MaxMind credentials are configured
if not MAXMIND_ACCOUNT_ID or not MAXMIND_LICENSE_KEY:
    logger.warning("MaxMind GeoLite2 Web Service credentials not configured. Geolocation will be limited.")
    if not DEV_MODE:
        logger.warning("Set MAXMIND_ACCOUNT_ID and MAXMIND_LICENSE_KEY environment variables.")
        logger.warning("Sign up at https://www.maxmind.com/en/geolite2/signup")
else:
    logger.info("MaxMind GeoLite2 Web Service credentials configured.")

# Class for MaxMind API exceptions
class MaxMindError(Exception):
    """Base exception for MaxMind API errors."""
    pass

class MaxMindAuthError(MaxMindError):
    """Exception for authentication errors."""
    pass

class MaxMindQuotaError(MaxMindError):
    """Exception for quota exceeded errors."""
    pass

class MaxMindServiceError(MaxMindError):
    """Exception for service errors."""
    pass

def is_private_ip(ip: str) -> bool:
    """
    Check if an IP address is in a private range.

    This function detects all private IP ranges including:
    - 10.0.0.0/8
    - **********/12
    - ***********/16
    - *********/8 (localhost)
    - Link-local addresses (***********/16)

    Args:
        ip (str): The IP address to check

    Returns:
        bool: True if the IP is in a private range, False otherwise
    """
    try:
        # Handle special cases
        if ip in ["localhost", "::1"]:
            return True

        # Convert string IP to IPv4Address or IPv6Address object
        ip_obj = ipaddress.ip_address(ip)

        # Check if it's a private address
        return ip_obj.is_private or ip_obj.is_loopback
    except ValueError:
        # If the IP is invalid, log a warning and assume it's not private
        logger.warning(f"Invalid IP address format: {ip}")
        return False

# Function to query MaxMind GeoLite2 Web Service
def query_maxmind(ip_address: str) -> Dict[str, Any]:
    """
    Query MaxMind GeoLite2 Web Service for IP geolocation data.

    Args:
        ip_address (str): The IP address to look up

    Returns:
        Dict[str, Any]: The JSON response from MaxMind

    Raises:
        MaxMindAuthError: If authentication fails
        MaxMindQuotaError: If the quota is exceeded
        MaxMindServiceError: If the service returns an error
    """
    if not MAXMIND_ACCOUNT_ID or not MAXMIND_LICENSE_KEY:
        raise MaxMindAuthError("MaxMind credentials not configured")

    # Add small random delay to avoid hitting rate limits
    time.sleep(random.uniform(0.1, 0.3))

    # Construct the URL
    service = MAXMIND_SERVICE.lower()
    if service not in ['city', 'country']:
        service = 'city'  # Default to city service

    url = f"{MAXMIND_BASE_URL}/{service}/{ip_address}"

    # Create the authorization header
    auth = base64.b64encode(f"{MAXMIND_ACCOUNT_ID}:{MAXMIND_LICENSE_KEY}".encode()).decode()
    headers = {
        'Authorization': f'Basic {auth}',
        'Accept': 'application/json'
    }

    if DEV_MODE:
        logger.debug(f"Making request to MaxMind GeoLite2 Web Service: {url}")

    try:
        # Make the request
        response = requests.get(url, headers=headers, timeout=MAXMIND_TIMEOUT)

        # Handle different response codes
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 401 or response.status_code == 403:
            error_msg = "Authentication failed. Check your MaxMind account ID and license key."
            logger.error(error_msg)
            raise MaxMindAuthError(error_msg)
        elif response.status_code == 402:
            error_msg = "MaxMind quota exceeded. You have reached your daily limit."
            logger.error(error_msg)
            raise MaxMindQuotaError(error_msg)
        elif response.status_code == 404:
            # IP not found in database, return empty result
            logger.warning(f"IP address {ip_address} not found in MaxMind database")
            return {}
        else:
            error_msg = f"MaxMind service error: HTTP {response.status_code}"
            logger.error(error_msg)
            raise MaxMindServiceError(error_msg)

    except requests.exceptions.Timeout:
        error_msg = f"Request to MaxMind timed out after {MAXMIND_TIMEOUT} seconds"
        logger.error(error_msg)
        raise MaxMindServiceError(error_msg)
    except requests.exceptions.RequestException as e:
        error_msg = f"Request to MaxMind failed: {str(e)}"
        logger.error(error_msg)
        raise MaxMindServiceError(error_msg)

def get_client_ip() -> str:
    """
    Get the client's IP address from the request.

    This function has different behavior in development vs. production mode:

    Development Mode (DEV_MODE=true):
    - If TEST_IP is set, it will be used regardless of the actual client IP
    - For private/local IPs, TEST_IP will be used if available, otherwise "127.0.0.1"

    Production Mode (DEV_MODE=false):
    - Always uses the actual client IP
    - Private IPs are returned as-is for proper handling by the geolocation function

    This function detects all private IP ranges (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
    using the ipaddress module.

    Returns:
        str: The client's IP address, test IP (in dev mode), or "127.0.0.1" if not found
    """
    # If geolocation is disabled, return a placeholder
    if not GEOLOCATION_ENABLED:
        return "0.0.0.0"

    # DEVELOPMENT MODE HANDLING
    if DEV_MODE:
        # In development mode, always use TEST_IP if it's set
        if TEST_IP:
            logger.info(f"Development mode: Using configured test IP: {TEST_IP}")
            return TEST_IP

        # If no TEST_IP is set, proceed with normal IP detection
        logger.debug("Development mode: No TEST_IP set, detecting actual IP")

    # Get the actual IP address from the request
    # Check for X-Forwarded-For header (common when behind a proxy/load balancer)
    if request.headers.get('X-Forwarded-For'):
        ip = request.headers.get('X-Forwarded-For').split(',')[0].strip()
        if DEV_MODE:
            logger.debug(f"IP from X-Forwarded-For header: {ip}")
    # Otherwise use the remote address
    else:
        ip = request.remote_addr or "127.0.0.1"
        if DEV_MODE:
            logger.debug(f"IP from remote_addr: {ip}")

    # Check if the IP is private/local
    is_private = ip in ["127.0.0.1", "localhost", "::1"] or is_private_ip(ip)

    # Handle private IPs differently in development vs. production mode
    if is_private:
        if DEV_MODE:
            logger.info(f"Development mode: Private/local IP detected: {ip}")

            # In development mode, we can use TEST_IP as a fallback for private IPs
            if TEST_IP:
                logger.info(f"Development mode: Using test IP for private network: {TEST_IP}")
                return TEST_IP
            else:
                logger.warning("Development mode: No TEST_IP set. Using localhost.")
                return "127.0.0.1"
        else:
            # In production mode, return the private IP as-is
            # The geolocation function will handle it appropriately
            logger.info(f"Production mode: Private/local IP detected: {ip}")
            return ip

    # For all other cases, return the detected IP
    return ip

def query_maxmind_with_retry(ip_address: str, max_retries: int = 3, initial_delay: float = 1.0) -> Dict[str, Any]:
    """
    Query MaxMind GeoLite2 Web Service with retry mechanism for temporary failures.

    This function implements an exponential backoff retry strategy for handling
    temporary service errors from the MaxMind API.

    Args:
        ip_address (str): The IP address to look up
        max_retries (int): Maximum number of retry attempts (default: 3)
        initial_delay (float): Initial delay in seconds before first retry (default: 1.0)

    Returns:
        Dict[str, Any]: The JSON response from MaxMind

    Raises:
        MaxMindAuthError: If authentication fails (not retried)
        MaxMindQuotaError: If quota is exceeded (not retried)
        MaxMindServiceError: If all retry attempts fail
    """
    retry_count = 0
    retry_delay = initial_delay
    last_error = None

    while retry_count <= max_retries:  # <= to include the initial attempt
        try:
            # If this is a retry, log the attempt
            if retry_count > 0:
                logger.info(f"Retry attempt {retry_count}/{max_retries} for IP {ip_address} after {retry_delay:.2f}s delay")

            # Attempt to query MaxMind
            return query_maxmind(ip_address)

        except MaxMindAuthError as e:
            # Don't retry authentication errors
            logger.error(f"MaxMind authentication error: {str(e)}")
            raise

        except MaxMindQuotaError as e:
            # Don't retry quota errors
            logger.error(f"MaxMind quota exceeded: {str(e)}")
            raise

        except MaxMindServiceError as e:
            # Only retry service errors
            last_error = e
            retry_count += 1

            if retry_count <= max_retries:
                logger.warning(f"MaxMind service error: {str(e)}. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = retry_delay * 2 * (0.5 + random.random())  # Multiply by random factor between 1.0 and 1.5
            else:
                logger.error(f"MaxMind service error: All {max_retries} retry attempts failed for IP {ip_address}")
                raise MaxMindServiceError(f"All {max_retries} retry attempts failed: {str(last_error)}")

        except Exception as e:
            # Don't retry other exceptions
            logger.error(f"Unexpected error querying MaxMind: {str(e)}")
            raise MaxMindServiceError(f"Unexpected error: {str(e)}")

    # This should never be reached due to the raise in the last iteration
    # But just in case, raise the last error
    if last_error:
        raise last_error
    else:
        raise MaxMindServiceError("Unknown error in retry mechanism")

def get_default_geo_response(ip_address: str) -> Dict[str, Any]:
    """Return a default geolocation response structure."""
    return {
        "ip": ip_address,
        "city": None,
        "region": None,
        "country": None,
        "loc": None,
        "latitude": None,
        "longitude": None
    }

def get_geolocation_data(ip_address: str) -> Dict[str, Any]:
    """
    Get geolocation data for an IP address using MaxMind's GeoLite2 Web Service.

    This function queries the MaxMind GeoLite2 Web Service to get geolocation data
    for the provided IP address. It includes caching to reduce API calls.

    Args:
        ip_address (str): The IP address to look up

    Returns:
        Dict[str, Any]: A dictionary containing geolocation data
    """
    if not GEOLOCATION_ENABLED:
        return get_default_geo_response(ip_address)

    current_time = datetime.now()

    # Return cached result if available and not expired
    if ip_address in location_cache:
        cache_entry = location_cache[ip_address]
        cache_age = current_time - cache_entry["timestamp"]

        if cache_age < timedelta(hours=CACHE_TTL_HOURS):
            logger.info(f"Using cached geolocation data for IP: {ip_address}")
            return cache_entry["data"]
        else:
            logger.info(f"Cached geolocation data expired for IP: {ip_address}")

    # Handle local IPs and private network IPs based on configuration
    if ip_address in ["127.0.0.1", "localhost", "::1", "0.0.0.0"] or is_private_ip(ip_address):
        # In development mode, always use the development location
        if DEV_MODE:
            logger.info(f"Development mode: Using development location for private IP: {ip_address}")
            dummy_data = {
                "ip": ip_address,
                "city": DEV_LOCATION["city"],
                "region": DEV_LOCATION["region"],
                "country": DEV_LOCATION["country_display"],
                "loc": f"{DEV_LOCATION['latitude']},{DEV_LOCATION['longitude']}",
                "latitude": DEV_LOCATION["latitude"],
                "longitude": DEV_LOCATION["longitude"]
            }
            location_cache[ip_address] = {"data": dummy_data, "timestamp": current_time}
            return dummy_data

        # In production mode, handle based on PRIVATE_IP_HANDLING setting
        if PRIVATE_IP_HANDLING == "default_location":
            # Use centralized development location configuration
            logger.info(f"Using default location for private IP: {ip_address}")
            dummy_data = {
                "ip": ip_address,
                "city": DEV_LOCATION["city"],
                "region": DEV_LOCATION["region"],
                "country": DEV_LOCATION["country_display"],
                "loc": f"{DEV_LOCATION['latitude']},{DEV_LOCATION['longitude']}",
                "latitude": DEV_LOCATION["latitude"],
                "longitude": DEV_LOCATION["longitude"]
            }
            location_cache[ip_address] = {"data": dummy_data, "timestamp": current_time}
            return dummy_data

        elif PRIVATE_IP_HANDLING == "null_response":
            # Return null values for private IPs
            logger.info(f"Returning null response for private IP: {ip_address}")
            null_data = get_default_geo_response(ip_address)
            location_cache[ip_address] = {"data": null_data, "timestamp": current_time}
            return null_data

        elif PRIVATE_IP_HANDLING == "server_ip":
            # Try to use the server's public IP for geolocation
            try:
                logger.info(f"Attempting to use server's public IP for private IP: {ip_address}")
                # Get the server's public IP using ipify.org
                response = requests.get("https://api.ipify.org", timeout=5)
                if response.status_code == 200:
                    server_ip = response.text.strip()
                    logger.info(f"Using server's public IP ({server_ip}) for private IP: {ip_address}")

                    # Check if we have this IP cached
                    if server_ip in location_cache:
                        logger.info(f"Using cached data for server IP: {server_ip}")
                        server_data = location_cache[server_ip]["data"].copy()
                        server_data["ip"] = ip_address  # Update the IP address
                        return server_data

                    # Otherwise, recursively call get_geolocation_data with the server's IP
                    # But set a flag to prevent infinite recursion
                    if not hasattr(get_geolocation_data, "_recursion_guard"):
                        get_geolocation_data._recursion_guard = set()

                    if ip_address not in get_geolocation_data._recursion_guard:
                        get_geolocation_data._recursion_guard.add(ip_address)
                        try:
                            server_data = get_geolocation_data(server_ip).copy()
                            server_data["ip"] = ip_address  # Update the IP address
                            return server_data
                        finally:
                            get_geolocation_data._recursion_guard.remove(ip_address)
                    else:
                        logger.warning(f"Recursion detected when resolving IP: {ip_address}")
                        return get_default_geo_response(ip_address)
            except Exception as e:
                logger.error(f"Failed to get server's public IP: {str(e)}")

            # Fallback to default location if server IP lookup fails
            logger.warning(f"Falling back to default location for private IP: {ip_address}")
            dummy_data = {
                "ip": ip_address,
                "city": DEV_LOCATION["city"],
                "region": DEV_LOCATION["region"],
                "country": DEV_LOCATION["country_display"],
                "loc": f"{DEV_LOCATION['latitude']},{DEV_LOCATION['longitude']}",
                "latitude": DEV_LOCATION["latitude"],
                "longitude": DEV_LOCATION["longitude"]
            }
            location_cache[ip_address] = {"data": dummy_data, "timestamp": current_time}
            return dummy_data

    try:
        if DEV_MODE:
            logger.info(f"Development mode: Querying MaxMind for IP address: {ip_address}")

        # Query MaxMind GeoLite2 Web Service with retry mechanism
        maxmind_data = query_maxmind_with_retry(ip_address)

        if not maxmind_data:
            logger.warning(f"No data returned from MaxMind for IP: {ip_address}")
            result = get_default_geo_response(ip_address)
            location_cache[ip_address] = {"data": result, "timestamp": current_time}
            return result

        if DEV_MODE:
            logger.debug(f"MaxMind raw response: {maxmind_data}")

        # Extract location data from MaxMind response
        city = None
        region = None
        country = None
        lat = None
        lon = None

        # Extract city information
        if 'city' in maxmind_data and 'names' in maxmind_data['city']:
            city = maxmind_data['city']['names'].get('en')

        # Extract region/subdivision information
        if 'subdivisions' in maxmind_data and maxmind_data['subdivisions']:
            subdivision = maxmind_data['subdivisions'][0]
            if 'names' in subdivision:
                region = subdivision['names'].get('en')

        # Extract country information
        if 'country' in maxmind_data and 'names' in maxmind_data['country']:
            country = maxmind_data['country']['names'].get('en')
        elif 'registered_country' in maxmind_data and 'names' in maxmind_data['registered_country']:
            country = maxmind_data['registered_country']['names'].get('en')

        # Extract coordinates
        if 'location' in maxmind_data:
            lat = maxmind_data['location'].get('latitude')
            lon = maxmind_data['location'].get('longitude')

        # Create a standardized result
        result = {
            "ip": ip_address,
            "city": city,
            "region": region,
            "country": country,
            "loc": f"{lat},{lon}" if lat is not None and lon is not None else None,
            "latitude": lat,
            "longitude": lon
        }

        # Add additional data if available
        if 'location' in maxmind_data and 'accuracy_radius' in maxmind_data['location']:
            result['accuracy_radius'] = maxmind_data['location'].get('accuracy_radius')

        if 'traits' in maxmind_data:
            traits = maxmind_data['traits']
            if 'autonomous_system_number' in traits:
                result['asn'] = traits.get('autonomous_system_number')
            if 'autonomous_system_organization' in traits:
                result['org'] = traits.get('autonomous_system_organization')
            if 'isp' in traits:
                result['isp'] = traits.get('isp')

        # Log successful lookup
        if city or region or country:
            logger.info(f"Successfully retrieved geolocation data for IP: {ip_address}")
            logger.debug(f"Geolocation data: {result}")

            if DEV_MODE:
                logger.info(f"Development mode: Location found for {ip_address}: {city or 'Unknown city'}, {region or 'Unknown region'}, {country or 'Unknown country'}")
                logger.debug(f"Development mode: Full result: {result}")
        else:
            logger.warning(f"Could not retrieve meaningful geolocation data for IP: {ip_address}")

            if DEV_MODE:
                logger.warning(f"Development mode: No meaningful location data found for {ip_address}")
                logger.debug(f"Development mode: Empty result details: {result}")

        # Cache the result
        location_cache[ip_address] = {"data": result, "timestamp": current_time}

        if DEV_MODE:
            logger.debug(f"Development mode: Cached result for IP: {ip_address}")
            logger.debug(f"Development mode: Cache now contains {len(location_cache)} entries")

        return result

    except (MaxMindAuthError, MaxMindQuotaError, MaxMindServiceError) as e:
        error_type = e.__class__.__name__
        logger.error(f"MaxMind error ({error_type}): {str(e)}")

        # Try to use cached data if available, even if expired
        if ip_address in location_cache:
            logger.info(f"Using cached data for {ip_address} due to MaxMind service error")
            return location_cache[ip_address]["data"]

        # If no cache is available, check if we have any cached data for similar IPs
        # This is useful for IP addresses in the same subnet
        try:
            ip_prefix = '.'.join(ip_address.split('.')[:3])  # Get first 3 octets (e.g., 192.168.1)
            for cached_ip, cache_data in location_cache.items():
                if cached_ip.startswith(ip_prefix):
                    logger.info(f"Using cached data from similar IP {cached_ip} for {ip_address}")
                    similar_data = cache_data["data"].copy()
                    similar_data["ip"] = ip_address  # Update the IP address
                    return similar_data
        except Exception as subnet_error:
            logger.warning(f"Error while trying to find similar IP: {str(subnet_error)}")

        # If all else fails, return default response
        return get_default_geo_response(ip_address)
    except Exception as e:
        logger.error(f"Unexpected error getting geolocation data for IP {ip_address}: {str(e)}")

        # Try to use cached data if available, even if expired
        if ip_address in location_cache:
            logger.info(f"Using cached data for {ip_address} due to unexpected error")
            return location_cache[ip_address]["data"]

        return get_default_geo_response(ip_address)

def get_location_for_analytics(ip: Optional[str] = None) -> Tuple[str, Optional[str], Optional[str], Optional[str], Optional[float], Optional[float]]:
    """
    Get location data for the current request to use in analytics.

    Args:
        ip (Optional[str]): IP address to use, or None to get from current request

    Returns:
        Tuple containing:
        - IP address (str)
        - City (str or None)
        - Region (str or None)
        - Country (str or None)
        - Latitude (float or None)
        - Longitude (float or None)
    """
    if ip is None:
        try:
            ip = get_client_ip()
        except Exception as e:
            logger.error(f"Error getting client IP: {str(e)}")
            ip = "0.0.0.0"

    geo_data = get_geolocation_data(ip)

    return (
        geo_data["ip"],
        geo_data["city"],
        geo_data["region"],
        geo_data["country"],
        geo_data["latitude"],
        geo_data["longitude"]
    )


def test_geolocation(test_ip: str) -> Dict[str, Any]:
    """
    Test geolocation functionality with a specific IP address.

    This function is useful for development and testing purposes.
    It bypasses the normal client IP detection and uses the provided IP address.

    Args:
        test_ip (str): The IP address to test geolocation with

    Returns:
        Dict[str, Any]: A dictionary containing geolocation data

    Example:
        >>> test_geolocation("*******")  # Test with Google's DNS server
    """
    if not test_ip:
        logger.warning("No test IP provided. Using a default test IP (*******).")
        test_ip = "*******"  # Google's DNS as a fallback

    logger.info(f"Testing geolocation with IP: {test_ip}")

    # Clear cache for this IP to ensure a fresh lookup
    if test_ip in location_cache:
        del location_cache[test_ip]
        logger.debug(f"Cleared cache for IP: {test_ip}")

    # Get geolocation data
    start_time = datetime.now()
    geo_data = get_geolocation_data(test_ip)
    end_time = datetime.now()

    # Calculate processing time
    processing_time = (end_time - start_time).total_seconds()

    # Add processing time to the result
    geo_data["processing_time"] = processing_time

    # Log the result
    if geo_data["city"] or geo_data["region"] or geo_data["country"]:
        logger.info(f"Geolocation test successful for IP {test_ip} in {processing_time:.2f} seconds")
        logger.info(f"Location: {geo_data['city'] or 'Unknown city'}, {geo_data['region'] or 'Unknown region'}, {geo_data['country'] or 'Unknown country'}")
        logger.info(f"Coordinates: {geo_data['latitude']}, {geo_data['longitude']}")
    else:
        logger.warning(f"Geolocation test completed but no meaningful location data found for IP {test_ip}")

    return geo_data

def get_development_info() -> Dict[str, Any]:
    """
    Get information about the current development environment settings.

    This function is useful for debugging and development purposes.

    Returns:
        Dict[str, Any]: A dictionary containing development environment information
    """
    return {
        "dev_mode": DEV_MODE,
        "test_ip": TEST_IP,
        "geolocation_enabled": GEOLOCATION_ENABLED,
        "private_ip_handling": PRIVATE_IP_HANDLING,
        "cache_ttl_hours": CACHE_TTL_HOURS,
        "maxmind_account_id": MAXMIND_ACCOUNT_ID[:4] + "****" if MAXMIND_ACCOUNT_ID else None,
        "maxmind_license_key": MAXMIND_LICENSE_KEY[:4] + "****" if MAXMIND_LICENSE_KEY else None,
        "maxmind_timeout": MAXMIND_TIMEOUT,
        "maxmind_service": MAXMIND_SERVICE,
        "maxmind_base_url": MAXMIND_BASE_URL,
        "cache_size": len(location_cache),
        "cached_ips": list(location_cache.keys())
    }


def get_privacy_notice() -> str:
    """
    Get the privacy notice text for geolocation tracking.

    Returns:
        str: HTML formatted privacy notice
    """
    return """
    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
        <h3 class="text-lg font-semibold text-blue-800 mb-2">Privacy Notice</h3>
        <p class="text-sm text-blue-700 mb-2">
            This application collects and stores your approximate geographic location based on your IP address
            using MaxMind's GeoLite2 Web Service.
            This information is used to:
        </p>
        <ul class="list-disc list-inside text-sm text-blue-700 mb-2 ml-2">
            <li>Analyze the geographic distribution of our users</li>
            <li>Improve our services based on regional usage patterns</li>
            <li>Provide aggregated analytics to system administrators</li>
        </ul>
        <p class="text-sm text-blue-700 mb-2">
            Your location data is stored alongside your device identifier but is not linked to personally
            identifiable information. This data is not shared with third parties.
        </p>
        <p class="text-xs text-gray-600">
            This product includes GeoLite2 data created by MaxMind, available from
            <a href="https://www.maxmind.com" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">https://www.maxmind.com</a>.
        </p>
    </div>
    """
