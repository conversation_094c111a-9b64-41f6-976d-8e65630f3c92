import os
import shutil
import logging
import json
from tqdm import tqdm
import time
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
from langchain.schema import Document
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler(), 
                              logging.FileHandler("reembed.log")])
logger = logging.getLogger(__name__)

# Configuration
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
BACKUP_PATH = f"./chroma_backup_{int(time.time())}"

def backup_chroma_db():
    """Create a backup of the current Chroma database"""
    if os.path.exists(CHROMA_PATH):
        logger.info(f"Creating backup of Chroma DB at {BACKUP_PATH}")
        shutil.copytree(CHROMA_PATH, BACKUP_PATH)
        logger.info(f"Backup created successfully at {BACKUP_PATH}")
        return True
    else:
        logger.error(f"Chroma DB path {CHROMA_PATH} does not exist")
        return False

def get_categories():
    """Get all categories (collections) from the Chroma database"""
    if not os.path.exists(CHROMA_PATH):
        logger.error(f"Chroma DB path {CHROMA_PATH} does not exist")
        return []
    
    categories = [d for d in os.listdir(CHROMA_PATH) 
                 if os.path.isdir(os.path.join(CHROMA_PATH, d))]
    logger.info(f"Found {len(categories)} categories: {categories}")
    return categories

def extract_documents(category):
    """Extract all documents and their metadata from a category"""
    try:
        # Use the original embedding model to access the data
        # This is important - we need to use the same dimensionality to read the data
        embed_fn = None
        
        # Try different embedding models with 768 dimensions
        for model_name in ["nomic-embed-text:latest", "bge-m3:latest"]:
            try:
                logger.info(f"Trying to access collection with {model_name}")
                embed_fn = OllamaEmbeddings(model=model_name)
                break
            except Exception as e:
                logger.warning(f"Failed to initialize {model_name}: {str(e)}")
        
        if embed_fn is None:
            logger.error("Could not initialize any compatible embedding model")
            return []
            
        # Initialize Chroma with the compatible embedding model
        persist_dir = os.path.join(CHROMA_PATH, category)
        db = Chroma(
            collection_name=category,
            persist_directory=persist_dir,
            embedding_function=embed_fn
        )
        
        # Get all documents
        results = db.get(include=["documents", "metadatas"])
        documents = results["documents"]
        metadatas = results["metadatas"]
        
        # Create Document objects
        docs = []
        for i, (doc_text, metadata) in enumerate(zip(documents, metadatas)):
            if doc_text and metadata:
                docs.append(Document(page_content=doc_text, metadata=metadata))
        
        logger.info(f"Extracted {len(docs)} documents from category {category}")
        return docs
    except Exception as e:
        logger.error(f"Error extracting documents from {category}: {str(e)}")
        return []

def clear_collection(category):
    """Clear the existing collection"""
    try:
        persist_dir = os.path.join(CHROMA_PATH, category)
        if os.path.exists(persist_dir):
            # Rename the directory first as a safety measure
            temp_dir = f"{persist_dir}_old_{int(time.time())}"
            os.rename(persist_dir, temp_dir)
            logger.info(f"Renamed collection directory {persist_dir} to {temp_dir}")
            
            # Create a new empty directory
            os.makedirs(persist_dir, exist_ok=True)
            logger.info(f"Created new empty directory for collection {category}")
            
            # After successful re-embedding, we can delete the old directory
            return temp_dir
        return None
    except Exception as e:
        logger.error(f"Error clearing collection {category}: {str(e)}")
        return None

def reembed_documents(category, documents, old_dir=None):
    """Re-embed documents with the new embedding model"""
    try:
        # Initialize with the new embedding model
        embed_fn = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)
        
        # Create a new Chroma collection
        persist_dir = os.path.join(CHROMA_PATH, category)
        db = Chroma(
            collection_name=category,
            persist_directory=persist_dir,
            embedding_function=embed_fn
        )
        
        # Add documents in batches to avoid memory issues
        batch_size = 50
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i+batch_size]
            db.add_documents(batch)
            logger.info(f"Added batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1} to {category}")
        
        # If successful, remove the old directory
        if old_dir and os.path.exists(old_dir):
            shutil.rmtree(old_dir)
            logger.info(f"Removed old collection directory {old_dir}")
        
        return True
    except Exception as e:
        logger.error(f"Error re-embedding documents for {category}: {str(e)}")
        return False

def process_category(category):
    """Process a single category"""
    logger.info(f"Processing category: {category}")
    
    # Extract documents
    documents = extract_documents(category)
    if not documents:
        logger.warning(f"No documents found for category {category}, skipping")
        return False
    
    # Clear the collection
    old_dir = clear_collection(category)
    
    # Re-embed documents
    success = reembed_documents(category, documents, old_dir)
    
    return success

def main():
    """Main function to re-embed all documents"""
    logger.info(f"Starting re-embedding process with model {TEXT_EMBEDDING_MODEL}")
    
    # Create backup
    if not backup_chroma_db():
        logger.error("Failed to create backup, aborting")
        return
    
    # Get categories
    categories = get_categories()
    if not categories:
        logger.error("No categories found, aborting")
        return
    
    # Process each category
    success_count = 0
    for category in categories:
        if process_category(category):
            success_count += 1
    
    logger.info(f"Re-embedding complete. Successfully processed {success_count}/{len(categories)} categories")
    logger.info(f"Backup is available at {BACKUP_PATH}")

if __name__ == "__main__":
    main()
