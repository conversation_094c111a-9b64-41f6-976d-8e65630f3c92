# Geolocation Configuration
# ----------------------

# Enable/disable geolocation functionality
GEOLOCATION_ENABLED=true

# How to handle private IP addresses in production mode
# Options: "default_location", "null_response", "server_ip"
PRIVATE_IP_HANDLING=default_location

# Cache time-to-live in hours
CACHE_TTL_HOURS=24

# MaxMind GeoLite2 Web Service configuration
# Sign up at: https://www.maxmind.com/en/geolite2/signup
# Generate a license key at: https://www.maxmind.com/en/accounts/current/license-key
MAXMIND_ACCOUNT_ID=your_account_id_here
MAXMIND_LICENSE_KEY=your_license_key_here

# MaxMind service type: 'city' or 'country'
MAXMIND_SERVICE=city

# Timeout in seconds for MaxMind API requests
MAXMIND_TIMEOUT=5

# Development Mode Configuration
# -----------------------------

# Enable development mode
DEV_MODE=false

# IP address to use for testing in development mode
# Examples:
# - ******* (Google DNS)
# - ******* (<PERSON><PERSON><PERSON><PERSON> DNS)
TEST_IP=*******

# Logging level for development mode: INFO or DEBUG
DEV_LOG_LEVEL=INFO

# Privacy Notice Configuration
# ---------------------------

# Whether to show the privacy notice
PRIVACY_NOTICE_SHOWN=true

# CSRF Protection Configuration
# -----------------------------

# Enable/disable CSRF protection
WTF_CSRF_ENABLED=true

# CSRF token timeout in seconds (7200 = 2 hours)
WTF_CSRF_TIME_LIMIT=7200

# Require HTTPS for CSRF tokens (set to true in production)
WTF_CSRF_SSL_STRICT=false

# Enable CSRF checking by default
WTF_CSRF_CHECK_DEFAULT=true

# Session Configuration
# --------------------

# Session timeout in minutes (120 = 2 hours)
SESSION_TIMEOUT_MINUTES=120
