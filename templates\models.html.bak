<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Models</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Manage Models</h1>
                <!-- <a href="{{ url_for('admin_dashboard') }}" class="text-blue-600 hover:underline">&larr; Back to Dashboard</a> -->
            </div>

            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            Select the models to use for chat responses and text embeddings. Changes will apply to all new queries.
                        </p>
                    </div>
                </div>
            </div>

            <form id="modelForm" class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-lg font-medium text-gray-800 mb-4">LLM Model Selection</h2>
                    <p class="text-sm text-gray-600 mb-4">Select the language model that will generate responses to user queries.</p>

                    {% if models %}
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for model in models %}
                                <div class="relative">
                                    <input type="radio" id="llm_{{ loop.index }}" name="llm_model" value="{{ model.name }}"
                                           class="hidden peer" {% if model.name == selected_model %}checked{% endif %}>
                                    <label for="llm_{{ loop.index }}"
                                           class="block p-4 bg-white border rounded-lg cursor-pointer
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 transition-all">
                                        <div class="font-medium text-gray-900">{{ model.name }}</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Size: {{ model.size | filesizeformat }}
                                        </div>
                                        {% if model.name == default_llm %}
                                        <div class="text-xs text-green-600 mt-1 font-semibold">
                                            Default Model
                                        </div>
                                        {% endif %}
                                    </label>
                                    <div class="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        No Ollama models found. Please make sure Ollama is running and has models installed.
                                    </p>
                                    <div class="mt-2">
                                        <p class="text-sm text-yellow-700 font-semibold">Troubleshooting steps:</p>
                                        <ol class="list-decimal list-inside text-sm text-yellow-700 mt-1 space-y-1">
                                            <li>Verify that Ollama is running on your system</li>
                                            <li>Check that Ollama is accessible at http://localhost:11434</li>
                                            <li>Make sure you have at least one model installed in Ollama</li>
                                            <li>Try restarting the application after confirming Ollama is running</li>
                                        </ol>
                                    </div>
                                    <div class="mt-3">
                                        <button id="useDefaultModels" class="px-3 py-1 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-colors text-sm">
                                            Use Default Models
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-lg font-medium text-gray-800 mb-4">Embedding Model Selection</h2>
                    <p class="text-sm text-gray-600 mb-4">Select the embedding model used to vectorize documents for retrieval.</p>

                    {% if embeddings %}
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for embedding in embeddings %}
                                <div class="relative">
                                    <input type="radio" id="embed_{{ loop.index }}" name="embedding_model" value="{{ embedding.name }}"
                                           class="hidden peer" {% if embedding.name == selected_embedding %}checked{% endif %}>
                                    <label for="embed_{{ loop.index }}"
                                           class="block p-4 bg-white border rounded-lg cursor-pointer
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 transition-all">
                                        <div class="font-medium text-gray-900">{{ embedding.name }}</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Size: {{ embedding.size | filesizeformat }}
                                        </div>
                                        {% if embedding.name == default_embedding %}
                                        <div class="text-xs text-green-600 mt-1 font-semibold">
                                            Default Model
                                        </div>
                                        {% endif %}
                                    </label>
                                    <div class="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        No embedding models found. Please make sure Ollama is running and has embedding models installed.
                                    </p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Vision Model Selection Section -->
                <!-- Model Parameters Section -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                    <h2 class="text-lg font-medium text-gray-800 mb-4">Model Parameters</h2>
                    <p class="text-sm text-gray-600 mb-4">Configure parameters for the LLM model to adjust its behavior.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <!-- Temperature -->
                        <div>
                            <label for="temperature" class="block text-sm font-medium text-gray-700 mb-1">Temperature</label>
                            <div class="flex items-center">
                                <input type="range" id="temperature" name="temperature" min="0" max="1" step="0.05" value="{{ temperature|default(0.7) }}"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <span id="temperatureValue" class="ml-2 text-sm font-medium text-gray-700 w-10">{{ temperature|default(0.7) }}</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Controls randomness (0 = deterministic, 1 = creative)</p>
                        </div>

                        <!-- Top P -->
                        <div>
                            <label for="top_p" class="block text-sm font-medium text-gray-700 mb-1">Top P</label>
                            <div class="flex items-center">
                                <input type="range" id="top_p" name="top_p" min="0" max="1" step="0.05" value="{{ top_p|default(0.9) }}"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <span id="topPValue" class="ml-2 text-sm font-medium text-gray-700 w-10">{{ top_p|default(0.9) }}</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Controls diversity via nucleus sampling</p>
                        </div>

                        <!-- Top K -->
                        <div>
                            <label for="top_k" class="block text-sm font-medium text-gray-700 mb-1">Top K</label>
                            <div class="flex items-center">
                                <input type="range" id="top_k" name="top_k" min="1" max="100" step="1" value="{{ top_k|default(40) }}"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <span id="topKValue" class="ml-2 text-sm font-medium text-gray-700 w-10">{{ top_k|default(40) }}</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Limits vocabulary to top K tokens</p>
                        </div>

                        <!-- Repeat Penalty -->
                        <div>
                            <label for="repeat_penalty" class="block text-sm font-medium text-gray-700 mb-1">Repeat Penalty</label>
                            <div class="flex items-center">
                                <input type="range" id="repeat_penalty" name="repeat_penalty" min="1" max="2" step="0.05" value="{{ repeat_penalty|default(1.1) }}"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <span id="repeatPenaltyValue" class="ml-2 text-sm font-medium text-gray-700 w-10">{{ repeat_penalty|default(1.1) }}</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Penalizes repetition (higher = less repetition)</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <!-- Context Window -->
                        <div>
                            <label for="num_ctx" class="block text-sm font-medium text-gray-700 mb-1">Context Window Size</label>
                            <div class="flex items-center">
                                <input type="number" id="num_ctx" name="num_ctx" min="512" max="32768" step="512" value="{{ num_ctx|default(4096) }}"
                                       class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Maximum context window size in tokens</p>
                        </div>

                        <!-- Max Tokens to Generate -->
                        <div>
                            <label for="num_predict" class="block text-sm font-medium text-gray-700 mb-1">Max Tokens to Generate</label>
                            <div class="flex items-center">
                                <input type="number" id="num_predict" name="num_predict" min="64" max="4096" step="64" value="{{ num_predict|default(256) }}"
                                       class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Maximum number of tokens to generate</p>
                        </div>
                    </div>

                    <!-- System Prompt -->
                    <div>
                        <label for="system_prompt" class="block text-sm font-medium text-gray-700 mb-1">System Prompt</label>
                        <textarea id="system_prompt" name="system_prompt" rows="3"
                                  class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">{{ system_prompt|default('You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.') }}</textarea>
                        <p class="mt-1 text-xs text-gray-500">System instructions for the model</p>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h2 class="text-lg font-medium text-gray-800 mb-4">Vision Model Selection</h2>
                    <p class="text-sm text-gray-600 mb-4">Select the vision model used for analyzing images in documents.</p>

                    <!-- Vision Model Toggle Switches -->
                    <div class="space-y-3">
                        <!-- Vision Model for Chat Toggle -->
                        <div class="flex items-center p-2 bg-blue-50 rounded-md">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="use_vision" name="use_vision" class="sr-only peer" value="true" {% if use_vision %}checked{% endif %}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                <span class="ml-3 text-sm font-medium text-gray-900">Enable Vision Model for Chat</span>
                            </label>
                            <div class="ml-4 text-xs text-gray-600">
                                {% if use_vision %}
                                    Vision model is enabled for chat responses. Disable to speed up query processing.
                                {% else %}
                                    Vision model is disabled for chat responses. Enable for image analysis during queries.
                                {% endif %}
                            </div>
                        </div>

                        <!-- Vision Model for Embedding Toggle (Global Control) -->
                        <div class="flex items-center p-2 bg-green-50 rounded-md border-l-4 border-green-500">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="use_vision_during_embedding" name="use_vision_during_embedding" class="sr-only peer" value="true" {% if use_vision_during_embedding %}checked{% endif %}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                <span class="ml-3 text-sm font-medium text-gray-900">Enable Vision Model During Embedding</span>
                            </label>
                            <div class="ml-4 text-xs text-gray-600">
                                {% if use_vision_during_embedding %}
                                    <span class="font-semibold">GLOBAL SETTING:</span> Vision model will analyze and filter images during PDF embedding. Disable for faster embedding.
                                {% else %}
                                    <span class="font-semibold">GLOBAL SETTING:</span> Vision model is disabled during embedding. Enable to filter irrelevant images during PDF upload.
                                {% endif %}
                            </div>
                        </div>

                        <!-- Explanation of hierarchical relationship -->
                        <div class="text-xs text-gray-600 bg-gray-100 p-2 rounded-md">
                            <p><span class="font-semibold">Note:</span> The "Enable Vision Model During Embedding" toggle is a global setting that controls whether the vision model option is available during PDF uploads. When disabled, users cannot enable vision analysis for individual uploads.</p>
                        </div>
                    </div>

                    <!-- Image Filtering Settings -->
                    <div id="imageFilteringSettings" class="mb-4 p-4 bg-gray-50 rounded-md border border-gray-200">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-md font-medium text-gray-800">Image Filtering Settings</h3>
                            <button type="button" id="toggleFilterSettings" class="text-blue-600 text-sm hover:underline focus:outline-none">
                                <span id="toggleFilterText">Hide Settings</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        </div>

                        <div id="filterSettingsContent" style="display: block;">
                            <!-- Enable Image Filtering Toggle -->
                            <div class="flex items-center mb-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="filter_pdf_images" name="filter_pdf_images" class="sr-only peer" value="true" {% if filter_pdf_images %}checked{% endif %}>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-3 text-sm font-medium text-gray-900">Enable Intelligent Image Filtering</span>
                                </label>
                                <div class="ml-4 text-xs text-gray-600">
                                    Automatically filter out decorative images, logos, and irrelevant graphics
                                </div>
                            </div>

                            <!-- Filter Sensitivity -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Filter Sensitivity</label>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <input type="radio" id="sensitivity_low" name="filter_sensitivity" value="low" class="h-4 w-4 text-blue-600 focus:ring-blue-500" {% if filter_sensitivity == 'low' %}checked{% endif %}>
                                        <label for="sensitivity_low" class="ml-2 text-sm text-gray-700">Low</label>
                                        <span class="ml-1 text-xs text-gray-500">(Keep most images)</span>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="sensitivity_medium" name="filter_sensitivity" value="medium" class="h-4 w-4 text-blue-600 focus:ring-blue-500" {% if filter_sensitivity == 'medium' or not filter_sensitivity %}checked{% endif %}>
                                        <label for="sensitivity_medium" class="ml-2 text-sm text-gray-700">Medium</label>
                                        <span class="ml-1 text-xs text-gray-500">(Balanced)</span>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="sensitivity_high" name="filter_sensitivity" value="high" class="h-4 w-4 text-blue-600 focus:ring-blue-500" {% if filter_sensitivity == 'high' %}checked{% endif %}>
                                        <label for="sensitivity_high" class="ml-2 text-sm text-gray-700">High</label>
                                        <span class="ml-1 text-xs text-gray-500">(Only keep highly relevant images)</span>
                                    </div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Controls how aggressively to filter out decorative or less relevant images</p>
                            </div>

                            <!-- Maximum Images to Analyze -->
                            <div class="mb-4">
                                <label for="max_pdf_images" class="block text-sm font-medium text-gray-700 mb-1">Maximum Images to Analyze</label>
                                <div class="flex items-center">
                                    <input type="number" id="max_pdf_images" name="max_pdf_images" min="1" max="50" value="{{ max_pdf_images }}"
                                           class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-24 sm:text-sm border-gray-300 rounded-md">
                                    <span class="ml-2 text-xs text-gray-500">Higher values may slow down processing</span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Limits the number of images analyzed per document (1-50)</p>
                            </div>

                            <!-- Show Filtered Images Toggle -->
                            <div class="flex items-center mb-2">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="show_filtered_images" name="show_filtered_images" class="sr-only peer" value="true" {% if show_filtered_images %}checked{% endif %}>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-3 text-sm font-medium text-gray-900">Show Filtered Images</span>
                                </label>
                                <div class="ml-4 text-xs text-gray-600">
                                    Display which images were filtered out and why
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if vision_models %}
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for vision in vision_models %}
                                <div class="relative">
                                    <input type="radio" id="vision_{{ loop.index }}" name="vision_model" value="{{ vision.name }}"
                                           class="hidden peer" {% if vision.name == selected_vision %}checked{% endif %}>
                                    <label for="vision_{{ loop.index }}"
                                           class="block p-4 bg-white border rounded-lg cursor-pointer
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 transition-all">
                                        <div class="font-medium text-gray-900">{{ vision.name }}</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Size: {{ vision.size | filesizeformat }}
                                        </div>
                                        {% if vision.name == default_vision %}
                                        <div class="text-xs text-green-600 mt-1 font-semibold">
                                            Default Model
                                        </div>
                                        {% endif %}
                                    </label>
                                    <div class="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        No vision models found. Please make sure Ollama is running and has vision-capable models installed.
                                    </p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <div class="flex justify-end space-x-4">
                    <button type="button" id="saveDefaultsButton" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                        Save as Default
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        Update Models
                    </button>
                </div>
            </form>

            <div id="statusMessage" class="mt-4 hidden"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script>
        // Function to safely get an element by ID
        function safeGetElement(id) {
            return document.getElementById(id);
        }

        // Function to check if either vision toggle is enabled
        function isAnyVisionEnabled() {
            const useVisionCheckbox = safeGetElement('use_vision');
            const useVisionDuringEmbeddingCheckbox = safeGetElement('use_vision_during_embedding');

            // If we can't find the checkboxes, default to showing the settings
            if (!useVisionCheckbox && !useVisionDuringEmbeddingCheckbox) {
                console.warn('Vision toggle checkboxes not found, defaulting to show filtering settings');
                return true;
            }

            return (useVisionCheckbox && useVisionCheckbox.checked) ||
                   (useVisionDuringEmbeddingCheckbox && useVisionDuringEmbeddingCheckbox.checked);
        }

        // Function to update filtering settings visibility
        function updateFilteringSettingsVisibility() {
            const filteringSettings = safeGetElement('imageFilteringSettings');
            if (!filteringSettings) {
                console.warn('Image filtering settings element not found');
                return;
            }

            if (isAnyVisionEnabled()) {
                filteringSettings.classList.remove('hidden');
                console.log('Image filtering settings are now visible');
            } else {
                filteringSettings.classList.add('hidden');
                console.log('Image filtering settings are now hidden');
            }
        }

        // Function to update slider value displays
        function updateSliderValueDisplay(sliderId, valueId) {
            const slider = safeGetElement(sliderId);
            const valueDisplay = safeGetElement(valueId);

            if (!slider || !valueDisplay) return;

            // Update initial value
            valueDisplay.textContent = slider.value;

            // Add event listener for changes
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
        }

        // Function to set up event listeners
        function setupEventListeners() {
            const useVisionCheckbox = safeGetElement('use_vision');
            const useVisionDuringEmbeddingCheckbox = safeGetElement('use_vision_during_embedding');
            const toggleFilterSettingsBtn = safeGetElement('toggleFilterSettings');

            // Add event listeners for vision toggles
            if (useVisionCheckbox) {
                useVisionCheckbox.addEventListener('change', updateFilteringSettingsVisibility);
            }

            if (useVisionDuringEmbeddingCheckbox) {
                useVisionDuringEmbeddingCheckbox.addEventListener('change', updateFilteringSettingsVisibility);
            }

            // Handle filter settings toggle
            if (toggleFilterSettingsBtn) {
                toggleFilterSettingsBtn.addEventListener('click', function() {
                    const filterContent = safeGetElement('filterSettingsContent');
                    const toggleText = safeGetElement('toggleFilterText');

                    if (!filterContent || !toggleText) return;

                    if (filterContent.style.display === 'none') {
                        filterContent.style.display = 'block';
                        toggleText.textContent = 'Hide Settings';
                    } else {
                        filterContent.style.display = 'none';
                        toggleText.textContent = 'Show Settings';
                    }
                });
            }

            // Set up slider value displays
            updateSliderValueDisplay('temperature', 'temperatureValue');
            updateSliderValueDisplay('top_p', 'topPValue');
            updateSliderValueDisplay('top_k', 'topKValue');
            updateSliderValueDisplay('repeat_penalty', 'repeatPenaltyValue');
        }

        // Initialize when DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded, initializing settings');
            setupEventListeners();
            updateFilteringSettingsVisibility();
        });

        // Also run immediately in case DOMContentLoaded has already fired
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('Document already loaded, initializing immediately');
            setupEventListeners();
            updateFilteringSettingsVisibility();
        }
        // Function to handle model updates
        async function updateModels(llmModel, embeddingModel, visionModel) {
            if (!llmModel || !embeddingModel) {
                Toastify({
                    text: "Please select both an LLM model and an embedding model",
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
                return;
            }

            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = `
                <div class="p-4 bg-blue-100 text-blue-700 rounded-md flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Updating models...
                </div>
            `;
            statusMessage.classList.remove('hidden');

            try {
                // Prepare data object with available models
                const data = {
                    llm_model: llmModel,
                    embedding_model: embeddingModel
                };

                // Add vision model if available
                if (visionModel) {
                    data.vision_model = visionModel;
                }

                // Add vision toggle states
                const useVisionCheckbox = document.getElementById('use_vision');
                if (useVisionCheckbox) {
                    data.use_vision = useVisionCheckbox.checked;
                }

                // Add vision during embedding toggle state
                const useVisionDuringEmbeddingCheckbox = document.getElementById('use_vision_during_embedding');
                if (useVisionDuringEmbeddingCheckbox) {
                    data.use_vision_during_embedding = useVisionDuringEmbeddingCheckbox.checked;
                }

                // Add image filtering settings
                const filterPdfImagesCheckbox = document.getElementById('filter_pdf_images');
                if (filterPdfImagesCheckbox) {
                    data.filter_pdf_images = filterPdfImagesCheckbox.checked;
                }

                // Add filter sensitivity
                const sensitivityLow = document.getElementById('sensitivity_low');
                const sensitivityMedium = document.getElementById('sensitivity_medium');
                const sensitivityHigh = document.getElementById('sensitivity_high');

                if (sensitivityLow && sensitivityLow.checked) {
                    data.filter_sensitivity = 'low';
                } else if (sensitivityMedium && sensitivityMedium.checked) {
                    data.filter_sensitivity = 'medium';
                } else if (sensitivityHigh && sensitivityHigh.checked) {
                    data.filter_sensitivity = 'high';
                }

                // Add max PDF images to analyze
                const maxPdfImages = document.getElementById('max_pdf_images');
                if (maxPdfImages && maxPdfImages.value) {
                    data.max_pdf_images = parseInt(maxPdfImages.value);
                }

                // Add show filtered images toggle
                const showFilteredImagesCheckbox = document.getElementById('show_filtered_images');
                if (showFilteredImagesCheckbox) {
                    data.show_filtered_images = showFilteredImagesCheckbox.checked;
                }

                // Add model parameters
                const temperature = document.getElementById('temperature');
                if (temperature) {
                    data.temperature = parseFloat(temperature.value);
                }

                const topP = document.getElementById('top_p');
                if (topP) {
                    data.top_p = parseFloat(topP.value);
                }

                const topK = document.getElementById('top_k');
                if (topK) {
                    data.top_k = parseInt(topK.value);
                }

                const repeatPenalty = document.getElementById('repeat_penalty');
                if (repeatPenalty) {
                    data.repeat_penalty = parseFloat(repeatPenalty.value);
                }

                const numCtx = document.getElementById('num_ctx');
                if (numCtx) {
                    data.num_ctx = parseInt(numCtx.value);
                }

                const numPredict = document.getElementById('num_predict');
                if (numPredict) {
                    data.num_predict = parseInt(numPredict.value);
                }

                const systemPrompt = document.getElementById('system_prompt');
                if (systemPrompt) {
                    data.system_prompt = systemPrompt.value;
                }

                const res = await fetch('/admin/models', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const json = await res.json();

                statusMessage.innerHTML = `
                    <div class="p-4 ${res.ok ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'} rounded-md">
                        ${json.message || json.error || (res.ok ? "Models updated successfully" : "Failed to update models")}
                    </div>
                `;

                Toastify({
                    text: json.message || json.error || (res.ok ? "Models updated successfully" : "Failed to update models"),
                    duration: 3000,
                    backgroundColor: res.ok ? "#00C851" : "#ff4444"
                }).showToast();

                // Reload the page after a short delay to reflect changes
                if (res.ok) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            } catch (error) {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-red-100 text-red-700 rounded-md">
                        Error: ${error.message || "Failed to update models"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${error.message || "Failed to update models"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        }

        // Handle form submission
        document.getElementById('modelForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const llmModel = formData.get('llm_model');
            const embeddingModel = formData.get('embedding_model');
            const visionModel = formData.get('vision_model');
            updateModels(llmModel, embeddingModel, visionModel);
        });

        // Function to save current models as default
        async function saveAsDefault() {
            const formData = new FormData(document.getElementById('modelForm'));
            const llmModel = formData.get('llm_model');
            const embeddingModel = formData.get('embedding_model');
            const visionModel = formData.get('vision_model'); // Get vision model if available

            if (!llmModel || !embeddingModel) {
                Toastify({
                    text: "Please select both an LLM model and an embedding model",
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
                return;
            }

            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = `
                <div class="p-4 bg-blue-100 text-blue-700 rounded-md flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving default models...
                </div>
            `;
            statusMessage.classList.remove('hidden');

            // Prepare data object with available models
            const data = {
                llm_model: llmModel,
                embedding_model: embeddingModel
            };

            // Add vision model if available
            if (visionModel) {
                data.vision_model = visionModel;
            }

            // Add vision toggle states
            const useVisionCheckbox = document.getElementById('use_vision');
            if (useVisionCheckbox) {
                data.use_vision = useVisionCheckbox.checked;
            }

            // Add vision during embedding toggle state
            const useVisionDuringEmbeddingCheckbox = document.getElementById('use_vision_during_embedding');
            if (useVisionDuringEmbeddingCheckbox) {
                data.use_vision_during_embedding = useVisionDuringEmbeddingCheckbox.checked;
            }

            // Add image filtering settings
            const filterPdfImagesCheckbox = document.getElementById('filter_pdf_images');
            if (filterPdfImagesCheckbox) {
                data.filter_pdf_images = filterPdfImagesCheckbox.checked;
            }

            // Add filter sensitivity
            const sensitivityLow = document.getElementById('sensitivity_low');
            const sensitivityMedium = document.getElementById('sensitivity_medium');
            const sensitivityHigh = document.getElementById('sensitivity_high');

            if (sensitivityLow && sensitivityLow.checked) {
                data.filter_sensitivity = 'low';
            } else if (sensitivityMedium && sensitivityMedium.checked) {
                data.filter_sensitivity = 'medium';
            } else if (sensitivityHigh && sensitivityHigh.checked) {
                data.filter_sensitivity = 'high';
            }

            // Add max PDF images to analyze
            const maxPdfImages = document.getElementById('max_pdf_images');
            if (maxPdfImages && maxPdfImages.value) {
                data.max_pdf_images = parseInt(maxPdfImages.value);
            }

            // Add show filtered images toggle
            const showFilteredImagesCheckbox = document.getElementById('show_filtered_images');
            if (showFilteredImagesCheckbox) {
                data.show_filtered_images = showFilteredImagesCheckbox.checked;
            }

            // Add model parameters
            const temperature = document.getElementById('temperature');
            if (temperature) {
                data.temperature = parseFloat(temperature.value);
            }

            const topP = document.getElementById('top_p');
            if (topP) {
                data.top_p = parseFloat(topP.value);
            }

            const topK = document.getElementById('top_k');
            if (topK) {
                data.top_k = parseInt(topK.value);
            }

            const repeatPenalty = document.getElementById('repeat_penalty');
            if (repeatPenalty) {
                data.repeat_penalty = parseFloat(repeatPenalty.value);
            }

            const numCtx = document.getElementById('num_ctx');
            if (numCtx) {
                data.num_ctx = parseInt(numCtx.value);
            }

            const numPredict = document.getElementById('num_predict');
            if (numPredict) {
                data.num_predict = parseInt(numPredict.value);
            }

            const systemPrompt = document.getElementById('system_prompt');
            if (systemPrompt) {
                data.system_prompt = systemPrompt.value;
            }

            try {
                const res = await fetch('/admin/models/default', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const json = await res.json();

                statusMessage.innerHTML = `
                    <div class="p-4 ${res.ok ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'} rounded-md">
                        ${json.message || json.error || (res.ok ? "Default models saved successfully" : "Failed to save default models")}
                    </div>
                `;

                Toastify({
                    text: json.message || json.error || (res.ok ? "Default models saved successfully" : "Failed to save default models"),
                    duration: 3000,
                    backgroundColor: res.ok ? "#00C851" : "#ff4444"
                }).showToast();

            } catch (error) {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-red-100 text-red-700 rounded-md">
                        Error: ${error.message || "Failed to save default models"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${error.message || "Failed to save default models"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        }

        // Handle "Save as Default" button click
        document.getElementById('saveDefaultsButton').addEventListener('click', saveAsDefault);

        // Handle "Use Default Models" button click
        const defaultModelsButton = document.getElementById('useDefaultModels');
        if (defaultModelsButton) {
            defaultModelsButton.addEventListener('click', () => {
                // Use default models
                const defaultLLM = "llama3.1:8b-instruct-q4_K_M";
                const defaultEmbedding = "mxbai-embed-large:latest";

                // Create radio buttons for the default models
                const modelContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-4');
                if (modelContainer) {
                    // Clear existing content
                    modelContainer.innerHTML = '';

                    // Add default models
                    const defaultModels = [
                        { name: "llama3.1:8b-instruct-q4_K_M", size: "Medium (8B parameters, 4.9 GB)" },
                        { name: "mistral:latest", size: "Medium (7B parameters, 4.1 GB)" },
                        { name: "llama3.2:3b-instruct-q4_K_M", size: "Small (3B parameters, 2.0 GB)" },
                        { name: "gemma3:1b", size: "Small (1B parameters, 815 MB)" }
                    ];

                    defaultModels.forEach((model, index) => {
                        const modelElement = document.createElement('div');
                        modelElement.className = 'relative';
                        modelElement.innerHTML = `
                            <input type="radio" id="llm_${index + 1}" name="llm_model" value="${model.name}"
                                   class="hidden peer" ${model.name === defaultLLM ? 'checked' : ''}>
                            <label for="llm_${index + 1}"
                                   class="block p-4 bg-white border rounded-lg cursor-pointer
                                          peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                          hover:bg-gray-50 transition-all">
                                <div class="font-medium text-gray-900">${model.name}</div>
                                <div class="text-xs text-gray-500 mt-1">
                                    Size: ${model.size}
                                </div>
                            </label>
                            <div class="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                        `;
                        modelContainer.appendChild(modelElement);
                    });

                    // Update the warning message
                    const warningContainer = document.querySelector('.bg-yellow-50.border-l-4.border-yellow-400');
                    if (warningContainer) {
                        warningContainer.innerHTML = `
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        Using default models. Ollama connection not detected.
                                    </p>
                                    <p class="text-sm text-yellow-700 mt-1">
                                        You can select a model from the list below and continue using the application.
                                    </p>
                                </div>
                            </div>
                        `;
                    }
                }

                // Create default embedding models
                const embeddingSection = document.querySelector('h2.text-lg.font-medium.text-gray-800.mb-4:nth-of-type(2)').closest('.bg-gray-50.p-4.rounded-lg.border.border-gray-200');

                // Default embedding models
                const defaultEmbeddingModels = [
                    { name: "mxbai-embed-large:latest", size: "Balanced embedding model (669 MB)" },
                    { name: "bge-m3:latest", size: "High-quality embedding model (1.2 GB)" },
                    { name: "nomic-embed-text:latest", size: "Lightweight embedding model (274 MB)" }
                ];

                if (embeddingSection) {
                    // Check if there's a warning message (no models found)
                    const warningContainer = embeddingSection.querySelector('.bg-yellow-50.border-l-4.border-yellow-400');

                    if (warningContainer) {
                        // Replace warning with grid of models
                        const gridContainer = document.createElement('div');
                        gridContainer.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';

                        defaultEmbeddingModels.forEach((model, index) => {
                            const modelElement = document.createElement('div');
                            modelElement.className = 'relative';
                            modelElement.innerHTML = `
                                <input type="radio" id="embed_${index + 1}" name="embedding_model" value="${model.name}"
                                       class="hidden peer" ${model.name === defaultEmbedding ? 'checked' : ''}>
                                <label for="embed_${index + 1}"
                                       class="block p-4 bg-white border rounded-lg cursor-pointer
                                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                              hover:bg-gray-50 transition-all">
                                    <div class="font-medium text-gray-900">${model.name}</div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        Size: ${model.size}
                                    </div>
                                </label>
                                <div class="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                            `;
                            gridContainer.appendChild(modelElement);
                        });

                        // Replace warning with grid
                        warningContainer.replaceWith(gridContainer);
                    } else {
                        // If there's already a grid, update it
                        const embeddingContainer = embeddingSection.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-4');
                        if (embeddingContainer) {
                            // Clear existing content
                            embeddingContainer.innerHTML = '';

                            // Add default embedding models
                            defaultEmbeddingModels.forEach((model, index) => {
                                const modelElement = document.createElement('div');
                                modelElement.className = 'relative';
                                modelElement.innerHTML = `
                                    <input type="radio" id="embed_${index + 1}" name="embedding_model" value="${model.name}"
                                           class="hidden peer" ${model.name === defaultEmbedding ? 'checked' : ''}>
                                    <label for="embed_${index + 1}"
                                           class="block p-4 bg-white border rounded-lg cursor-pointer
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 transition-all">
                                        <div class="font-medium text-gray-900">${model.name}</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Size: ${model.size}
                                        </div>
                                    </label>
                                    <div class="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                                `;
                                embeddingContainer.appendChild(modelElement);
                            });
                        }
                    }
                }

                Toastify({
                    text: "Default models loaded. You can now select and update models.",
                    duration: 3000,
                    backgroundColor: "#00C851"
                }).showToast();
            });
        }
    </script>
</body>
</html>