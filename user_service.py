"""
User service module for the user management system.

This module provides functions for user CRUD operations and related functionality.
"""

import os
import datetime
import logging
import uuid
import re
from typing import Tuple, List, Dict, Any, Optional, Union
from email_validator import validate_email, EmailNotValidError
from flask import request

import db
import security
from user_models import User
from permissions import log_permission_change

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Profile picture settings
PROFILE_PICS_DIR = os.getenv("PROFILE_PICS_DIR", "./_temp/profile_pics")
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
MAX_PROFILE_PIC_SIZE = 2 * 1024 * 1024  # 2MB


def get_user_by_id(user_id: Union[int, str]) -> Optional[User]:
    """
    Get a user by ID.

    Args:
        user_id: The ID of the user to get

    Returns:
        A User object if found, None otherwise
    """
    try:
        user_id = int(user_id)
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, profile_picture, full_name, group_id,
                   password_changed_at
            FROM users
            WHERE user_id = ?
        """, (user_id,))

        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Failed to get user by ID: {str(e)}")
        return None


def get_user_by_username(username: str) -> Optional[User]:
    """
    Get a user by username.

    Args:
        username: The username of the user to get

    Returns:
        A User object if found, None otherwise
    """
    try:
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, profile_picture, full_name, group_id,
                   password_changed_at
            FROM users
            WHERE username = ?
        """, (username,))

        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Failed to get user by username: {str(e)}")
        return None


def get_user_by_email(email: str) -> Optional[User]:
    """
    Get a user by email.

    Args:
        email: The email of the user to get

    Returns:
        A User object if found, None otherwise
    """
    try:
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, profile_picture, full_name, group_id,
                   password_changed_at
            FROM users
            WHERE email = ?
        """, (email,))

        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Failed to get user by email: {str(e)}")
        return None


def get_all_users(page: int = 1, per_page: int = 25, search: Optional[str] = None,
                 sort_by: str = 'username', sort_order: str = 'asc') -> Tuple[List[Dict[str, Any]], int]:
    """
    Get all users with pagination and search.

    Args:
        page: The page number
        per_page: The number of users per page
        search: Search term to filter users
        sort_by: Column to sort by
        sort_order: Sort order ('asc' or 'desc')

    Returns:
        A tuple of (users, total_count)
    """
    try:
        # Validate sort parameters
        valid_sort_columns = ['username', 'email', 'role', 'account_status', 'created_at', 'last_login']
        if sort_by not in valid_sort_columns:
            sort_by = 'username'

        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'asc'

        # Base query
        query = """
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, full_name, profile_picture, group_id
            FROM users
        """

        count_query = "SELECT COUNT(*) as count FROM users"

        params = []

        # Add search condition if provided
        if search:
            search_condition = " WHERE username LIKE ? OR email LIKE ? OR full_name LIKE ?"
            query += search_condition
            count_query += search_condition
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        # Add sorting
        query += f" ORDER BY {sort_by} {sort_order}"

        # Add pagination
        query += " LIMIT ? OFFSET ?"
        params.extend([per_page, (page - 1) * per_page])

        # Get users
        users = db.execute_query(query, tuple(params))

        # Get total count
        count_result = db.execute_query(count_query, tuple(params[:3] if search else []))
        total_count = count_result[0]['count'] if count_result else 0

        # Add group information
        for user in users:
            if user.get('group_id'):
                group_data = db.execute_query(
                    "SELECT name FROM permission_groups WHERE group_id = ?",
                    (user['group_id'],)
                )
                user['group_name'] = group_data[0]['name'] if group_data else None
            else:
                user['group_name'] = None

        return users, total_count
    except Exception as e:
        logger.error(f"Failed to get users: {str(e)}")
        return [], 0


def register_user(username: str, email: str, password: str, role: str = 'viewer',
                 full_name: Optional[str] = None, require_approval: bool = True) -> Tuple[bool, str]:
    """
    Register a new user.

    Args:
        username: The username for the new user
        email: The email for the new user
        password: The password for the new user
        role: The role for the new user
        full_name: The full name for the new user
        require_approval: Whether admin approval is required

    Returns:
        A tuple of (success, message)
    """
    try:
        # Validate username (3-20 alphanumeric characters)
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
            return False, "Username must be 3-20 alphanumeric characters or underscores"

        # Validate email
        try:
            validate_email(email)
        except EmailNotValidError:
            return False, "Invalid email address"

        # Validate password complexity
        is_valid, error_message = security.validate_password_complexity(password)
        if not is_valid:
            return False, error_message

        # Check if username or email already exists
        existing = db.execute_query(
            "SELECT username, email FROM users WHERE username = ? OR email = ?",
            (username, email)
        )

        if existing:
            if existing[0]['username'] == username:
                return False, "Username already exists"
            if existing[0]['email'] == email:
                return False, "Email already exists"

        # Hash the password
        password_hash = security.hash_password(password)

        # Generate email verification token
        verification_token = str(uuid.uuid4())
        verification_expiry = (datetime.datetime.now() + datetime.timedelta(seconds=security.EMAIL_VERIFY_EXPIRY)).isoformat()

        # Set initial account status
        account_status = 'pending' if require_approval else 'active'

        # Insert the new user
        user_id = db.execute_insert('''
            INSERT INTO users (
                username, password_hash, email, role, account_status,
                verification_token, verification_token_expiry, full_name,
                password_changed_at, email_verified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            username,
            password_hash,
            email,
            role,
            account_status,
            verification_token,
            verification_expiry,
            full_name,
            datetime.datetime.now().isoformat(),  # Set password_changed_at to now
            0  # Not verified yet
        ))

        # Automatically assign users to the corresponding permission group based on role
        if role in ['editor', 'viewer']:
            group_name = 'Editor' if role == 'editor' else 'Viewer'
            group_data = db.execute_query(
                "SELECT group_id FROM permission_groups WHERE name = ?",
                (group_name,)
            )

            if group_data and group_data[0]['group_id']:
                group_id = group_data[0]['group_id']
                db.execute_update(
                    "UPDATE users SET group_id = ? WHERE user_id = ?",
                    (group_id, user_id)
                )

        # Log the user creation
        log_user_activity(
            user_id=user_id,
            action_type="user_created",
            details=f"User created with role {role}",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        # Send verification email
        send_verification_email(username, email, verification_token)

        return True, "User registered successfully"
    except Exception as e:
        logger.error(f"Failed to register user: {str(e)}")
        return False, f"An error occurred during registration: {str(e)}"


def send_verification_email(username: str, email: str, token: str) -> bool:
    """
    Send an email verification email.

    Args:
        username: The username of the user
        email: The email address to send to
        token: The verification token

    Returns:
        True if successful, False otherwise
    """
    try:
        from flask import request
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # Email configuration
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME", "")
        smtp_password = os.getenv("SMTP_PASSWORD", "")
        smtp_from_email = os.getenv("SMTP_FROM_EMAIL", "<EMAIL>")

        # Skip sending email if SMTP credentials are not configured
        if not smtp_username or not smtp_password:
            logger.warning("SMTP credentials not configured. Skipping verification email.")
            return True

        # Create verification URL
        verification_url = f"{request.host_url.rstrip('/')}/verify_email/{token}"

        # Create email message
        message = MIMEMultipart()
        message["From"] = smtp_from_email
        message["To"] = email
        message["Subject"] = "Verify Your Email Address"

        # Email body
        body = f"""
        <html>
        <body>
            <h2>Welcome to the Document Management System, {username}!</h2>
            <p>Thank you for registering. Please verify your email address by clicking the link below:</p>
            <p><a href="{verification_url}">Verify Email Address</a></p>
            <p>This link will expire in 24 hours.</p>
            <p>If you did not register for an account, please ignore this email.</p>
        </body>
        </html>
        """

        message.attach(MIMEText(body, "html"))

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(message)

        return True
    except Exception as e:
        logger.error(f"Failed to send verification email: {str(e)}")
        return False


def log_user_activity(user_id: int, action_type: str, details: str,
                     status: str = 'success', ip_address: Optional[str] = None) -> bool:
    """
    Log user activity.

    Args:
        user_id: The ID of the user
        action_type: The type of action
        details: Details about the action
        status: The status of the action
        ip_address: The IP address of the user

    Returns:
        True if successful, False otherwise
    """
    try:
        db.execute_insert("""
            INSERT INTO user_activity_logs (
                user_id, action_type, details, status, ip_address, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            user_id,
            action_type,
            details,
            status,
            ip_address,
            datetime.datetime.now().isoformat()
        ))

        return True
    except Exception as e:
        logger.error(f"Failed to log user activity: {str(e)}")
        return False
