#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add time-based greetings to the database.
Run this script to populate the database with sample time-based greetings.
"""

import sqlite3
import os
from datetime import datetime

# Database path
DB_PATH = os.getenv("DB_PATH", "./content_db.sqlite")

def add_time_based_greetings():
    """Add sample time-based greetings to the database."""
    
    # Time-based greeting templates
    time_based_greetings = [
        # Morning greetings (6 AM - 12 PM)
        ('time_based', 'Good morning, {name}!', '{"time_period": "morning"}', 1, 3),
        ('time_based', 'Hello {name}, hope you\'re having a great morning!', '{"time_period": "morning"}', 1, 2),
        ('time_based', 'Morning, {name}! Ready to explore ERDB knowledge?', '{"time_period": "morning"}', 1, 2),
        ('time_based', '{name}, good morning! How can I help you today?', '{"time_period": "morning"}', 1, 2),
        ('time_based', 'Rise and shine, {name}! What would you like to know?', '{"time_period": "morning"}', 1, 1),
        
        # Afternoon greetings (12 PM - 6 PM)
        ('time_based', 'Good afternoon, {name}!', '{"time_period": "afternoon"}', 1, 3),
        ('time_based', 'Hello {name}, hope your day is going well!', '{"time_period": "afternoon"}', 1, 2),
        ('time_based', 'Afternoon, {name}! What brings you here today?', '{"time_period": "afternoon"}', 1, 2),
        ('time_based', '{name}, good afternoon! How can I assist you?', '{"time_period": "afternoon"}', 1, 2),
        ('time_based', 'Hello {name}, ready for some afternoon learning?', '{"time_period": "afternoon"}', 1, 1),
        
        # Evening greetings (6 PM - 12 AM)
        ('time_based', 'Good evening, {name}!', '{"time_period": "evening"}', 1, 3),
        ('time_based', 'Hello {name}, hope you\'re having a pleasant evening!', '{"time_period": "evening"}', 1, 2),
        ('time_based', 'Evening, {name}! What can I help you with tonight?', '{"time_period": "evening"}', 1, 2),
        ('time_based', '{name}, good evening! Ready to explore some knowledge?', '{"time_period": "evening"}', 1, 2),
        ('time_based', 'Hello {name}, working late? Let me help you find what you need!', '{"time_period": "evening"}', 1, 1),
        
        # Night greetings (12 AM - 6 AM)
        ('time_based', 'Hello {name}, burning the midnight oil?', '{"time_period": "night"}', 1, 2),
        ('time_based', '{name}, you\'re up late! How can I help?', '{"time_period": "night"}', 1, 2),
        ('time_based', 'Good evening, {name}! What keeps you up tonight?', '{"time_period": "night"}', 1, 2),
        ('time_based', 'Hello {name}, night owl! What would you like to know?', '{"time_period": "night"}', 1, 1),
        ('time_based', '{name}, late night research? I\'m here to help!', '{"time_period": "night"}', 1, 1),
    ]
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if time-based greetings already exist
        cursor.execute("SELECT COUNT(*) FROM greeting_templates WHERE template_type = 'time_based'")
        existing_count = cursor.fetchone()[0]
        
        if existing_count > 0:
            print(f"Found {existing_count} existing time-based greetings.")
            response = input("Do you want to add more time-based greetings? (y/n): ")
            if response.lower() != 'y':
                print("Cancelled.")
                return
        
        # Insert time-based greetings
        added_count = 0
        for template_type, greeting_text, context_conditions, is_active, weight in time_based_greetings:
            cursor.execute('''
                INSERT INTO greeting_templates 
                (template_type, greeting_text, context_conditions, is_active, weight)
                VALUES (?, ?, ?, ?, ?)
            ''', (template_type, greeting_text, context_conditions, is_active, weight))
            added_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ Successfully added {added_count} time-based greetings to the database!")
        print(f"📊 Total time-based greetings now: {existing_count + added_count}")
        print("\n🔍 You can view and manage these greetings at:")
        print("   http://localhost:8080/admin/greeting_management")
        print("\n⚠️  Note: Time-based selection logic will be implemented in Phase 2.")
        print("   Currently, these greetings will be randomly selected.")
        
    except Exception as e:
        print(f"❌ Error adding time-based greetings: {str(e)}")

if __name__ == "__main__":
    print("🕐 Adding Time-Based Greetings to ERDB Knowledge Hub")
    print("=" * 50)
    add_time_based_greetings()
