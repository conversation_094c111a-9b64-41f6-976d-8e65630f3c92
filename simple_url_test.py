#!/usr/bin/env python3
"""
Simple test to verify URL extraction works correctly.
"""

import re
import json
import urllib.parse

def test_url_extraction():
    """Test URL extraction from malformed content"""
    
    # Test case: URL-encoded dictionary (the actual problem case)
    malformed_url = "%7B'url':%20'https://erdbservices.denr.gov.ph/eskris/service_requests_for_client.php?operation=insert&service_id=6&requested_iec_id=61',%20'source':%20'url',%20'metadata':%20{'index':%200,%20'source':%20'url_scrape'}}"
    
    print("Testing URL extraction from malformed content:")
    print(f"Input: {malformed_url[:100]}...")
    
    # Method 1: Direct regex extraction
    url_match = re.search(r'https?://[^\s\'"}]+', malformed_url)
    if url_match:
        extracted_url = url_match.group(0)
        print(f"Method 1 (regex): {extracted_url}")
    
    # Method 2: URL decode then JSON parse
    try:
        decoded_content = urllib.parse.unquote(malformed_url)
        print(f"Decoded: {decoded_content[:100]}...")
        
        if decoded_content.startswith('{') and decoded_content.endswith('}'):
            parsed_dict = json.loads(decoded_content)
            if isinstance(parsed_dict, dict) and 'url' in parsed_dict:
                extracted_url = parsed_dict['url']
                print(f"Method 2 (JSON): {extracted_url}")
    except Exception as e:
        print(f"Method 2 failed: {e}")
    
    # Expected result
    expected = "https://erdbservices.denr.gov.ph/eskris/service_requests_for_client.php?operation=insert&service_id=6&requested_iec_id=61"
    print(f"Expected: {expected}")

if __name__ == "__main__":
    test_url_extraction()
