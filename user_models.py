"""
User and permission-related data models for the document management application.

This module defines the data models for users, permissions, and related entities.
"""

import datetime
from typing import Optional, List, Dict, Any, Union
from flask_login import UserMixin

import db
import security


class User(UserMixin):
    """User model representing a system user."""
    
    def __init__(self, user_data: Dict[str, Any]):
        """
        Initialize a user from database data.
        
        Args:
            user_data: Dictionary containing user data from the database
        """
        self.user_id = user_data.get('user_id')
        self.username = user_data.get('username')
        self.email = user_data.get('email')
        self.role = user_data.get('role')
        self.account_status = user_data.get('account_status')
        self.created_at = user_data.get('created_at')
        self.last_login = user_data.get('last_login')
        self.failed_login_attempts = user_data.get('failed_login_attempts', 0)
        self.email_verified = bool(user_data.get('email_verified', 0))
        self.profile_picture = user_data.get('profile_picture')
        self.full_name = user_data.get('full_name')
        self.group_id = user_data.get('group_id')
        self.password_changed_at = user_data.get('password_changed_at')
        
        # Cache for permissions
        self._permission_cache = {}
    
    @property
    def is_active(self) -> bool:
        """Check if the user account is active."""
        return self.account_status == 'active'
    
    @property
    def is_authenticated(self) -> bool:
        """Check if the user is authenticated."""
        return True
    
    @property
    def is_anonymous(self) -> bool:
        """Check if the user is anonymous."""
        return False
    
    @property
    def password_expired(self) -> bool:
        """Check if the user's password has expired."""
        from security import PASSWORD_EXPIRY_DAYS
        
        if not self.password_changed_at:
            return True
            
        try:
            # Parse the ISO format datetime
            changed_at = datetime.datetime.fromisoformat(self.password_changed_at)
            expiry_date = changed_at + datetime.timedelta(days=PASSWORD_EXPIRY_DAYS)
            return datetime.datetime.now() > expiry_date
        except (ValueError, TypeError):
            # If there's any error parsing the date, consider the password expired
            return True
    
    def get_id(self) -> str:
        """Return the user ID as a string, required by Flask-Login."""
        return str(self.user_id)
    
    def has_permission(self, category: str, required_permission: str = 'read') -> bool:
        """
        Check if the user has the required permission for a category.
        
        Note: This method is maintained for backward compatibility.
        It now uses the dashboard permission system internally.
        
        Args:
            category: The category to check permissions for
            required_permission: The permission level required ('read', 'write', or 'admin')
            
        Returns:
            True if the user has the required permission, False otherwise
        """
        # Admins have all permissions
        if self.role == 'admin':
            return True
        
        # Map category permissions to dashboard functions
        if required_permission == 'read':
            return self.has_dashboard_permission(f'view_category_{category}')
        elif required_permission == 'write':
            return self.has_dashboard_permission(f'edit_category_{category}')
        elif required_permission == 'admin':
            return self.has_dashboard_permission(f'manage_category_{category}')
        
        return False
    
    def has_dashboard_permission(self, function_name: str) -> bool:
        """
        Check if the user has permission to access a dashboard function.
        
        Permission checking order:
        1. Admin role (always has all permissions)
        2. Special cases (edit_own_profile)
        3. Individual permission overrides
        4. Group-based permissions
        5. Legacy dashboard permissions
        6. Role-based defaults
        7. Default to no access
        
        Args:
            function_name: The dashboard function to check permissions for
            
        Returns:
            True if the user has permission, False otherwise
        """
        # Check cache first
        if function_name in self._permission_cache:
            return self._permission_cache[function_name]
        
        # 1. Admins have all permissions
        if self.role == 'admin':
            self._permission_cache[function_name] = True
            return True
        
        # 2. Special case: All users can edit their own profile
        if function_name == 'edit_own_profile':
            self._permission_cache[function_name] = True
            return True
        
        # 3. Check for permission overrides first (highest priority)
        override_result = db.execute_query(
            "SELECT enabled FROM permission_overrides WHERE user_id = ? AND function_name = ?",
            (self.user_id, function_name)
        )
        
        if override_result:
            has_permission = bool(override_result[0]['enabled'])
            self._permission_cache[function_name] = has_permission
            return has_permission
        
        # 4. Check for group-based permissions if user belongs to a group
        if self.group_id:
            group_result = db.execute_query(
                "SELECT enabled FROM group_permissions WHERE group_id = ? AND function_name = ?",
                (self.group_id, function_name)
            )
            
            if group_result:
                has_permission = bool(group_result[0]['enabled'])
                self._permission_cache[function_name] = has_permission
                return has_permission
        
        # 5. Fall back to legacy dashboard permissions
        legacy_result = db.execute_query(
            "SELECT enabled FROM dashboard_permissions WHERE user_id = ? AND function_name = ?",
            (self.user_id, function_name)
        )
        
        if legacy_result:
            has_permission = bool(legacy_result[0]['enabled'])
            self._permission_cache[function_name] = has_permission
            return has_permission
        
        # 6. Role-based defaults if no permission record exists
        if self.role == 'editor' and function_name in [
            'upload_content', 'manage_files', 'chat_history', 
            'chat_sessions', 'ai_analytics', 'clean_urls'
        ]:
            self._permission_cache[function_name] = True
            return True
        elif self.role == 'viewer' and function_name in [
            'chat_history', 'chat_sessions', 'ai_analytics'
        ]:
            self._permission_cache[function_name] = True
            return True
        
        # 7. Default to no access
        self._permission_cache[function_name] = False
        return False
    
    def clear_permission_cache(self) -> None:
        """Clear the permission cache."""
        self._permission_cache = {}


class PermissionGroup:
    """Model representing a permission group."""
    
    def __init__(self, group_data: Dict[str, Any]):
        """
        Initialize a permission group from database data.
        
        Args:
            group_data: Dictionary containing group data from the database
        """
        self.group_id = group_data.get('group_id')
        self.name = group_data.get('name')
        self.description = group_data.get('description')
        self.created_at = group_data.get('created_at')
        self.updated_at = group_data.get('updated_at')
        self.permissions = {}
        self.user_count = group_data.get('user_count', 0)
    
    def load_permissions(self) -> None:
        """Load permissions for this group from the database."""
        permissions_data = db.execute_query(
            "SELECT function_name, enabled FROM group_permissions WHERE group_id = ?",
            (self.group_id,)
        )
        
        self.permissions = {
            item['function_name']: bool(item['enabled']) 
            for item in permissions_data
        }
    
    @staticmethod
    def get_all_groups() -> List['PermissionGroup']:
        """
        Get all permission groups with their permissions.
        
        Returns:
            List of PermissionGroup objects
        """
        groups_data = db.execute_query("SELECT * FROM permission_groups ORDER BY name")
        
        groups = []
        for group_data in groups_data:
            group = PermissionGroup(group_data)
            
            # Get count of users in this group
            user_count_data = db.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE group_id = ?", 
                (group.group_id,)
            )
            group.user_count = user_count_data[0]['count'] if user_count_data else 0
            
            # Load permissions
            group.load_permissions()
            
            groups.append(group)
            
        return groups
