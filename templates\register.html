<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Document Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center py-12">
    <div class="container mx-auto px-4 max-w-md">
        <div class="bg-white rounded-lg shadow-md p-8">
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold text-gray-800">Create an Account</h1>
                <p class="text-gray-600 mt-2">Register to access the Document Management System</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="mb-4 p-4 rounded-md {% if category == 'error' %}bg-red-100 text-red-700{% elif category == 'warning' %}bg-yellow-100 text-yellow-700{% else %}bg-green-100 text-green-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('user.register') }}">
                {{ form.csrf_token }}
                
                <div class="mb-4">
                    <label for="username" class="block text-gray-700 text-sm font-bold mb-2">Username</label>
                    {{ form.username(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                    {% if form.username.errors %}
                        <p class="text-red-500 text-xs italic mt-1">{{ form.username.errors[0] }}</p>
                    {% endif %}
                    <p class="text-gray-500 text-xs mt-1">3-20 characters, alphanumeric and underscores only</p>
                </div>
                
                <div class="mb-4">
                    <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                    {{ form.email(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                    {% if form.email.errors %}
                        <p class="text-red-500 text-xs italic mt-1">{{ form.email.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="full_name" class="block text-gray-700 text-sm font-bold mb-2">Full Name (Optional)</label>
                    {{ form.full_name(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                    {% if form.full_name.errors %}
                        <p class="text-red-500 text-xs italic mt-1">{{ form.full_name.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                    {{ form.password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                    {% if form.password.errors %}
                        <p class="text-red-500 text-xs italic mt-1">{{ form.password.errors[0] }}</p>
                    {% endif %}
                    <p class="text-gray-500 text-xs mt-1">Minimum 8 characters, must include uppercase, lowercase, number, and special character</p>
                </div>
                
                <div class="mb-6">
                    <label for="confirm_password" class="block text-gray-700 text-sm font-bold mb-2">Confirm Password</label>
                    {{ form.confirm_password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                    {% if form.confirm_password.errors %}
                        <p class="text-red-500 text-xs italic mt-1">{{ form.confirm_password.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div class="mb-6">
                    <div class="flex items-center">
                        {{ form.accept_tos(class="mr-2") }}
                        <label for="accept_tos" class="text-sm text-gray-700">I accept the <a href="#" class="text-blue-600 hover:underline">Terms of Service</a></label>
                    </div>
                    {% if form.accept_tos.errors %}
                        <p class="text-red-500 text-xs italic mt-1">{{ form.accept_tos.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div class="mb-6">
                    <div class="g-recaptcha" data-sitekey="{{ recaptcha_site_key }}"></div>
                    {% if recaptcha_error %}
                        <p class="text-red-500 text-xs italic mt-1">{{ recaptcha_error }}</p>
                    {% endif %}
                </div>
                
                <div class="flex items-center justify-between">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full">
                        Register
                    </button>
                </div>
            </form>
            
            <div class="text-center mt-6">
                <p class="text-sm text-gray-600">
                    Already have an account? 
                    <a href="{{ url_for('user.login') }}" class="text-blue-600 hover:underline">Log In</a>
                </p>
            </div>
            
            <div class="text-center mt-4">
                <a href="/" class="text-sm text-gray-600 hover:underline">&larr; Back to Home</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
</body>
</html>
