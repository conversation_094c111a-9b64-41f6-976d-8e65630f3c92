#!/usr/bin/env python3
"""
Migration script to add the missing group_id column to the users table.
This script checks if the column exists and adds it if it doesn't.
"""

import os
import sqlite3
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Get database path from environment variable or use default
USER_DB_PATH = os.getenv("USER_DB_PATH", "./user_management.db")

def check_column_exists(cursor, table, column):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table})")
    columns = cursor.fetchall()
    return any(col[1] == column for col in columns)

def add_group_id_column():
    """Add the group_id column to the users table if it doesn't exist."""
    try:
        # Connect to the database
        conn = sqlite3.connect(USER_DB_PATH)
        cursor = conn.cursor()
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Check if the users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone() is None:
            logger.error("The users table does not exist. Please initialize the database first.")
            return False
        
        # Check if the group_id column already exists
        if check_column_exists(cursor, 'users', 'group_id'):
            logger.info("The group_id column already exists in the users table.")
            return True
        
        # Check if the permission_groups table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permission_groups'")
        if cursor.fetchone() is None:
            logger.error("The permission_groups table does not exist. Please initialize the database first.")
            return False
        
        # Add the group_id column to the users table
        logger.info("Adding group_id column to users table...")
        cursor.execute('''
            ALTER TABLE users
            ADD COLUMN group_id INTEGER
            REFERENCES permission_groups(group_id) ON DELETE SET NULL
        ''')
        
        # Create an index for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_group_id ON users(group_id)")
        
        # Commit the changes
        conn.commit()
        logger.info("Successfully added group_id column to users table.")
        
        return True
    except sqlite3.Error as e:
        logger.error(f"Database error: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    if add_group_id_column():
        logger.info("Migration completed successfully.")
    else:
        logger.error("Migration failed.")
