{% extends "chat_base.html" %}

{% block title %}Chat Sample - Document Management System{% endblock %}

{% block modals %}
<!-- Client Name Modal -->
<div id="clientNameModal" class="modal-backdrop d-none">
    <div class="modal-content">
        <h2 class="h4 text-primary mb-3">Welcome to ERDB AI</h2>
        <p class="text-secondary mb-2">This system provides information based on ERDB Knowledge Products.</p>
        <p class="text-secondary mb-3">Please enter your name to continue:</p>
        <div class="mb-3">
            <input type="text" id="clientNameInput" placeholder="Your name" class="form-control"
                onkeypress="if(event.key === 'Enter') saveClientName()">
        </div>
        <div class="alert alert-primary mb-3">
            <p class="mb-1"><strong>How to use:</strong> Select a category, ask questions about ERDB knowledge products, and get answers with citations and related resources.</p>
            <p class="mb-0">Your chat history will be preserved when you refresh the page, but will be completely cleared when you click Logout.</p>
        </div>
        <div class="d-grid">
            <button onclick="saveClientName()" class="btn btn-primary">
                <i class="fas fa-arrow-right me-2"></i>Continue
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Chat Box - Takes most of the screen -->
<div id="chat-box" class="chat-box flex-grow-1 overflow-auto"></div>

<!-- Input Area -->
<div class="chat-input-container">
    <form onsubmit="return false;">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select id="category" class="form-select">
                    <option value="" selected>-- Select Category --</option>
                    <option value="forestry">Forestry</option>
                    <option value="environment">Environment</option>
                    <option value="climate">Climate Change</option>
                    <option value="biodiversity">Biodiversity</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="model" class="form-label">AI Model</label>
                <select id="model" class="form-select">
                    <option value="llama3.1:8b-instruct-q4_K_M" selected>Llama 3.1 8B</option>
                    <option value="llama3.1:70b-instruct-q4_K_M">Llama 3.1 70B</option>
                    <option value="gemma3:4b-it-q4_K_M">Gemma 3 4B</option>
                    <option value="gemma3:12b-it-q4_K_M">Gemma 3 12B</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="anti-hallucination" class="form-label">Response Mode</label>
                <select id="anti-hallucination" class="form-select">
                    <option value="strict" selected>Strict</option>
                    <option value="balanced">Balanced</option>
                    <option value="creative">Creative</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="max-tokens" class="form-label">Max Tokens</label>
                <select id="max-tokens" class="form-select">
                    <option value="1024">1024</option>
                    <option value="2048" selected>2048</option>
                    <option value="4096">4096</option>
                    <option value="8192">8192</option>
                </select>
            </div>
        </div>

        <div class="mt-3">
            <label for="query" class="form-label">Your Question</label>
            <div class="input-group">
                <textarea id="query" class="form-control chat-input-textarea" rows="1"
                    placeholder="Enter your question about ERDB knowledge..."
                    onkeypress="handleKeyPress(event)"></textarea>
                <button onclick="sendQuery()" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Global variables
    let clientName = localStorage.getItem('clientName');
    let chatHistory = [];
    let isProcessing = false;

    // Generate device fingerprint for session tracking
    function generateDeviceFingerprint() {
        // Get screen information
        const screenInfo = `${screen.width}x${screen.height}x${screen.colorDepth}`;

        // Get timezone
        const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Get language
        const language = navigator.language || navigator.userLanguage;

        // Get platform
        const platform = navigator.platform;

        // Get CPU cores
        const cpuCores = navigator.hardwareConcurrency || 'unknown';

        // Get device memory if available
        const deviceMemory = navigator.deviceMemory || 'unknown';

        // Get Do Not Track setting
        const doNotTrack = navigator.doNotTrack || 'unknown';

        // Check if cookies are enabled
        const cookiesEnabled = navigator.cookieEnabled;

        // Get user agent (truncated)
        const userAgent = navigator.userAgent;

        // Add canvas fingerprinting (creates a unique image based on hardware/software)
        let canvasData = 'unknown';
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 200;
            canvas.height = 50;

            // Draw text with specific styling
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#1a73e8';
            ctx.fillText('ERDB Knowledge Products', 2, 2);

            // Add a colored rectangle
            ctx.fillStyle = '#e8731a';
            ctx.fillRect(100, 25, 80, 20);

            // Get canvas data
            canvasData = canvas.toDataURL().substring(0, 100);
        } catch (e) {
            console.error('Canvas fingerprinting failed:', e);
        }

        // Combine all values
        const fingerprint = `${screenInfo}-${timeZone}-${language}-${platform}-${cpuCores}-${deviceMemory}-${doNotTrack}-${cookiesEnabled}-${canvasData}-${userAgent.substring(0, 50)}`;

        // Create a hash of the fingerprint
        let hash = 0;
        for (let i = 0; i < fingerprint.length; i++) {
            const char = fingerprint.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }

        // Combine hash with base64 encoding for better uniqueness
        const base64 = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '');
        return `${Math.abs(hash).toString(16)}-${base64.substring(0, 16)}`;
    }

    // Save the fingerprint to localStorage
    localStorage.setItem('deviceFingerprint', generateDeviceFingerprint());

    // Function to generate varied personalized greetings using API
    async function getPersonalizedGreeting(name, greetingType = 'response') {
        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

            const response = await fetch('/api/greeting', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    client_name: name,
                    context: {
                        greeting_type: greetingType,
                        session_id: localStorage.getItem('deviceFingerprint')
                    }
                })
            });

            const result = await response.json();

            if (result.success) {
                return result.greeting;
            } else {
                // Fallback to hardcoded greeting
                return getFallbackGreeting(name);
            }
        } catch (error) {
            console.error('Error getting personalized greeting:', error);
            return getFallbackGreeting(name);
        }
    }

    // Fallback greeting function for when API fails
    function getFallbackGreeting(name) {
        const greetings = [
            `Hello ${name}!`,
            `Hi ${name}!`,
            `Welcome ${name}!`,
            `Good to see you, ${name}!`
        ];
        return greetings[Math.floor(Math.random() * greetings.length)];
    }

    // Helper function to add personalized greeting to response content
    async function addPersonalizedGreeting(contentElementId, clientName, greetingType = 'response') {
        try {
            const greeting = await getPersonalizedGreeting(clientName, greetingType);
            const contentElement = document.getElementById(contentElementId);
            if (contentElement) {
                const greetingParagraph = document.createElement('p');
                greetingParagraph.innerHTML = `<strong>${greeting}</strong>`;
                contentElement.insertBefore(greetingParagraph, contentElement.firstChild);
            }
        } catch (error) {
            console.error('Error adding personalized greeting:', error);
            // Add fallback greeting
            const contentElement = document.getElementById(contentElementId);
            if (contentElement) {
                const greetingParagraph = document.createElement('p');
                greetingParagraph.innerHTML = `<strong>${getFallbackGreeting(clientName)}</strong>`;
                contentElement.insertBefore(greetingParagraph, contentElement.firstChild);
            }
        }
    }

    // Show client name modal
    function showClientNameModal() {
        document.getElementById('clientNameModal').classList.remove('d-none');
    }

    // Save client name
    function saveClientName() {
        const nameInput = document.getElementById('clientNameInput');
        const name = nameInput.value.trim();

        if (name) {
            clientName = name;
            localStorage.setItem('clientName', name);
            document.getElementById('clientNameModal').classList.add('d-none');

            // Update logout button text
            const logoutText = document.getElementById('logout-text');
            if (logoutText) {
                logoutText.textContent = `Logout (${name})`;
            }

            // Add welcome message
            addWelcomeMessage();
        } else {
            nameInput.classList.add('is-invalid');
            setTimeout(() => nameInput.classList.remove('is-invalid'), 3000);
        }
    }

    // Add welcome message
    async function addWelcomeMessage() {
        const chatBox = document.getElementById('chat-box');
        const welcomeMessage = document.createElement('div');
        welcomeMessage.className = 'chat-message bot-message';

        const contentId = `welcome-content-${Date.now()}`;
        welcomeMessage.innerHTML = `
            <div class="message-header">
                <i class="fas fa-robot message-avatar text-success"></i>
                <span class="message-sender">ERDB AI</span>
                <span class="message-time">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="message-content" id="${contentId}">
                <p>Welcome to the ERDB Knowledge Hub. To get started:</p>
                <ol>
                    <li>Select a category from the dropdown menu below</li>
                    <li>Type your question in the text box</li>
                    <li>Press Enter or click the send button to submit</li>
                </ol>
                <p>My answers will be based on the available ERDB Knowledge Products in the selected category. How can I help you today?</p>
            </div>
        `;
        chatBox.appendChild(welcomeMessage);

        // Add personalized greeting
        await addPersonalizedGreeting(contentId, clientName, 'welcome');

        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Handle key press in query input
    function handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendQuery();
        }
    }

    // Send query to server
    function sendQuery() {
        const query = document.getElementById('query').value.trim();
        const category = document.getElementById('category').value;

        if (!query) {
            DMSUtils.showToast('Please enter a question', 'error');
            return;
        }

        if (!category) {
            DMSUtils.showToast('Please select a category', 'error');
            return;
        }

        if (isProcessing) {
            DMSUtils.showToast('Please wait for the current request to complete', 'warning');
            return;
        }

        // Add user message to chat
        addUserMessage(query);

        // Clear input
        document.getElementById('query').value = '';

        // Add loading indicator
        addLoadingIndicator();

        // Simulate API call
        isProcessing = true;
        setTimeout(() => {
            // Remove loading indicator
            removeLoadingIndicator();

            // Add bot response
            addBotResponse(query, category);

            isProcessing = false;
        }, 2000);
    }

    // Add user message to chat
    function addUserMessage(query) {
        const chatBox = document.getElementById('chat-box');
        const userMessage = document.createElement('div');
        userMessage.className = 'chat-message user-message fade-in';

        const antiHallucinationMode = document.getElementById('anti-hallucination').value;
        const modeClass = antiHallucinationMode === 'strict' ? 'mode-strict' :
                         antiHallucinationMode === 'balanced' ? 'mode-balanced' : 'mode-creative';
        const modeText = antiHallucinationMode === 'strict' ? 'Strict' :
                        antiHallucinationMode === 'balanced' ? 'Balanced' : 'Creative';

        userMessage.innerHTML = `
            <div class="message-header">
                <i class="fas fa-user message-avatar text-primary"></i>
                <span class="message-sender">${clientName || 'You'}</span>
                <span class="message-time">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="message-content">
                <p>${query}</p>
                <div class="mode-badge ${modeClass}">
                    <i class="fas fa-shield-alt me-1"></i>Mode: ${modeText}
                </div>
            </div>
        `;

        chatBox.appendChild(userMessage);
        setTimeout(() => {
            userMessage.classList.remove('fade-in');
            chatBox.scrollTop = chatBox.scrollHeight;
        }, 50);
    }

    // Add loading indicator
    function addLoadingIndicator() {
        const chatBox = document.getElementById('chat-box');
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'loading-indicator';
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        `;
        chatBox.appendChild(loadingIndicator);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Remove loading indicator
    function removeLoadingIndicator() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    // Add bot response
    async function addBotResponse(query, category) {
        const chatBox = document.getElementById('chat-box');
        const botMessage = document.createElement('div');
        botMessage.className = 'chat-message bot-message fade-in';

        // Sample response
        const response = `This is a sample response to your question about ${category}. In a real implementation, this would be generated by the AI model based on the documents in the selected category.`;

        const contentId = `response-content-${Date.now()}`;
        botMessage.innerHTML = `
            <div class="message-header">
                <i class="fas fa-robot message-avatar text-success"></i>
                <span class="message-sender">ERDB AI</span>
                <span class="message-time">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="message-content" id="${contentId}">
                <p>${response}</p>

                <div class="follow-up-questions">
                    <p><strong>You might also want to ask:</strong></p>
                    <div>
                        <span class="follow-up-question" onclick="document.getElementById('query').value = this.textContent; document.getElementById('query').focus();">What are the key forestry policies?</span>
                        <span class="follow-up-question" onclick="document.getElementById('query').value = this.textContent; document.getElementById('query').focus();">How does climate change affect biodiversity?</span>
                        <span class="follow-up-question" onclick="document.getElementById('query').value = this.textContent; document.getElementById('query').focus();">What are sustainable forestry practices?</span>
                    </div>
                </div>

                <div class="sources-section">
                    <p><strong>Sources:</strong></p>
                    <div class="source-item">
                        <i class="fas fa-file-pdf source-icon"></i>
                        <span>Forestry Manual 2023 (Page 42)</span>
                    </div>
                    <div class="source-item">
                        <i class="fas fa-file-pdf source-icon"></i>
                        <span>Climate Change Report (Page 15)</span>
                    </div>
                    <div class="source-item">
                        <i class="fas fa-globe source-icon"></i>
                        <a href="#" target="_blank" rel="noopener noreferrer" class="text-primary">ERDB Official Website</a>
                    </div>
                </div>
            </div>
        `;

        chatBox.appendChild(botMessage);

        // Add personalized greeting
        await addPersonalizedGreeting(contentId, clientName, 'response');

        setTimeout(() => {
            botMessage.classList.remove('fade-in');
            chatBox.scrollTop = chatBox.scrollHeight;
        }, 50);
    }

    // Clear all session data
    function clearAllSessionData() {
        localStorage.removeItem('clientName');
        localStorage.removeItem('chatHistory');
        clientName = null;
        chatHistory = [];
    }

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        // Set up logout button
        const logoutButton = document.getElementById('logout-button');
        if (logoutButton) {
            logoutButton.addEventListener('click', function() {
                clearAllSessionData();
                window.location.reload();
            });

            // Update logout button text
            if (clientName) {
                document.getElementById('logout-text').textContent = `Logout (${clientName})`;
            }
        }

        // Show client name modal if no name is stored
        if (!clientName) {
            setTimeout(showClientNameModal, 500);
        } else {
            // Add welcome message
            addWelcomeMessage();
        }
    });
</script>
{% endblock %}
