document.addEventListener('DOMContentLoaded', function() {
    // Toggle vision model options based on checkbox state
    setupVisionModelToggle();

    // Handle "Show Dimensions" checkbox
    setupDimensionsCheck();

    // Form submission
    setupFormSubmission();
});

function setupVisionModelToggle() {
    const useVisionModelCheckbox = document.getElementById('use_vision_model');
    const visionModelOptions = document.getElementById('visionModelOptions');
    const filterSensitivity = document.getElementById('filter_sensitivity');
    const maxImages = document.getElementById('max_images');
    const showFilteredImages = document.querySelector('input[name="show_filtered_images"]');

    // Initial state
    updateVisionOptions();

    // Toggle on change
    useVisionModelCheckbox.addEventListener('change', updateVisionOptions);

    function updateVisionOptions() {
        if (useVisionModelCheckbox.checked) {
            visionModelOptions.classList.remove('opacity-50');
            filterSensitivity.disabled = false;
            maxImages.disabled = false;
            if (showFilteredImages) showFilteredImages.disabled = false;
        } else {
            visionModelOptions.classList.add('opacity-50');
            filterSensitivity.disabled = true;
            maxImages.disabled = true;
            if (showFilteredImages) showFilteredImages.disabled = true;
        }
    }
}

function setupDimensionsCheck() {
    const showDimensionsCheckbox = document.getElementById('show_dimensions');
    const dimensionsResult = document.getElementById('dimensionsResult');
    const embeddingModelSelect = document.getElementById('embedding_model');

    showDimensionsCheckbox.addEventListener('change', async function() {
        if (this.checked) {
            dimensionsResult.classList.remove('hidden');
            dimensionsResult.innerHTML = `
                <div class="p-3 bg-blue-50 text-blue-700 rounded-md flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Checking dimensions of ${embeddingModelSelect.value}...
                </div>
            `;

            try {
                const response = await fetch('/admin/check_embedding_dimensions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model_name: embeddingModelSelect.value
                    })
                });

                const result = await response.json();

                if (response.ok && result.dimensions) {
                    dimensionsResult.innerHTML = `
                        <div class="p-3 bg-green-50 text-green-700 rounded-md">
                            <p><strong>Model:</strong> ${embeddingModelSelect.value}</p>
                            <p><strong>Dimensions:</strong> ${result.dimensions}</p>
                        </div>
                    `;
                } else {
                    dimensionsResult.innerHTML = `
                        <div class="p-3 bg-red-50 text-red-700 rounded-md">
                            <p>Error checking dimensions: ${result.error || 'Unknown error'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                dimensionsResult.innerHTML = `
                    <div class="p-3 bg-red-50 text-red-700 rounded-md">
                        <p>Error: ${error.message || 'Failed to check dimensions'}</p>
                    </div>
                `;
            }
        } else {
            dimensionsResult.classList.add('hidden');
        }
    });

    // Reset dimensions display when model changes
    embeddingModelSelect.addEventListener('change', function() {
        if (showDimensionsCheckbox.checked) {
            showDimensionsCheckbox.checked = false;
            dimensionsResult.classList.add('hidden');
        }
    });
}

function setupFormSubmission() {
    document.getElementById('embeddingConfigForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        const statusMessage = document.getElementById('statusMessage');
        statusMessage.innerHTML = `
            <div class="p-4 bg-blue-100 text-blue-700 rounded-md flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving embedding configuration...
            </div>
        `;
        statusMessage.classList.remove('hidden');

        try {
            // Gather form data
            const formData = new FormData(this);

            // Prepare data object
            const data = {
                chunk_size: parseInt(formData.get('chunk_size')),
                chunk_overlap: parseInt(formData.get('chunk_overlap')),
                extract_tables: formData.get('extract_tables') === 'on',
                extract_images: formData.get('extract_images') === 'on',
                use_vision_model: formData.get('use_vision_during_embedding') === 'on',
                filter_sensitivity: formData.get('filter_sensitivity'),
                max_images: parseInt(formData.get('max_pdf_images')),
                show_filtered_images: formData.get('show_filtered_images') === 'on',
                embedding_model: formData.get('embedding_model'),
                batch_size: parseInt(formData.get('batch_size')),
                processing_threads: parseInt(formData.get('processing_threads'))
            };

            // Send data to server
            const response = await fetch('/admin/embedding_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            // Show success/error message
            if (response.ok) {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-green-100 text-green-700 rounded-md">
                        ${result.message || "Embedding configuration saved successfully"}
                    </div>
                `;

                Toastify({
                    text: result.message || "Embedding configuration saved successfully",
                    duration: 3000,
                    backgroundColor: "#00C851"
                }).showToast();
            } else {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-red-100 text-red-700 rounded-md">
                        Error: ${result.error || "Failed to save embedding configuration"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${result.error || "Failed to save embedding configuration"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        } catch (error) {
            statusMessage.innerHTML = `
                <div class="p-4 bg-red-100 text-red-700 rounded-md">
                    Error: ${error.message || "An unexpected error occurred"}
                </div>
            `;

            Toastify({
                text: `Error: ${error.message || "An unexpected error occurred"}`,
                duration: 3000,
                backgroundColor: "#ff4444"
            }).showToast();
        }
    });
}
