{% extends "admin_base.html" %}

{% block title %}Chat Sessions{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <style>
        /* Additional text contrast fixes specific to sessions page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Table specific fixes */
        .bg-white table .text-gray-800 { color: #1a202c !important; }
        .bg-white table .text-gray-600 { color: #4a5568 !important; }

        /* Badge colors */
        .bg-blue-100.text-blue-800 {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }

        .bg-green-100.text-green-800 {
            background-color: #d1fae5 !important;
            color: #065f46 !important;
        }

        /* Dark mode styles */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #374151 !important; }
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .bg-yellow-50 { background-color: #92400e !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }
        .dark .text-gray-900 { color: #f3f4f6 !important; }

        /* Table specific fixes in dark mode */
        .dark table .text-gray-800 { color: #f3f4f6 !important; }
        .dark table .text-gray-600 { color: #d1d5db !important; }
        .dark table .text-gray-500 { color: #9ca3af !important; }

        /* Badge colors in dark mode */
        .dark .bg-blue-100.text-blue-800 {
            background-color: #1e3a8a !important;
            color: #bfdbfe !important;
        }

        .dark .bg-green-100.text-green-800 {
            background-color: #065f46 !important;
            color: #a7f3d0 !important;
        }

        /* Status indicators in dark mode */
        .dark .bg-gray-100.text-gray-800 {
            background-color: #4b5563 !important;
            color: #f3f4f6 !important;
        }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }

        /* Table styles in dark mode */
        .dark .divide-gray-200 > * { border-color: #4b5563 !important; }
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }

        /* Link colors in dark mode */
        .dark .text-blue-600 { color: #3b82f6 !important; }
        .dark .text-blue-700 { color: #60a5fa !important; }
        .dark .text-red-600 { color: #ef4444 !important; }

        /* Timeline elements in dark mode */
        .dark .bg-blue-50.text-blue-700 {
            background-color: #1e3a8a !important;
            color: #60a5fa !important;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Chat Sessions</h1>
            <div class="flex space-x-4">
                <a href="{{ url_for('chat_history') }}" class="text-blue-600 hover:underline">View All Chat History</a>
            </div>
        </div>

            {% if sessions %}
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Device ID</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">User Name</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Category</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Started</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Ended</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Messages</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Status</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            {% for session in sessions %}
                                <tr class="hover:bg-gray-50">
                                    <td class="py-3 px-4 text-sm font-medium text-gray-900">
                                        <span class="px-2 py-1 bg-blue-50 text-blue-700 rounded-md">{{ (session.device_fingerprint[:8] + '...') if session.device_fingerprint else 'Unknown' }}</span>
                                    </td>
                                    <td class="py-3 px-4 text-sm font-medium text-gray-900">{{ session.client_name if session.client_name else 'Anonymous' }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-500">{{ session.category }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-500">{{ session.start_time }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-500">{{ session.end_time or 'Active' }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-500">{{ session.message_count }}</td>
                                    <td class="py-3 px-4 text-sm">
                                        {% if session.end_time %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                                        {% else %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                        {% endif %}
                                    </td>
                                    <td class="py-3 px-4 text-sm">
                                        <div class="flex space-x-2">
                                            <a href="{{ url_for('view_session', session_id=session.session_id) }}" class="text-blue-600 hover:underline">View</a>
                                            {% if not session.end_time %}
                                                <form action="{{ url_for('close_session', session_id=session.session_id) }}" method="post" class="inline">
                                                    <button type="submit" class="text-red-600 hover:underline">Close</button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                No chat sessions found. Start a conversation to create a session.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
{% endblock %}
