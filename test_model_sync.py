#!/usr/bin/env python3
"""
Test script to verify model synchronization between default_models.json, .env file, and environment variables.
"""

import os
import json
import sys

def test_model_synchronization():
    """Test that model configuration is properly synchronized"""
    
    print("=== Model Synchronization Test ===\n")
    
    # 1. Check default_models.json
    print("1. Checking default_models.json:")
    try:
        with open('default_models.json', 'r') as f:
            config = json.load(f)
        
        config_llm = config.get('llm_model', 'NOT_SET')
        config_embedding = config.get('embedding_model', 'NOT_SET')
        config_vision = config.get('vision_model', 'NOT_SET')
        
        print(f"   LLM Model: {config_llm}")
        print(f"   Embedding Model: {config_embedding}")
        print(f"   Vision Model: {config_vision}")
    except Exception as e:
        print(f"   ERROR: Could not read default_models.json: {e}")
        config_llm = config_embedding = config_vision = "ERROR"
    
    # 2. Check .env file
    print("\n2. Checking .env file:")
    try:
        env_llm = env_embedding = env_vision = "NOT_SET"
        
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('LLM_MODEL='):
                    env_llm = line.split('=', 1)[1]
                elif line.startswith('TEXT_EMBEDDING_MODEL='):
                    env_embedding = line.split('=', 1)[1]
                elif line.startswith('VISION_MODEL='):
                    env_vision = line.split('=', 1)[1]
        
        print(f"   LLM Model: {env_llm}")
        print(f"   Embedding Model: {env_embedding}")
        print(f"   Vision Model: {env_vision}")
    except Exception as e:
        print(f"   ERROR: Could not read .env file: {e}")
        env_llm = env_embedding = env_vision = "ERROR"
    
    # 3. Check environment variables
    print("\n3. Checking environment variables:")
    runtime_llm = os.getenv('LLM_MODEL', 'NOT_SET')
    runtime_embedding = os.getenv('TEXT_EMBEDDING_MODEL', 'NOT_SET')
    runtime_vision = os.getenv('VISION_MODEL', 'NOT_SET')
    
    print(f"   LLM Model: {runtime_llm}")
    print(f"   Embedding Model: {runtime_embedding}")
    print(f"   Vision Model: {runtime_vision}")
    
    # 4. Check for synchronization
    print("\n4. Synchronization Analysis:")
    
    # LLM Model
    llm_sync = config_llm == env_llm == runtime_llm
    print(f"   LLM Model synchronized: {'✓' if llm_sync else '✗'}")
    if not llm_sync:
        print(f"     Config: {config_llm}")
        print(f"     .env:   {env_llm}")
        print(f"     Runtime: {runtime_llm}")
    
    # Embedding Model
    embedding_sync = config_embedding == env_embedding == runtime_embedding
    print(f"   Embedding Model synchronized: {'✓' if embedding_sync else '✗'}")
    if not embedding_sync:
        print(f"     Config: {config_embedding}")
        print(f"     .env:   {env_embedding}")
        print(f"     Runtime: {runtime_embedding}")
    
    # Vision Model
    vision_sync = config_vision == env_vision == runtime_vision
    print(f"   Vision Model synchronized: {'✓' if vision_sync else '✗'}")
    if not vision_sync:
        print(f"     Config: {config_vision}")
        print(f"     .env:   {env_vision}")
        print(f"     Runtime: {runtime_vision}")
    
    # Overall result
    all_sync = llm_sync and embedding_sync and vision_sync
    print(f"\n5. Overall Result: {'✓ ALL SYNCHRONIZED' if all_sync else '✗ SYNCHRONIZATION ISSUES DETECTED'}")
    
    if not all_sync:
        print("\nRecommendations:")
        print("- Restart the application to reload configuration")
        print("- Check that the 'Save as Default' functionality is working correctly")
        print("- Verify that environment variables are not being overridden elsewhere")
    
    return all_sync

if __name__ == "__main__":
    success = test_model_synchronization()
    sys.exit(0 if success else 1)
