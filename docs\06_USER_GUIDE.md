# User Guide

## Overview

This comprehensive user guide provides step-by-step instructions for end users and administrators of the Document Management System. The guide covers all major features and workflows.

## Getting Started

### 1. Accessing the System

**For End Users:**
1. Open your web browser
2. Navigate to the system URL (e.g., `http://localhost:8080`)
3. You'll see the main chat interface with available document categories

**For Administrators:**
1. Navigate to the admin dashboard (e.g., `http://localhost:8080/admin`)
2. Log in with your administrator credentials
3. Access the full administrative interface

### 2. First-Time Setup

**Setting Your Name:**
1. When you first visit the chat interface, you'll be prompted to enter your name
2. Enter your preferred name for personalized interactions
3. Your name will be remembered for future sessions

**Theme Selection:**
- Click the theme toggle button (🌙/☀️) in the top-right corner
- Choose between dark and light modes
- Your preference will be saved automatically

## End User Features

### 1. Document Search and Chat

#### Basic Chat Interface

**Starting a Conversation:**
1. Select a document category from the dropdown menu
2. Type your question in the text input field
3. Click "Send" or press Enter to submit your query
4. Wait for the AI to process and respond

**Example Queries:**
- "What are the key findings about forest conservation?"
- "Show me information about biodiversity in marine ecosystems"
- "What research methods were used in the climate studies?"

#### Understanding Responses

**Response Components:**
- **Main Answer**: AI-generated response based on document content
- **Source Citations**: References to specific documents and page numbers
- **Related Images**: Relevant images from the documents
- **Cover Images**: Representative images displayed prominently
- **Follow-up Questions**: AI-suggested related questions

**Source Information Format:**
```
Source: Document_Name.pdf (Page 5)
- Brief excerpt from the relevant section
```

#### Advanced Chat Features

**Anti-Hallucination Modes:**
- **Strict Mode**: Highest accuracy, conservative responses
- **Balanced Mode**: Balance between accuracy and completeness
- **Permissive Mode**: More comprehensive responses

**Session Management:**
- Your chat history is automatically saved
- Sessions persist across browser refreshes
- Use "Logout" to clear your session data

### 2. Working with Images

**Image Types:**
- **Cover Images**: Displayed at the top of responses as clickable thumbnails
- **Related Images**: Shown in the "Related Images" section
- **Document Thumbnails**: Representative images for each document

**Image Interactions:**
- Click on any image to view it in full size
- Images open in a new tab for detailed viewing
- AI-generated captions provide context for each image

### 3. Follow-up Questions

**Using Suggested Questions:**
- Click on any suggested follow-up question to ask it automatically
- Questions are generated based on available document content
- Each question includes a brief explanation of what you'll learn

**Custom Follow-up:**
- Ask your own follow-up questions based on the response
- Reference specific parts of the answer for clarification
- Build on previous responses for deeper understanding

## Administrator Features

### 1. Admin Dashboard Access

**Logging In:**
1. Navigate to `/admin` or click "Admin Dashboard" from the main interface
2. Enter your administrator credentials
3. Access the comprehensive admin dashboard

**Dashboard Overview:**
- **Upload Content**: Add new PDFs or URLs
- **Manage Files**: View and organize existing content
- **User Management**: Control user accounts and permissions
- **Analytics**: View system usage and performance metrics
- **Configuration**: Adjust system settings and AI models

### 2. Content Management

#### Uploading PDF Documents

**Single PDF Upload:**
1. Click "Upload Content" from the dashboard
2. Select "Upload PDF" tab
3. Choose your PDF file (max 25MB)
4. Select the appropriate category
5. Optionally add a source URL
6. Click "Upload and Process"

**Processing Options:**
- **Use Vision Model**: Enable AI analysis of images in the PDF
- **Filter Images**: Remove irrelevant images automatically
- **Extract Tables**: Detect and extract table data
- **Sensitivity Level**: Adjust image filtering sensitivity

#### Adding Web Content

**URL Scraping:**
1. Select "Add URL" tab in the upload interface
2. Enter the target URL
3. Choose the document category
4. Set crawling depth (0 for single page, 1+ for multi-page)
5. Click "Scrape and Embed"

**Scraping Options:**
- **Depth 0**: Single page only
- **Depth 1**: Include linked pages (one level)
- **Depth 2+**: Deep crawling (use carefully)

#### Managing Existing Content

**File Management:**
1. Navigate to "Manage Files" from the dashboard
2. Browse files by category
3. View file details, processing status, and metadata
4. Delete files if necessary (removes all associated data)

**Category Management:**
- Categories are created automatically when content is uploaded
- Organize content logically by topic or document type
- Each category maintains its own vector database

### 3. User Management

#### Creating User Accounts

**Adding New Users:**
1. Go to "User Management" → "Users"
2. Click "Add New User"
3. Fill in user details:
   - Username (unique)
   - Email address
   - Full name
   - Role (Admin/Editor/Viewer)
   - Initial password
4. Click "Create User"

**User Roles:**
- **Admin**: Full system access and configuration
- **Editor**: Content management and user interactions
- **Viewer**: Read-only access to content

#### Managing Permissions

**Permission Groups:**
1. Navigate to "Permission Groups"
2. View default groups (Admin, Editor, Viewer)
3. Modify group permissions as needed
4. Users inherit permissions from their assigned group

**Individual Permissions:**
1. Edit a specific user
2. Override group permissions for individual functions
3. Grant or revoke specific capabilities
4. Monitor permission changes in audit logs

#### User Account Management

**Account Status:**
- **Active**: Normal account access
- **Pending**: Awaiting activation
- **Locked**: Temporarily disabled
- **Disabled**: Permanently disabled

**Password Management:**
- Force password changes for security
- Set password expiration policies
- Monitor failed login attempts
- Reset passwords when needed

### 4. System Analytics

#### Usage Analytics

**Dashboard Metrics:**
- Total queries processed
- Active users and sessions
- Popular document categories
- Response times and performance

**Detailed Analytics:**
1. Navigate to "Analytics" from the dashboard
2. View comprehensive usage statistics
3. Filter by date range for specific periods
4. Export data for external analysis

#### Client Analytics

**Individual User Analysis:**
- Click on any client name in analytics
- View detailed usage patterns
- Monitor engagement and activity
- Track question types and preferences

#### Geographic Analytics

**Location Tracking:**
- View user locations on interactive maps
- Analyze usage by geographic region
- Monitor global system adoption
- Respect privacy settings and regulations

### 5. System Configuration

#### AI Model Settings

**Model Configuration:**
1. Navigate to "Model Settings"
2. Configure three main areas:
   - **AI Models**: Select language and vision models
   - **Query Config**: Adjust search and response parameters
   - **Embedding Config**: Configure document processing

**Model Selection:**
- Choose from available Llama and Gemma models
- Test different models for optimal performance
- Save configurations as system defaults

#### Query Configuration

**Search Parameters:**
- **Retrieval K**: Number of documents to search (default: 12)
- **Relevance Threshold**: Minimum relevance score (default: 0.15)
- **Max Documents**: Maximum documents in responses (default: 8)

**Response Limits:**
- **Max Images**: Limit images shown in responses
- **Max Tables**: Control table display count
- **Max Links**: Limit PDF links shown

**Hallucination Detection:**
- **Strict Mode**: High confidence threshold (0.6)
- **Balanced Mode**: Medium threshold (0.4)
- **Enable/Disable**: Toggle detection system

#### Embedding Configuration

**Document Processing:**
- **Chunk Size**: Text segment size for processing (default: 1000)
- **Chunk Overlap**: Overlap between segments (default: 200)
- **Extract Tables**: Enable table detection
- **Extract Images**: Enable image extraction

**Vision Processing:**
- **Use Vision During Embedding**: Analyze images during upload
- **Filter Sensitivity**: Image relevance filtering level
- **Max Images**: Limit images processed per document

## Troubleshooting

### Common Issues

**Upload Problems:**
- **File too large**: Reduce file size or contact administrator
- **Unsupported format**: Ensure PDF format and valid file
- **Processing timeout**: Try smaller files or contact support

**Search Issues:**
- **No results**: Try different keywords or broader terms
- **Irrelevant results**: Use more specific questions
- **Slow responses**: Check system load or contact administrator

**Login Problems:**
- **Forgotten password**: Use password reset feature
- **Account locked**: Contact administrator for unlock
- **Permission denied**: Verify role and permissions with admin

### Getting Help

**For End Users:**
- Contact your system administrator
- Check with your organization's IT support
- Review this user guide for common solutions

**For Administrators:**
- Check system logs for error details
- Review configuration settings
- Consult technical documentation
- Contact system developers for advanced issues

## Best Practices

### For End Users

**Effective Querying:**
- Use specific, clear questions
- Include relevant context in your queries
- Try different phrasings if results aren't helpful
- Use follow-up questions to dive deeper

**Information Verification:**
- Always check source citations
- Cross-reference important information
- Be aware of AI limitations and potential errors
- Use multiple sources for critical decisions

### For Administrators

**Content Management:**
- Organize content into logical categories
- Regularly review and update document collections
- Monitor storage usage and performance
- Maintain backup procedures

**User Management:**
- Regularly review user accounts and permissions
- Monitor system usage and user activity
- Implement appropriate security policies
- Provide user training and support

**System Maintenance:**
- Monitor system performance and analytics
- Keep AI models updated
- Regular database maintenance
- Security updates and patches

This user guide provides comprehensive instructions for effectively using the Document Management System, ensuring both end users and administrators can maximize the system's capabilities.
