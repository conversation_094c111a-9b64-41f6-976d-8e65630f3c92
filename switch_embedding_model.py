import os
import logging
import argparse
import chromadb
from langchain_ollama.embeddings import OllamaEmbeddings
import requests

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

def get_available_models():
    """Get available embedding models from Ollama"""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            logger.warning(f"Ollama service returned status code: {response.status_code}")
            return []
            
        # Define known embedding model prefixes/keywords
        known_embedding_prefixes = ["nomic-embed", "all-minilm", "bge-m3", "mxbai-embed", "embed", "text-embedding"]
        
        # Extract embedding models
        models = response.json().get("models", [])
        embedding_models = []
        
        for model in models:
            model_name = model["name"].lower()
            if any(prefix in model_name for prefix in known_embedding_prefixes):
                embedding_models.append(model["name"])
        
        return embedding_models
    except Exception as e:
        logger.error(f"Failed to get available models: {str(e)}")
        return []

def check_model_dimensions(model_name):
    """Check the dimensions of a specific embedding model"""
    try:
        # Initialize the embedding model
        embed_fn = OllamaEmbeddings(model=model_name)
        
        # Generate an embedding for a test string
        test_embedding = embed_fn.embed_query("This is a test")
        
        logger.info(f"Model: {model_name}")
        logger.info(f"Embedding dimensions: {len(test_embedding)}")
        
        return len(test_embedding)
    except Exception as e:
        logger.error(f"Error checking model dimensions for {model_name}: {str(e)}")
        return None

def check_collection_dimensions():
    """Check the dimensions of all collections in the Chroma database"""
    try:
        CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")
        results = {}
        
        # Get all categories
        if not os.path.exists(CHROMA_PATH):
            logger.error(f"Chroma DB path {CHROMA_PATH} does not exist")
            return results
        
        categories = [d for d in os.listdir(CHROMA_PATH) 
                     if os.path.isdir(os.path.join(CHROMA_PATH, d))]
        
        for category in categories:
            try:
                # Access the Chroma collection directly using chromadb
                persist_dir = os.path.join(CHROMA_PATH, category)
                client = chromadb.PersistentClient(path=persist_dir)
                
                # Get the collection
                collection = client.get_collection(name=category)
                
                # Get collection info
                collection_info = collection._collection.metadata
                
                # Extract dimension information
                if collection_info and "dimension" in collection_info:
                    dimension = collection_info["dimension"]
                    results[category] = dimension
                else:
                    # Try to infer from the embeddings
                    try:
                        peek = collection.peek(limit=1)
                        if peek and "embeddings" in peek and peek["embeddings"]:
                            dimension = len(peek["embeddings"][0])
                            results[category] = dimension
                        else:
                            results[category] = "unknown"
                    except Exception:
                        results[category] = "unknown"
            except Exception as e:
                logger.error(f"Error checking dimensions for category {category}: {str(e)}")
                results[category] = "error"
        
        return results
    except Exception as e:
        logger.error(f"Error checking collection dimensions: {str(e)}")
        return {}

def update_environment_variable(model_name):
    """Update the environment variable for the embedding model"""
    try:
        # Set the environment variable
        os.environ["TEXT_EMBEDDING_MODEL"] = model_name
        
        # Also update the .env file if it exists
        env_file = ".env"
        if os.path.exists(env_file):
            with open(env_file, "r") as f:
                lines = f.readlines()
            
            # Update or add the TEXT_EMBEDDING_MODEL line
            found = False
            for i, line in enumerate(lines):
                if line.startswith("TEXT_EMBEDDING_MODEL="):
                    lines[i] = f"TEXT_EMBEDDING_MODEL={model_name}\n"
                    found = True
                    break
            
            if not found:
                lines.append(f"TEXT_EMBEDDING_MODEL={model_name}\n")
            
            with open(env_file, "w") as f:
                f.writelines(lines)
            
            logger.info(f"Updated .env file with TEXT_EMBEDDING_MODEL={model_name}")
        
        logger.info(f"Set environment variable TEXT_EMBEDDING_MODEL to {model_name}")
        return True
    except Exception as e:
        logger.error(f"Error updating environment variable: {str(e)}")
        return False

def main():
    """Main function to switch embedding models"""
    parser = argparse.ArgumentParser(description="Switch embedding models")
    parser.add_argument("--list", action="store_true", help="List available embedding models")
    parser.add_argument("--check", action="store_true", help="Check collection dimensions")
    parser.add_argument("--model", type=str, help="Switch to the specified embedding model")
    parser.add_argument("--auto", action="store_true", help="Automatically select a compatible model")
    
    args = parser.parse_args()
    
    if args.list:
        # List available models
        models = get_available_models()
        if models:
            print("\nAvailable embedding models:")
            for model in models:
                dim = check_model_dimensions(model)
                print(f"  - {model} ({dim} dimensions)")
        else:
            print("No embedding models found. Make sure Ollama is running.")
    
    elif args.check:
        # Check collection dimensions
        collection_dims = check_collection_dimensions()
        if collection_dims:
            print("\nCollection dimensions:")
            for category, dimension in collection_dims.items():
                print(f"  - {category}: {dimension}")
        else:
            print("No collections found or error checking dimensions.")
    
    elif args.model:
        # Switch to the specified model
        dim = check_model_dimensions(args.model)
        if dim is not None:
            print(f"\nSwitching to embedding model: {args.model} ({dim} dimensions)")
            if update_environment_variable(args.model):
                print("Successfully updated embedding model.")
            else:
                print("Failed to update embedding model.")
        else:
            print(f"Failed to check dimensions for model {args.model}. Make sure it exists and Ollama is running.")
    
    elif args.auto:
        # Automatically select a compatible model
        collection_dims = check_collection_dimensions()
        if not collection_dims:
            print("No collections found or error checking dimensions.")
            return
        
        # Find the most common dimension
        dim_counts = {}
        for dimension in collection_dims.values():
            if dimension not in ["unknown", "error"]:
                dim_counts[int(dimension)] = dim_counts.get(int(dimension), 0) + 1
        
        if not dim_counts:
            print("Could not determine collection dimensions.")
            return
        
        target_dim = max(dim_counts.items(), key=lambda x: x[1])[0]
        print(f"\nMost common collection dimension: {target_dim}")
        
        # Find a compatible model
        models = get_available_models()
        compatible_models = []
        
        for model in models:
            dim = check_model_dimensions(model)
            if dim == target_dim:
                compatible_models.append(model)
        
        if compatible_models:
            # Prefer certain models in this order
            preferred_order = ["mxbai-embed-large", "bge-m3", "nomic-embed-text"]
            selected_model = None
            
            for preferred in preferred_order:
                for model in compatible_models:
                    if preferred in model:
                        selected_model = model
                        break
                if selected_model:
                    break
            
            if not selected_model:
                selected_model = compatible_models[0]
            
            print(f"Selected compatible model: {selected_model} ({target_dim} dimensions)")
            if update_environment_variable(selected_model):
                print("Successfully updated embedding model.")
            else:
                print("Failed to update embedding model.")
        else:
            print(f"No compatible models found with {target_dim} dimensions.")
    
    else:
        # Show current model
        current_model = os.getenv("TEXT_EMBEDDING_MODEL", "Not set")
        dim = check_model_dimensions(current_model)
        print(f"\nCurrent embedding model: {current_model}")
        if dim is not None:
            print(f"Dimensions: {dim}")
        
        print("\nUse --list to see available models")
        print("Use --check to check collection dimensions")
        print("Use --model MODEL to switch to a specific model")
        print("Use --auto to automatically select a compatible model")

if __name__ == "__main__":
    main()
