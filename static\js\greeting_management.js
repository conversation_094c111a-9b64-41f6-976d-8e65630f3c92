// Greeting Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize greeting management functionality
    initGreetingManagement();
});

function initGreetingManagement() {
    // Add event listeners
    setupEventListeners();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupEventListeners() {
    // Add greeting form submission
    const addForm = document.getElementById('addGreetingForm');
    if (addForm) {
        addForm.addEventListener('submit', handleAddGreeting);
    }
    
    // Edit greeting form submission
    const editForm = document.getElementById('editGreetingForm');
    if (editForm) {
        editForm.addEventListener('submit', handleEditGreeting);
    }
    
    // Edit buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-greeting-btn')) {
            handleEditButtonClick(e.target.closest('.edit-greeting-btn'));
        }
        
        if (e.target.closest('.delete-greeting-btn')) {
            handleDeleteButtonClick(e.target.closest('.delete-greeting-btn'));
        }
        
        if (e.target.closest('.test-greeting-btn')) {
            handleTestButtonClick(e.target.closest('.test-greeting-btn'));
        }
    });
}

async function handleAddGreeting(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        template_type: formData.get('template_type'),
        greeting_text: formData.get('greeting_text'),
        weight: parseInt(formData.get('weight')),
        is_active: formData.get('is_active') === 'on'
    };
    
    try {
        const response = await fetch('/api/greeting_templates', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('Greeting template added successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addGreetingModal')).hide();
            e.target.reset();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error adding greeting:', error);
        showToast('Error adding greeting template', 'error');
    }
}

async function handleEditGreeting(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const templateId = formData.get('template_id');
    const data = {
        template_type: formData.get('template_type'),
        greeting_text: formData.get('greeting_text'),
        weight: parseInt(formData.get('weight')),
        is_active: formData.get('is_active') === 'on'
    };
    
    try {
        const response = await fetch(`/api/greeting_templates/${templateId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('Greeting template updated successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editGreetingModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error updating greeting:', error);
        showToast('Error updating greeting template', 'error');
    }
}

function handleEditButtonClick(button) {
    const templateId = button.dataset.templateId;
    const templateType = button.dataset.templateType;
    const greetingText = button.dataset.greetingText;
    const weight = button.dataset.weight;
    const isActive = button.dataset.isActive === 'True';
    
    // Populate edit form
    document.getElementById('editTemplateId').value = templateId;
    document.getElementById('editTemplateType').value = templateType;
    document.getElementById('editGreetingText').value = greetingText;
    document.getElementById('editWeight').value = weight;
    document.getElementById('editIsActive').checked = isActive;
    
    // Show edit modal
    const editModal = new bootstrap.Modal(document.getElementById('editGreetingModal'));
    editModal.show();
}

async function handleDeleteButtonClick(button) {
    const templateId = button.dataset.templateId;
    const greetingText = button.dataset.greetingText;
    
    if (!confirm(`Are you sure you want to delete this greeting template?\n\n"${greetingText}"`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/greeting_templates/${templateId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('Greeting template deleted successfully!', 'success');
            // Remove the card from the DOM
            const card = button.closest('.greeting-template-card');
            card.style.transition = 'opacity 0.3s';
            card.style.opacity = '0';
            setTimeout(() => {
                card.remove();
                updateTabCounts();
            }, 300);
        } else {
            showToast('Error: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Error deleting greeting:', error);
        showToast('Error deleting greeting template', 'error');
    }
}

function handleTestButtonClick(button) {
    const greetingText = button.dataset.greetingText;
    const card = button.closest('.greeting-template-card');
    const previewDiv = card.querySelector('.greeting-preview');
    const previewText = card.querySelector('.preview-text');
    
    // Generate preview with sample name
    const sampleName = 'John Doe';
    const previewGreeting = greetingText.replace('{name}', sampleName);
    
    previewText.innerHTML = `<strong>"${previewGreeting}"</strong>`;
    
    // Toggle preview visibility
    if (previewDiv.style.display === 'none') {
        previewDiv.style.display = 'block';
        button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        button.title = 'Hide preview';
    } else {
        previewDiv.style.display = 'none';
        button.innerHTML = '<i class="fas fa-play"></i>';
        button.title = 'Test greeting';
    }
}

function updateTabCounts() {
    // Update tab counts after deletion
    const tabs = ['welcome', 'response', 'return-user', 'time-based'];
    
    tabs.forEach(tabType => {
        const container = document.querySelector(`[data-type="${tabType.replace('-', '_')}"]`);
        const count = container ? container.querySelectorAll('.greeting-template-card').length : 0;
        const tab = document.getElementById(`${tabType}-tab`);
        if (tab) {
            const tabText = tab.innerHTML;
            const newTabText = tabText.replace(/\(\d+\)/, `(${count})`);
            tab.innerHTML = newTabText;
        }
    });
}

function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}

function showToast(message, type = 'info') {
    // Create toast element
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
    
    const toastHTML = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${bgClass} text-white border-0">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                <strong class="me-auto">Greeting Management</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}
