{% extends "admin_base.html" %}

{% block title %}Manage Files{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <style>
        /* Additional text contrast fixes specific to files page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Table specific fixes */
        .bg-white table .text-gray-800 { color: #1a202c !important; }
        .bg-white table .text-gray-600 { color: #4a5568 !important; }

        /* Link colors */
        .bg-white .text-blue-600 { color: #2563eb !important; }
        .bg-white .text-red-600 { color: #dc2626 !important; }

        /* Dark mode styles */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #374151 !important; }
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .bg-yellow-50 { background-color: #92400e !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }

        /* Table specific fixes in dark mode */
        .dark table .text-gray-800 { color: #f3f4f6 !important; }
        .dark table .text-gray-600 { color: #d1d5db !important; }

        /* Link colors in dark mode */
        .dark .text-blue-600 { color: #3b82f6 !important; }
        .dark .text-red-600 { color: #ef4444 !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }

        /* File preview elements and thumbnails in dark mode */
        .dark .file-preview {
            background-color: #374151 !important;
            border-color: #4b5563 !important;
        }

        .dark .file-thumbnail {
            border-color: #4b5563 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
        }

        /* Table styles in dark mode */
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .divide-gray-200 > * { border-color: #4b5563 !important; }
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Manage Files</h1>

            <!-- List Files by Category -->
            {% if files_data %}
                <div class="space-y-6">
                    {% for category, files in files_data.items() %}
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h2 class="text-xl font-semibold text-gray-700 mb-3">{{ category }}</h2>
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th class="py-3 px-4 text-left text-gray-600 font-medium">File/URL Name</th>
                                            <th class="py-3 px-4 text-left text-gray-600 font-medium">Type</th>
                                            <th class="py-3 px-4 text-left text-gray-600 font-medium">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        {% for file in files %}
                                            <tr class="hover:bg-gray-50">
                                                <td class="py-3 px-4 text-gray-800">
                                                    {% if file.type == 'url' %}
                                                        <a href="{{ file.original_filename }}" target="_blank" class="text-blue-600 hover:underline truncate block max-w-xs" title="{{ file.original_filename }}">
                                                            {{ file.original_filename }}
                                                        </a>
                                                        {% if file.scrape_depth is defined and file.scrape_depth > 0 %}
                                                            <span class="text-xs text-gray-600 mt-1 block">
                                                                Depth: {{ file.scrape_depth }}
                                                                {% if file.pages_scraped is defined %}
                                                                    ({{ file.pages_scraped }} pages)
                                                                {% endif %}
                                                            </span>
                                                        {% endif %}

                                                        {% if file.database_retrieval is defined and file.database_retrieval %}
                                                            <span class="text-xs text-green-600 mt-1 block">
                                                                <svg class="inline-block h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                </svg>
                                                                Using database content
                                                                {% if file.url_last_scraped is defined %}
                                                                    (Last scraped: {{ file.url_last_scraped.split('T')[0] }})
                                                                {% endif %}
                                                            </span>
                                                        {% endif %}

                                                        {% if file.source_url_id is defined %}
                                                            <span class="text-xs text-gray-500 mt-1 block">
                                                                Database ID: {{ file.source_url_id }}
                                                            </span>
                                                        {% endif %}
                                                    {% elif file.original_url %}
                                                        <div>
                                                            <span title="{{ file.original_filename }}">{{ file.original_filename }}</span>
                                                            <div class="mt-1">
                                                                <a href="{{ file.original_url }}" target="_blank" class="text-xs text-blue-600 hover:underline truncate block max-w-xs" title="{{ file.original_url }}">
                                                                    Source: {{ file.original_url }}
                                                                </a>
                                                            </div>

                                                            {% if file.database_retrieval is defined and file.database_retrieval %}
                                                                <span class="text-xs text-green-600 mt-1 block">
                                                                    <svg class="inline-block h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                    </svg>
                                                                    Using database content
                                                                </span>
                                                            {% endif %}

                                                            {% if file.source_url_id is defined or file.pdf_document_id is defined or file.cover_image_id is defined %}
                                                                <div class="text-xs text-gray-500 mt-1">
                                                                    {% if file.source_url_id is defined %}
                                                                        <span class="block">URL ID: {{ file.source_url_id }}</span>
                                                                    {% endif %}
                                                                    {% if file.pdf_document_id is defined %}
                                                                        <span class="block">PDF ID: {{ file.pdf_document_id }}</span>
                                                                    {% endif %}
                                                                    {% if file.cover_image_id is defined %}
                                                                        <span class="block">Cover Image ID: {{ file.cover_image_id }}</span>
                                                                    {% endif %}
                                                                </div>
                                                            {% endif %}
                                                        </div>
                                                    {% else %}
                                                        <span title="{{ file.original_filename }}">{{ file.original_filename }}</span>
                                                    {% endif %}
                                                </td>
                                                <td class="py-3 px-4">
                                                    <span class="px-2 py-1 text-xs font-semibold rounded-full {% if file.type == 'pdf' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                                        {{ file.type | upper }}
                                                    </span>

                                                    {% if file.database_retrieval is defined and file.database_retrieval %}
                                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 ml-1">
                                                            DB
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td class="py-3 px-4">
                                                    <div class="flex space-x-2">
                                                        <a href="{{ url_for('view_vector_data', category=category, filename=file.source) }}"
                                                           class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                                            View Vector Data
                                                        </a>
                                                        <form method="POST" action="{{ url_for('delete_file_route', category=category, filename=file.source) }}"
                                                              onsubmit="return confirm('Are you sure you want to delete this {{ file.type }}? This will also delete all associated vector data.');">
                                                            <button type="submit" class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                                                Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                No files or URLs found. <a href="{{ url_for('upload_file') }}" class="font-medium underline text-yellow-700 hover:text-yellow-600">Upload some content</a> to get started.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
{% endblock %}