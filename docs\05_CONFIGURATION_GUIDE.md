# Configuration Guide

## Overview

This guide provides comprehensive instructions for configuring the Document Management System, including environment setup, AI model configuration, and system optimization.

## Environment Configuration

### 1. Environment Variables

Create a `.env` file in the project root directory:

```bash
# Database Configuration
DB_PATH=./database.db
USER_DB_PATH=./user_management.db
SCRAPED_DB_PATH=./scraped_pages.db
CHROMA_PATH=./chroma
TEMP_FOLDER=./_temp

# Flask Configuration
FLASK_SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=true

# AI Model Configuration
LLM_MODEL=llama3.1:8b-instruct-q4_K_M
TEXT_EMBEDDING_MODEL=mxbai-embed-large:latest
VISION_MODEL=llama3.2-vision:11b-instruct-q4_K_M

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_API_HOST=http://localhost:11434

# Feature Toggles
USE_VISION_MODEL=true
USE_VISION_MODEL_DURING_EMBEDDING=true
VISION_CACHE_ENABLED=true
FILTER_PDF_IMAGES=true
SHOW_FILTERED_IMAGES=false

# Processing Configuration
PDF_IMAGE_FILTER_SENSITIVITY=medium
MAX_PDF_IMAGES_TO_ANALYZE=30
ANTI_HALLUCINATION_MODE=strict

# Security Configuration
PASSWORD_EXPIRY_DAYS=90
MAX_LOGIN_ATTEMPTS=5
SESSION_TIMEOUT_MINUTES=120

# CSRF Protection Configuration
WTF_CSRF_ENABLED=true
WTF_CSRF_TIME_LIMIT=7200
WTF_CSRF_SSL_STRICT=false
WTF_CSRF_CHECK_DEFAULT=true

# External Services
GHOSTSCRIPT_PATH=/path/to/ghostscript
TESSERACT_CMD=/path/to/tesseract

# Geolocation Configuration
GEOIP_DATABASE_PATH=./GeoLite2-City.mmdb
ENABLE_GEOLOCATION=true
```

### 2. Production Environment Variables

For production deployment, use more secure configurations:

```bash
# Production Security
FLASK_ENV=production
FLASK_DEBUG=false
FLASK_SECRET_KEY=generate-strong-random-key

# Database Configuration (Production)
DB_PATH=/var/lib/dms/database.db
USER_DB_PATH=/var/lib/dms/user_management.db
CHROMA_PATH=/var/lib/dms/chroma

# Performance Optimization
OLLAMA_NUM_PARALLEL=4
OLLAMA_MAX_LOADED_MODELS=3
VISION_CACHE_SIZE=1000

# Security Hardening
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=60
ENABLE_CSRF_PROTECTION=true
```

## AI Model Configuration

### 1. Default Model Settings (`default_models.json`)

```json
{
  "llm_model": "llama3.1:8b-instruct-q4_K_M",
  "embedding_model": "mxbai-embed-large:latest",
  "vision_model": "llama3.2-vision:11b-instruct-q4_K_M",
  "use_vision_model": true,
  "filter_pdf_images": true,
  "filter_sensitivity": "medium",
  "max_pdf_images": 30,
  "show_filtered_images": false,
  "use_vision_model_during_embedding": true,
  "model_parameters": {
    "temperature": 0.7,
    "num_ctx": 4096,
    "num_predict": 256,
    "top_p": 0.9,
    "top_k": 40,
    "repeat_penalty": 1.1,
    "system_prompt": "You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context."
  },
  "embedding_config": {
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "extract_tables": true,
    "extract_images": true,
    "batch_size": 10,
    "processing_threads": 4
  }
}
```

### 2. Model Parameter Explanations

**Language Model Parameters:**
- **temperature** (0.0-2.0): Controls response creativity (0.7 recommended)
- **num_ctx** (1024-8192): Context window size (4096 recommended)
- **num_predict** (1-2048): Maximum response tokens (256 recommended)
- **top_p** (0.1-1.0): Nucleus sampling parameter (0.9 recommended)
- **top_k** (1-100): Top-k sampling parameter (40 recommended)
- **repeat_penalty** (1.0-1.5): Repetition penalty (1.1 recommended)

**Vision Model Parameters:**
- **filter_sensitivity**: Image filtering level (low/medium/high)
- **max_pdf_images**: Maximum images to process per document
- **use_vision_model_during_embedding**: Enable vision analysis during upload

**Embedding Parameters:**
- **chunk_size**: Text chunk size for embedding (1000 recommended)
- **chunk_overlap**: Overlap between chunks (200 recommended)
- **batch_size**: Embedding batch size (10 recommended)

### 3. Supported AI Models

**Language Models:**
```bash
# Llama Models
llama3.1:8b-instruct-q4_K_M    # Recommended default
llama3.1:70b-instruct-q4_K_M   # High performance
llama3.1:405b-instruct-q4_K_M  # Maximum capability

# Gemma Models
gemma3:1b                       # Lightweight option
gemma3:4b-it-q4_K_M            # Balanced performance
gemma3:12b-it-q4_K_M           # High capability
```

**Vision Models:**
```bash
# Llama Vision
llama3.2-vision:11b-instruct-q4_K_M  # Recommended
llama3.2-vision:90b-instruct-q4_K_M  # High performance

# Gemma Vision
gemma3:4b-it-q4_K_M                  # Lightweight vision
gemma3:12b-it-q4_K_M                 # Advanced vision
```

**Embedding Models:**
```bash
# Recommended Models
mxbai-embed-large:latest        # Default recommendation
nomic-embed-text:latest         # Alternative option
all-minilm:latest              # Lightweight option
```

## Query Configuration

### 1. Query Processing Settings (`config/query_config.py`)

```python
# Document Retrieval Settings
RETRIEVAL_K = 12                    # Documents to retrieve
RELEVANCE_THRESHOLD = 0.15          # Minimum relevance score
MIN_DOCUMENTS = 3                   # Minimum documents in response
MAX_DOCUMENTS = 8                   # Maximum documents in response

# Response Display Limits
MAX_PDF_IMAGES_DISPLAY = 5          # PDF images shown
MAX_URL_IMAGES_DISPLAY = 5          # URL images shown
MAX_TABLES_DISPLAY = 3              # Tables displayed
MAX_PDF_LINKS_DISPLAY = 10          # PDF links shown

# Context Processing
MAX_VISION_CONTEXT_LENGTH = 2000    # Vision model context limit
CONTEXT_TRUNCATION_STRATEGY = "end" # How to truncate context

# Hallucination Detection
HALLUCINATION_THRESHOLD_STRICT = 0.6    # Strict mode threshold
HALLUCINATION_THRESHOLD_BALANCED = 0.4  # Balanced mode threshold
HALLUCINATION_THRESHOLD_DEFAULT = 0.5   # Default threshold
MIN_STATEMENT_LENGTH = 20               # Minimum statement length
ENABLE_HALLUCINATION_DETECTION = True   # Enable detection

# Performance Settings
MAX_PROCESSING_TIME = 120           # Maximum processing time (seconds)
ENABLE_RESPONSE_CACHING = False     # Enable response caching
CACHE_TTL_MINUTES = 60             # Cache time-to-live
```

### 2. Runtime Configuration Updates

**Via Admin Interface:**
1. Navigate to Admin Dashboard → Model Settings
2. Select the "Query Config" tab
3. Adjust parameters using interactive sliders
4. Click "Save Query Configuration"

**Via API:**
```python
# Update query configuration
POST /api/query_config
{
    "retrieval_k": 15,
    "relevance_threshold": 0.2,
    "max_documents": 10,
    "hallucination_threshold_strict": 0.7
}
```

## Database Configuration

### 1. SQLite Configuration

**Database Paths:**
```python
# Development
DB_PATH = "./database.db"
USER_DB_PATH = "./user_management.db"
SCRAPED_DB_PATH = "./scraped_pages.db"

# Production
DB_PATH = "/var/lib/dms/database.db"
USER_DB_PATH = "/var/lib/dms/user_management.db"
SCRAPED_DB_PATH = "/var/lib/dms/scraped_pages.db"
```

**Performance Optimization:**
```sql
-- SQLite optimization settings
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456;
```

### 2. ChromaDB Configuration

**Vector Database Settings:**
```python
# ChromaDB configuration
CHROMA_PATH = "./chroma"           # Development
CHROMA_PATH = "/var/lib/dms/chroma" # Production

# Collection settings
COLLECTION_METADATA = {
    "hnsw:space": "cosine",        # Distance metric
    "hnsw:construction_ef": 200,   # Construction parameter
    "hnsw:M": 16                   # Connectivity parameter
}
```

## Security Configuration

### 1. Authentication Settings

```python
# Password policies
PASSWORD_MIN_LENGTH = 8
PASSWORD_REQUIRE_UPPERCASE = True
PASSWORD_REQUIRE_LOWERCASE = True
PASSWORD_REQUIRE_NUMBERS = True
PASSWORD_REQUIRE_SPECIAL = True
PASSWORD_EXPIRY_DAYS = 90

# Session security
SESSION_TIMEOUT_MINUTES = 60
REMEMBER_COOKIE_DURATION = 30  # days
REMEMBER_COOKIE_SECURE = True
REMEMBER_COOKIE_HTTPONLY = True

# Account lockout
MAX_LOGIN_ATTEMPTS = 5
LOCKOUT_DURATION_MINUTES = 30
```

### 2. Rate Limiting Configuration

```python
# Rate limiting settings
RATELIMIT_STORAGE_URL = "memory://"
RATELIMIT_DEFAULT = "100 per hour"
RATELIMIT_HEADERS_ENABLED = True

# Specific endpoint limits
RATE_LIMITS = {
    "/query": "30 per minute",
    "/admin/upload": "10 per minute",
    "/login": "5 per minute"
}
```

### 3. CSRF Protection

```python
# CSRF configuration
WTF_CSRF_ENABLED = True
WTF_CSRF_TIME_LIMIT = 7200  # 2 hours (default)
WTF_CSRF_SSL_STRICT = False  # Set to True in production with HTTPS
WTF_CSRF_CHECK_DEFAULT = True  # Enable CSRF checking by default

# Environment variables for CSRF configuration
WTF_CSRF_ENABLED=true
WTF_CSRF_TIME_LIMIT=7200
WTF_CSRF_SSL_STRICT=false
WTF_CSRF_CHECK_DEFAULT=true
```

**CSRF Token Management:**
- Tokens automatically refresh when expired
- JavaScript API functions handle token refresh transparently
- Increased timeout to 2 hours to reduce expiration issues
- Automatic retry mechanism for failed requests due to token expiration

## Performance Optimization

### 1. System Performance

**Memory Management:**
```python
# Memory optimization
MAX_CONTENT_LENGTH = 25 * 1024 * 1024  # 25MB upload limit
TEMP_FILE_CLEANUP_INTERVAL = 3600       # 1 hour
VISION_CACHE_SIZE = 1000                # Cached analyses
```

**Processing Optimization:**
```python
# Parallel processing
PROCESSING_THREADS = 4          # Embedding threads
BATCH_SIZE = 10                # Embedding batch size
MAX_CONCURRENT_UPLOADS = 3     # Concurrent file uploads
```

### 2. AI Model Optimization

**Ollama Configuration:**
```bash
# Ollama environment variables
OLLAMA_NUM_PARALLEL=4          # Parallel requests
OLLAMA_MAX_LOADED_MODELS=3     # Loaded models
OLLAMA_FLASH_ATTENTION=true    # Flash attention
OLLAMA_GPU_LAYERS=35           # GPU layers (if available)
```

**Model Loading:**
```python
# Model preloading
PRELOAD_MODELS = [
    "llama3.1:8b-instruct-q4_K_M",
    "mxbai-embed-large:latest"
]
```

## Monitoring and Logging

### 1. Logging Configuration

```python
# Logging settings
LOG_LEVEL = "INFO"              # Development
LOG_LEVEL = "WARNING"           # Production
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = "/var/log/dms/app.log"
LOG_MAX_BYTES = 10485760        # 10MB
LOG_BACKUP_COUNT = 5
```

### 2. Analytics Configuration

```python
# Analytics settings
ENABLE_ANALYTICS = True
ANALYTICS_RETENTION_DAYS = 365
ENABLE_GEOLOCATION = True
GEOIP_DATABASE_PATH = "./GeoLite2-City.mmdb"
```

## Backup and Maintenance

### 1. Backup Configuration

```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/var/backups/dms"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup databases
cp /var/lib/dms/database.db "$BACKUP_DIR/database_$DATE.db"
cp /var/lib/dms/user_management.db "$BACKUP_DIR/users_$DATE.db"

# Backup ChromaDB
tar -czf "$BACKUP_DIR/chroma_$DATE.tar.gz" /var/lib/dms/chroma/

# Cleanup old backups (keep 30 days)
find "$BACKUP_DIR" -name "*.db" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
```

### 2. Maintenance Tasks

```python
# Scheduled maintenance
CLEANUP_TEMP_FILES_INTERVAL = 86400     # 24 hours
VACUUM_DATABASE_INTERVAL = 604800       # 7 days
UPDATE_ANALYTICS_INTERVAL = 3600        # 1 hour
VISION_CACHE_CLEANUP_INTERVAL = 43200   # 12 hours
```

This configuration guide provides comprehensive settings for optimal system performance, security, and maintainability across different deployment scenarios.
