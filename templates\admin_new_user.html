{% extends "admin_base.html" %}

{% block title %}Add New User{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Add New User</h1>
            <div class="flex space-x-4">
                <a href="{{ url_for('user.admin_users') }}" class="text-blue-600 hover:underline">&larr; Back to User Management</a>
            </div>
        </div>

            <div class="max-w-3xl mx-auto">
                <div class="bg-white p-6 rounded-lg border border-gray-200">
                    <form method="POST" action="{{ url_for('user.admin_new_user') }}">
                        {{ form.csrf_token }}

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="username" class="block text-gray-700 text-sm font-bold mb-2">Username</label>
                                {{ form.username(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                {% if form.username.errors %}
                                    <p class="text-red-500 text-xs italic mt-1">{{ form.username.errors[0] }}</p>
                                {% endif %}
                                <p class="text-gray-500 text-xs mt-1">3-20 characters, alphanumeric and underscores only</p>
                            </div>

                            <div>
                                <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                                {{ form.email(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                {% if form.email.errors %}
                                    <p class="text-red-500 text-xs italic mt-1">{{ form.email.errors[0] }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="full_name" class="block text-gray-700 text-sm font-bold mb-2">Full Name</label>
                            {{ form.full_name(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                            {% if form.full_name.errors %}
                                <p class="text-red-500 text-xs italic mt-1">{{ form.full_name.errors[0] }}</p>
                            {% endif %}
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="role" class="block text-gray-700 text-sm font-bold mb-2">Role</label>
                                {{ form.role(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline", onchange="showRoleGroupInfo(this.value)") }}
                                {% if form.role.errors %}
                                    <p class="text-red-500 text-xs italic mt-1">{{ form.role.errors[0] }}</p>
                                {% endif %}
                                <p id="roleGroupInfo" class="text-gray-500 text-xs mt-1 hidden"></p>
                            </div>

                            <div>
                                <label for="account_status" class="block text-gray-700 text-sm font-bold mb-2">Account Status</label>
                                {{ form.account_status(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                {% if form.account_status.errors %}
                                    <p class="text-red-500 text-xs italic mt-1">{{ form.account_status.errors[0] }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-4 border-t border-gray-200 pt-4">
                            <h4 class="text-md font-semibold mb-2">Password</h4>
                            <p class="text-gray-500 text-sm mb-4">Leave blank to generate a temporary password</p>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                                    {{ form.password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                    {% if form.password.errors %}
                                        <p class="text-red-500 text-xs italic mt-1">{{ form.password.errors[0] }}</p>
                                    {% endif %}
                                    <p class="text-gray-500 text-xs mt-1">Minimum 8 characters with mixed case, numbers, and symbols</p>
                                </div>

                                <div>
                                    <label for="confirm_password" class="block text-gray-700 text-sm font-bold mb-2">Confirm Password</label>
                                    {{ form.confirm_password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                    {% if form.confirm_password.errors %}
                                        <p class="text-red-500 text-xs italic mt-1">{{ form.confirm_password.errors[0] }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between mt-6">
                            <a href="{{ url_for('user.admin_users') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md text-center transition-colors">
                                Cancel
                            </a>
                            <button type="submit" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md text-center transition-colors">
                                Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
{% endblock %}

{% block scripts %}
    <script>
        // Function to show information about group assignment based on role
        function showRoleGroupInfo(role) {
            const infoElement = document.getElementById('roleGroupInfo');

            if (role === 'editor') {
                infoElement.textContent = 'User will be automatically assigned to the Editor Group with all Editor permissions.';
                infoElement.classList.remove('hidden');
                infoElement.classList.add('text-blue-600');
            } else if (role === 'viewer') {
                infoElement.textContent = 'User will be automatically assigned to the Viewer Group with all Viewer permissions.';
                infoElement.classList.remove('hidden');
                infoElement.classList.add('text-green-600');
            } else {
                infoElement.classList.add('hidden');
            }
        }

        // Show info on page load based on current selection
        document.addEventListener('DOMContentLoaded', function() {
            const roleSelect = document.querySelector('select[name="role"]');
            if (roleSelect) {
                showRoleGroupInfo(roleSelect.value);
            }
        });
    </script>
{% endblock %}
