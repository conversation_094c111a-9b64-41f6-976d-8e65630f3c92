{% extends "admin_base.html" %}

{% block title %}Chat History{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
    <style>
        /* Style for cover images */
        .cover-image {
            order: -1; /* Display cover images first */
            border: 2px solid #3b82f6; /* Blue border to highlight cover images */
            border-radius: 0.5rem;
            padding: 0.25rem;
            background-color: #eff6ff; /* Light blue background */
        }

        /* Make the grid container use flexbox ordering */
        .document-thumbnails-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        /* Additional text contrast fixes specific to chat history page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Ensure markdown content has proper contrast */
        .markdown-content p,
        .markdown-content li,
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            color: #1a202c !important;
        }

        /* Badge colors */
        .bg-blue-100.text-blue-800 {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }

        .bg-green-100.text-green-800 {
            background-color: #d1fae5 !important;
            color: #065f46 !important;
        }

        /* Dark mode styles */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #374151 !important; }
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .bg-yellow-50 { background-color: #92400e !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }

        /* Ensure markdown content has proper contrast in dark mode */
        .dark .markdown-content p,
        .dark .markdown-content li,
        .dark .markdown-content h1,
        .dark .markdown-content h2,
        .dark .markdown-content h3,
        .dark .markdown-content h4,
        .dark .markdown-content h5,
        .dark .markdown-content h6 {
            color: #f3f4f6 !important;
        }

        /* Badge colors in dark mode */
        .dark .bg-blue-100.text-blue-800 {
            background-color: #1e3a8a !important;
            color: #bfdbfe !important;
        }

        .dark .bg-green-100.text-green-800 {
            background-color: #065f46 !important;
            color: #a7f3d0 !important;
        }

        /* Cover image in dark mode */
        .dark .cover-image {
            border-color: #3b82f6;
            background-color: #1e3a8a;
        }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }
        .dark .border-blue-100 { border-color: #1e3a8a !important; }

        /* Conversation listings in dark mode */
        .dark .bg-gray-50 {
            background-color: #374151 !important;
        }

        /* Timestamps in dark mode */
        .dark .text-gray-500 {
            color: #9ca3af !important;
        }

        /* Table styles in dark mode */
        .dark .divide-gray-200 > * { border-color: #4b5563 !important; }
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }
    </style>
    <script>
        // Configure marked.js to properly handle links and other markdown elements
        marked.setOptions({
            breaks: true,  // Add line breaks
            gfm: true,     // Enable GitHub Flavored Markdown
            headerIds: false,
            mangle: false,
            sanitize: false, // Allow HTML in markdown
            renderer: (function() {
                const renderer = new marked.Renderer();

                // Override the link renderer to add target="_blank" and other attributes
                renderer.link = function(href, title, text) {
                    const link = marked.Renderer.prototype.link.call(this, href, title, text);
                    return link.replace('<a ', '<a target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline" ');
                };

                return renderer;
            })()
        });
    </script>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Chat History</h1>

            {% if history %}
                <div class="space-y-8">
                    {% for entry in history %}
                        {% set entry_index = loop.index %}
                        <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                                        {{ entry.category }}
                                    </span>
                                    {% if entry.client_name %}
                                    <span class="ml-2 px-3 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">
                                        {{ entry.client_name }}
                                    </span>
                                    {% endif %}
                                    <span class="ml-2 text-sm text-gray-500">
                                        {{ entry.timestamp }}
                                    </span>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h3 class="text-md font-medium text-gray-700 mb-2">
                                    {% if entry.client_name %}
                                    Question from {{ entry.client_name }}:
                                    {% else %}
                                    Question:
                                    {% endif %}
                                </h3>
                                <div class="bg-blue-50 p-3 rounded-lg border border-blue-100">
                                    {{ entry.question }}
                                </div>
                            </div>

                            {% if entry.document_thumbnails %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Document Thumbnails:</h3>
                                    <div class="document-thumbnails-container">
                                        {% for img in entry.document_thumbnails %}
                                            {% if img.startswith('<div class="image-container">') or img.startswith('<div class="image-container cover-image">') %}
                                                <div id="doc-thumbnail-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('doc-thumbnail-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            {% if entry.url_images %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">URL Images:</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                        {% for img in entry.url_images %}
                                            {% if img.startswith('<div class="image-container">') %}
                                                <div id="url-image-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('url-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% elif img is string and img.startswith('http') and "'" not in img and "{" not in img and "}" not in img %}
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image from URL" class="w-full h-48 object-cover rounded-lg shadow-md hover:opacity-90 transition-opacity" />
                                                    </a>
                                                    <div class="text-xs text-gray-500 mt-1">Image from URL</div>
                                                </div>
                                            {% elif img is string %}
                                                <div class="text-sm">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ img }}</a>
                                                </div>
                                            {% else %}
                                                <div class="text-sm text-red-500">Invalid image URL format</div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            <div class="mb-4">
                                <h3 class="text-md font-medium text-gray-700 mb-2">Answer:</h3>
                                <div class="bg-white p-4 rounded-lg border border-gray-200 prose max-w-none">
                                    {% if entry.client_name %}
                                    <p class="font-medium text-green-700">Hello {{ entry.client_name }},</p>
                                    {% endif %}

                                    <!-- Render the answer content with markdown formatting -->
                                    <div class="markdown-content">
                                        {{ entry.answer|markdown|safe }}
                                    </div>

                                    <!-- Fallback if the answer is empty -->
                                    {% if not entry.answer or entry.answer.strip() == '' %}
                                    <p class="text-red-500">No answer content available</p>
                                    {% endif %}
                                </div>
                            </div>

                            {% if entry.sources %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Sources:</h3>
                                    <div class="bg-white p-3 rounded-lg border border-gray-200">
                                        <div class="space-y-3">
                                            {% set grouped_sources = {} %}

                                            {# Group sources by display_name #}
                                            {% for source in entry.sources %}
                                                {% if source is not string %}
                                                    {% set display_name = source.display_name|default(source.source|default('Unknown')) %}

                                                    {% if display_name not in grouped_sources %}
                                                        {% set _ = grouped_sources.update({
                                                            display_name: {
                                                                'type': source.type|default('unknown'),
                                                                'pages': [],
                                                                'file_path': source.file_path,
                                                                'original_url': source.original_url,
                                                                'image_count': 0,
                                                                'link_count': 0,
                                                                'sources': []
                                                            }
                                                        }) %}
                                                    {% endif %}

                                                    {# Add page if it exists and isn't already in the array #}
                                                    {% if source.page and source.page not in grouped_sources[display_name].pages %}
                                                        {% set _ = grouped_sources[display_name].pages.append(source.page) %}
                                                    {% endif %}

                                                    {# Sum up image and link counts #}
                                                    {% set _ = grouped_sources[display_name].update({
                                                        'image_count': grouped_sources[display_name].image_count + (source.image_count|default(0)),
                                                        'link_count': grouped_sources[display_name].link_count + (source.link_count|default(0))
                                                    }) %}

                                                    {# Store the original source object #}
                                                    {% set _ = grouped_sources[display_name].sources.append(source) %}
                                                {% endif %}
                                            {% endfor %}

                                            {# Display grouped sources #}
                                            {% for source_name, source_group in grouped_sources|dictsort %}
                                                <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                                    <div class="flex flex-wrap items-center gap-2">
                                                        <div class="font-medium">
                                                            {% if source_group.type == 'pdf' %}
                                                                {% if source_group.original_url %}
                                                                    <a href="{{ source_group.original_url }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline font-medium" title="View PDF">{{ source_name }}</a>
                                                                {% elif source_group.file_path %}
                                                                    <a href="{{ source_group.file_path }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline font-medium" title="View PDF">{{ source_name }}</a>
                                                                {% else %}
                                                                    {{ source_name }}
                                                                {% endif %}
                                                            {% elif source_group.type == 'url' %}
                                                                {% if source_group.original_url %}
                                                                    <a href="{{ source_group.original_url }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline font-medium" title="{{ source_group.original_url }}">{{ source_name }}</a>
                                                                {% elif source_name.startswith('http') %}
                                                                    <a href="{{ source_name }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline font-medium" title="{{ source_name }}">{{ source_name }}</a>
                                                                {% else %}
                                                                    {{ source_name }}
                                                                {% endif %}
                                                            {% else %}
                                                                {{ source_name }}
                                                            {% endif %}
                                                        </div>

                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">{{ source_group.type }}</span>

                                                        {% if source_group.image_count > 0 %}
                                                            <span class="inline-flex items-center px-2 py-0.5 ml-1 rounded text-xs font-medium bg-blue-100 text-blue-800">{{ source_group.image_count }} images</span>
                                                        {% endif %}

                                                        {% if source_group.link_count > 0 %}
                                                            <span class="inline-flex items-center px-2 py-0.5 ml-1 rounded text-xs font-medium bg-green-100 text-green-800">{{ source_group.link_count }} links</span>
                                                        {% endif %}
                                                    </div>

                                                    {% if source_group.pages and source_group.pages|length > 0 %}
                                                        <div class="text-sm text-gray-500 mt-1">Pages: {{ source_group.pages|sort|join(', ') }}</div>
                                                    {% endif %}
                                                </div>
                                            {% endfor %}

                                            {# Display string sources if any #}
                                            {% for source in entry.sources %}
                                                {% if source is string %}
                                                    <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                                        <div class="text-sm text-gray-700">{{ source }}</div>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            {% if entry.pdf_images %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Images from PDFs:</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                        {% for img in entry.pdf_images %}
                                            {% if img.startswith('<div class="image-container">') %}
                                                <div id="pdf-image-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('pdf-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% elif img is string and (img.startswith('http') or img.startswith('/')) and "'" not in img and "{" not in img and "}" not in img %}
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image from PDF" class="w-full h-48 object-cover rounded-lg shadow-md hover:opacity-90 transition-opacity" />
                                                    </a>
                                                    <div class="text-xs text-gray-500 mt-1">{{ img.split('/')[-1] if '/' in img else img }}</div>
                                                </div>
                                            {% elif img is string %}
                                                <div class="text-sm">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ img.split('/')[-1] if '/' in img else img }}</a>
                                                </div>
                                            {% else %}
                                                <div class="text-sm text-red-500">Invalid image URL format</div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% elif entry.images %}
                                <!-- Fallback for backward compatibility -->
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Images:</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                        {% for img in entry.images %}
                                            {% if img.startswith('<div class="image-container">') %}
                                                <div id="fallback-image-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('fallback-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% elif img is string and (img.startswith('http') or img.startswith('/')) and "'" not in img and "{" not in img and "}" not in img %}
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image" class="w-full h-48 object-cover rounded-lg shadow-md hover:opacity-90 transition-opacity" />
                                                    </a>
                                                    <div class="text-xs text-gray-500 mt-1">{{ img.split('/')[-1] if '/' in img else img }}</div>
                                                </div>
                                            {% elif img is string %}
                                                <div class="text-sm">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ img.split('/')[-1] if '/' in img else img }}</a>
                                                </div>
                                            {% else %}
                                                <div class="text-sm text-red-500">Invalid image URL format</div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            {% if entry.pdf_links %}
                                <div>
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Document Links:</h3>
                                    <div class="bg-white p-3 rounded-lg border border-gray-200">
                                        <ul class="list-disc pl-5 space-y-1">
                                            {% for link in entry.pdf_links %}
                                                <li class="text-sm">
                                                    <a href="{{ link }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ link }}</a>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                No chat history found. Start a conversation to see history here.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
{% endblock %}