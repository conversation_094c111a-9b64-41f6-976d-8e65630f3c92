<div class="card mb-3 greeting-template-card" data-template-id="{{ template.id }}">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h6 class="card-title mb-2">
                    <span class="badge bg-{{ 'success' if template.is_active else 'secondary' }} me-2">
                        {{ 'Active' if template.is_active else 'Inactive' }}
                    </span>
                    <span class="badge bg-info">Weight: {{ template.weight }}</span>
                </h6>
                <p class="card-text greeting-text">{{ template.greeting_text }}</p>
                <div class="text-muted small">
                    <i class="fas fa-calendar-plus me-1"></i>Created: {{ template.created_at[:19] if template.created_at else 'Unknown' }}
                    {% if template.updated_at and template.updated_at != template.created_at %}
                        <br><i class="fas fa-edit me-1"></i>Updated: {{ template.updated_at[:19] }}
                    {% endif %}
                </div>
            </div>
            <div class="ms-3">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary edit-greeting-btn" 
                            data-template-id="{{ template.id }}"
                            data-template-type="{{ template.template_type }}"
                            data-greeting-text="{{ template.greeting_text }}"
                            data-weight="{{ template.weight }}"
                            data-is-active="{{ template.is_active }}"
                            title="Edit greeting">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success test-greeting-btn"
                            data-greeting-text="{{ template.greeting_text }}"
                            title="Test greeting">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-greeting-btn"
                            data-template-id="{{ template.id }}"
                            data-greeting-text="{{ template.greeting_text }}"
                            title="Delete greeting">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Preview section -->
        <div class="mt-3 p-2 bg-light rounded greeting-preview" style="display: none;">
            <small class="text-muted">Preview with sample name:</small>
            <div class="preview-text mt-1"></div>
        </div>
    </div>
</div>
