document.addEventListener('DOMContentLoaded', function() {
    // Tab switching for prompt templates
    setupTemplateTabs();

    // Tab switching for followup templates
    setupFollowupTabs();

    // Add/remove phrases for insufficient info
    setupPhraseManagement();

    // Form submission
    setupFormSubmission();
});

function setupTemplateTabs() {
    const tabs = [
        { id: 'templateTabStrict', content: 'templateContentStrict' },
        { id: 'templateTabBalanced', content: 'templateContentBalanced' },
        { id: 'templateTabOff', content: 'templateContentOff' },
        { id: 'templateTabGeneral', content: 'templateContentGeneral' },
        { id: 'templateTabDocSpecific', content: 'templateContentDocSpecific' }
    ];

    tabs.forEach(tab => {
        document.getElementById(tab.id).addEventListener('click', function() {
            // Hide all content
            document.querySelectorAll('.template-content').forEach(el => {
                el.classList.add('hidden');
            });

            // Show selected content
            document.getElementById(tab.content).classList.remove('hidden');

            // Update tab styling
            tabs.forEach(t => {
                document.getElementById(t.id).classList.remove('bg-blue-600', 'text-white');
                document.getElementById(t.id).classList.add('bg-gray-300', 'text-gray-700');
            });

            this.classList.remove('bg-gray-300', 'text-gray-700');
            this.classList.add('bg-blue-600', 'text-white');
        });
    });
}

function setupFollowupTabs() {
    const tabs = [
        { id: 'followupTabDefault', content: 'followupContentDefault' },
        { id: 'followupTabInsufficient', content: 'followupContentInsufficient' }
    ];

    tabs.forEach(tab => {
        document.getElementById(tab.id).addEventListener('click', function() {
            // Hide all content
            document.querySelectorAll('.followup-content').forEach(el => {
                el.classList.add('hidden');
            });

            // Show selected content
            document.getElementById(tab.content).classList.remove('hidden');

            // Update tab styling
            tabs.forEach(t => {
                document.getElementById(t.id).classList.remove('bg-blue-600', 'text-white');
                document.getElementById(t.id).classList.add('bg-gray-300', 'text-gray-700');
            });

            this.classList.remove('bg-gray-300', 'text-gray-700');
            this.classList.add('bg-blue-600', 'text-white');
        });
    });
}

function setupPhraseManagement() {
    // Add new phrase
    document.getElementById('addPhrase').addEventListener('click', function() {
        const container = document.getElementById('insufficientPhrases');
        const newPhrase = document.createElement('div');
        newPhrase.className = 'flex items-center';
        newPhrase.innerHTML = `
            <input type="text" name="insufficient_phrase[]"
                class="flex-grow px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                placeholder="Enter a phrase...">
            <button type="button" class="remove-phrase ml-2 px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        `;
        container.appendChild(newPhrase);

        // Add event listener to the new remove button
        newPhrase.querySelector('.remove-phrase').addEventListener('click', function() {
            container.removeChild(newPhrase);
        });
    });

    // Remove phrase (for existing buttons)
    document.querySelectorAll('.remove-phrase').forEach(button => {
        button.addEventListener('click', function() {
            this.parentElement.remove();
        });
    });
}

function setupFormSubmission() {
    document.getElementById('queryConfigForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        const statusMessage = document.getElementById('statusMessage');
        statusMessage.innerHTML = `
            <div class="p-4 bg-blue-100 text-blue-700 rounded-md flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving query configuration...
            </div>
        `;
        statusMessage.classList.remove('hidden');

        try {
            // Gather form data
            const formData = new FormData(this);

            // Extract insufficient info phrases
            const insufficientPhrases = [];
            document.querySelectorAll('input[name="insufficient_phrase[]"]').forEach(input => {
                if (input.value.trim()) {
                    insufficientPhrases.push(input.value.trim());
                }
            });

            // Prepare data object
            const data = {
                preamble: formData.get('preamble'),
                anti_hallucination_mode: formData.get('default_mode'),
                anti_hallucination_custom_instructions: formData.get('custom_instructions'),
                use_vision: formData.get('use_vision') === 'on',
                prompt_templates: {
                    strict: formData.get('template_strict'),
                    balanced: formData.get('template_balanced'),
                    off: formData.get('template_off'),
                    general: formData.get('template_general'),
                    document_specific: formData.get('template_doc_specific')
                },
                insufficient_info_phrases: insufficientPhrases,
                followup_question_templates: {
                    default: formData.get('followup_default'),
                    insufficient_info: formData.get('followup_insufficient')
                }
            };

            // Send data to server
            const response = await fetch('/admin/query_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            // Show success/error message
            if (response.ok) {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-green-100 text-green-700 rounded-md">
                        ${result.message || "Query configuration saved successfully"}
                    </div>
                `;

                Toastify({
                    text: result.message || "Query configuration saved successfully",
                    duration: 3000,
                    backgroundColor: "#00C851"
                }).showToast();
            } else {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-red-100 text-red-700 rounded-md">
                        Error: ${result.error || "Failed to save query configuration"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${result.error || "Failed to save query configuration"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        } catch (error) {
            statusMessage.innerHTML = `
                <div class="p-4 bg-red-100 text-red-700 rounded-md">
                    Error: ${error.message || "An unexpected error occurred"}
                </div>
            `;

            Toastify({
                text: `Error: ${error.message || "An unexpected error occurred"}`,
                duration: 3000,
                backgroundColor: "#ff4444"
            }).showToast();
        }
    });
}
