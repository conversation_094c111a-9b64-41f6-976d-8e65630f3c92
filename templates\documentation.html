<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - Document Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .doc-sidebar {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }
        .doc-content {
            padding: 20px;
        }
        .doc-nav-link {
            color: #495057;
            text-decoration: none;
            padding: 8px 12px;
            display: block;
            border-radius: 4px;
            margin-bottom: 4px;
        }
        .doc-nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }
        .doc-nav-link.active {
            background-color: #007bff;
            color: white;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 doc-sidebar">
                <h5 class="mb-4">
                    <i class="fas fa-book me-2"></i>Documentation
                </h5>
                <nav>
                    <a href="#overview" class="doc-nav-link active">Overview</a>
                    <a href="#getting-started" class="doc-nav-link">Getting Started</a>
                    <a href="#uploading-content" class="doc-nav-link">Uploading Content</a>
                    <a href="#managing-files" class="doc-nav-link">Managing Files</a>
                    <a href="#chat-interface" class="doc-nav-link">Chat Interface</a>
                    <a href="#analytics" class="doc-nav-link">Analytics</a>
                    <a href="#user-management" class="doc-nav-link">User Management</a>
                    <a href="#troubleshooting" class="doc-nav-link">Troubleshooting</a>
                </nav>
                <hr>
                <a href="/admin/dashboard" class="btn btn-primary btn-sm">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin
                </a>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 doc-content">
                <section id="overview">
                    <h1>Document Management System</h1>
                    <p class="lead">A comprehensive AI-powered document management and chat system.</p>
                    
                    <h2>Features</h2>
                    <ul>
                        <li><strong>PDF Upload & Processing:</strong> Upload PDF documents with automatic text extraction and vectorization</li>
                        <li><strong>URL Scraping:</strong> Add web content by URL with configurable depth crawling</li>
                        <li><strong>AI-Powered Chat:</strong> Query your documents using natural language with multiple AI models</li>
                        <li><strong>Category Organization:</strong> Organize content into categories for better management</li>
                        <li><strong>User Management:</strong> Role-based access control with permissions</li>
                        <li><strong>Analytics:</strong> Comprehensive usage analytics and performance metrics</li>
                    </ul>
                </section>

                <section id="getting-started" class="mt-5">
                    <h2>Getting Started</h2>
                    <p>Follow these steps to start using the Document Management System:</p>
                    
                    <ol>
                        <li><strong>Login:</strong> Access the admin dashboard at <code>/admin</code></li>
                        <li><strong>Create Categories:</strong> Organize your content by creating categories</li>
                        <li><strong>Upload Content:</strong> Add PDFs or URLs to your knowledge base</li>
                        <li><strong>Configure Models:</strong> Set up AI models in the Model Settings</li>
                        <li><strong>Start Chatting:</strong> Use the chat interface to query your documents</li>
                    </ol>
                </section>

                <section id="uploading-content" class="mt-5">
                    <h2>Uploading Content</h2>
                    
                    <h3>PDF Upload</h3>
                    <p>Upload PDF documents to make them searchable:</p>
                    <div class="code-block">
                        1. Navigate to Content Management → Upload Content<br>
                        2. Select a category (create one if needed)<br>
                        3. Choose your PDF file (max 25MB)<br>
                        4. Optionally add a source URL<br>
                        5. Configure vision model settings if needed<br>
                        6. Click Upload
                    </div>

                    <h3>URL Scraping</h3>
                    <p>Add web content by URL:</p>
                    <div class="code-block">
                        1. Enter the URL in the URL field<br>
                        2. Set crawl depth (0-3 levels)<br>
                        3. Select category<br>
                        4. Click Upload
                    </div>
                </section>

                <section id="managing-files" class="mt-5">
                    <h2>Managing Files</h2>
                    <p>View and manage your uploaded content:</p>
                    
                    <ul>
                        <li><strong>View Files:</strong> See all uploaded PDFs and URLs by category</li>
                        <li><strong>Vector Data:</strong> Examine how content is chunked and vectorized</li>
                        <li><strong>Delete Content:</strong> Remove files and all associated data</li>
                        <li><strong>Source URLs:</strong> View original sources for uploaded content</li>
                    </ul>
                </section>

                <section id="chat-interface" class="mt-5">
                    <h2>Chat Interface</h2>
                    <p>Query your documents using natural language:</p>
                    
                    <h3>Features</h3>
                    <ul>
                        <li>Category-based queries</li>
                        <li>Anti-hallucination modes</li>
                        <li>Source citations</li>
                        <li>Image display from documents</li>
                        <li>Follow-up question suggestions</li>
                    </ul>

                    <h3>Usage Tips</h3>
                    <div class="code-block">
                        • Be specific in your questions<br>
                        • Use the category filter to narrow results<br>
                        • Check source citations for accuracy<br>
                        • Try different anti-hallucination modes for better results
                    </div>
                </section>

                <section id="analytics" class="mt-5">
                    <h2>Analytics</h2>
                    <p>Monitor system usage and performance:</p>
                    
                    <ul>
                        <li><strong>Usage Statistics:</strong> Track queries, users, and popular content</li>
                        <li><strong>Performance Metrics:</strong> Monitor response times and model performance</li>
                        <li><strong>Geographic Data:</strong> See where users are accessing from</li>
                        <li><strong>Chat History:</strong> Review past conversations</li>
                        <li><strong>Session Management:</strong> Monitor active user sessions</li>
                    </ul>
                </section>

                <section id="user-management" class="mt-5">
                    <h2>User Management</h2>
                    <p>Manage users and permissions:</p>
                    
                    <h3>Roles</h3>
                    <ul>
                        <li><strong>Admin:</strong> Full system access</li>
                        <li><strong>Editor:</strong> Content management and analytics</li>
                        <li><strong>Viewer:</strong> Read-only access to analytics</li>
                    </ul>

                    <h3>Permissions</h3>
                    <p>Fine-grained control over dashboard functions:</p>
                    <ul>
                        <li>Upload Content</li>
                        <li>Manage Files</li>
                        <li>Model Settings</li>
                        <li>Chat History</li>
                        <li>User Management</li>
                    </ul>
                </section>

                <section id="troubleshooting" class="mt-5">
                    <h2>Troubleshooting</h2>
                    
                    <h3>Common Issues</h3>
                    <div class="accordion" id="troubleshootingAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#upload-issues">
                                    Upload Issues
                                </button>
                            </h2>
                            <div id="upload-issues" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li>Check file size (max 25MB for PDFs)</li>
                                        <li>Ensure category is selected</li>
                                        <li>Verify file format (PDF only)</li>
                                        <li>Check network connection for URL scraping</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chat-issues">
                                    Chat Issues
                                </button>
                            </h2>
                            <div id="chat-issues" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li>Ensure content is uploaded and processed</li>
                                        <li>Check if AI models are configured</li>
                                        <li>Try different categories</li>
                                        <li>Verify network connectivity</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="support" class="mt-5">
                    <h2>Support</h2>
                    <p>Need help? Contact our support team:</p>
                    <ul>
                        <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><strong>System Info:</strong> Use the Help menu in the admin interface</li>
                        <li><strong>Chat Interface:</strong> <a href="/" target="_blank">Try the chat interface</a></li>
                    </ul>
                </section>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.doc-nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                    
                    // Update active link
                    document.querySelectorAll('.doc-nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Update active link on scroll
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.doc-nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
