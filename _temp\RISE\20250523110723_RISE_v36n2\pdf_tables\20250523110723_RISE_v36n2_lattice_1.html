<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">6</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">7</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">8</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">9</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">10</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">11</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">12</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Non-Mist Propagation Method \nNon-Mist Propagation Method</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Species \nSpecies</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average Rooting \nAverage Rooting\n(%) \n(%)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average \nAverage \nNumber of Roots (no.) \nNumber of Roots (no.)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average \nAverage\nLength of roots (cm) \nLength of roots (cm)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Ctrl</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA| \nIBA \nCtrl  | 300  | 500  | 800  | Ctrl | 300 | 500 | 800 | Ctrl  | 300  | 500 | 800 \n300 \nppm\n100</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n500 \nPpm [ ppm | ppm \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA  | IBA \nIBA \n800  \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Ctrl</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n300 \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA | IBA | IBA \nIBA \n500 \nPpm [ ppm ( ppm \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n800  \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Ctrl</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n300 \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA  | IBA | IBA \nIBA \n500 \nPpm | ppm | ppm \nppm</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">IBA \n800  \nppm</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Arangen</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Arangen  | 96  | 100 \n96</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">80 \n80</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">96</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">9 | 97  | 100 | 82  | 97  | 96 | 100  |  80 \n97</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">100</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">82</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">97</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">96</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">100</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">80</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">96 \n96</td>
    </tr>
  </tbody>
</table>