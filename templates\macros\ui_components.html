{# UI Components Macros for Document Management System - DarkPan Theme #}

{# Button Macro - DarkPan Style #}
{% macro button(text, type='primary', size='md', icon=None, href=None, id=None, classes='', attrs={}) %}
    {% set button_classes = {
        'primary': 'btn-darkpan-primary',
        'secondary': 'btn-darkpan-light',
        'success': 'btn-success',
        'danger': 'btn-danger',
        'warning': 'btn-warning',
        'info': 'btn-info',
        'light': 'btn-darkpan-light',
        'dark': 'btn-darkpan-dark',
        'link': 'btn-link',
        'outline-primary': 'btn-outline-danger',
        'outline-secondary': 'btn-outline-secondary',
        'outline-success': 'btn-outline-success',
        'outline-danger': 'btn-outline-danger',
        'outline-warning': 'btn-outline-warning',
        'outline-info': 'btn-outline-info',
        'outline-light': 'btn-outline-light',
        'outline-dark': 'btn-outline-dark'
    } %}

    {% set size_classes = {
        'sm': 'btn-sm',
        'md': '',
        'lg': 'btn-lg'
    } %}

    {% set tag = 'a' if href else 'button' %}
    {% set href_attr = 'href="' + href + '"' if href else '' %}
    {% set id_attr = 'id="' + id + '"' if id else '' %}

    {% set additional_attrs = '' %}
    {% for key, value in attrs.items() %}
        {% set additional_attrs = additional_attrs + ' ' + key + '="' + value + '"' %}
    {% endfor %}

    <{{ tag }} {{ href_attr }} {{ id_attr }} class="btn {{ button_classes[type] }} {{ size_classes[size] }} {{ classes }}" {{ additional_attrs }}>
        {% if icon %}
            <i class="{{ icon }} me-2"></i>
        {% endif %}
        {{ text }}
    </{{ tag }}>
{% endmacro %}

{# Card Macro - DarkPan Style #}
{% macro card(title=None, subtitle=None, content=None, footer=None, header_classes='', body_classes='', footer_classes='', card_classes='') %}
    <div class="card bg-dark {{ card_classes }}">
        {% if title %}
            <div class="card-header border-dark {{ header_classes }}">
                <div class="d-flex align-items-center justify-content-between">
                    <h5 class="text-light mb-0">{{ title }}</h5>
                    {% if subtitle %}
                        <h6 class="text-light">{{ subtitle }}</h6>
                    {% endif %}
                </div>
            </div>
        {% endif %}

        <div class="card-body {{ body_classes }}">
            {% if content %}
                {{ content }}
            {% else %}
                {{ caller() if caller else '' }}
            {% endif %}
        </div>

        {% if footer %}
            <div class="card-footer border-dark {{ footer_classes }}">
                {{ footer }}
            </div>
        {% endif %}
    </div>
{% endmacro %}

{# Stat Card Macro - DarkPan Style #}
{% macro stat_card(title, value, icon, color='primary') %}
    {% set icon_colors = {
        'primary': 'text-red',
        'secondary': 'text-light',
        'success': 'text-success',
        'danger': 'text-danger',
        'warning': 'text-warning',
        'info': 'text-info'
    } %}

    <div class="stat-card">
        <i class="{{ icon }} stat-icon {{ icon_colors[color] }}"></i>
        <h1 class="stat-value">{{ value }}</h1>
        <p class="stat-title">{{ title }}</p>
    </div>
{% endmacro %}

{# Alert Macro - DarkPan Style #}
{% macro alert(message, type='info', dismissible=False, icon=None) %}
    {% set alert_icons = {
        'primary': 'fas fa-info-circle',
        'secondary': 'fas fa-info-circle',
        'success': 'fas fa-check-circle',
        'danger': 'fas fa-exclamation-triangle',
        'warning': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle',
        'light': 'fas fa-info-circle',
        'dark': 'fas fa-info-circle'
    } %}

    {% set alert_bg_classes = {
        'primary': 'bg-dark border-danger',
        'secondary': 'bg-dark border-light',
        'success': 'bg-dark border-success',
        'danger': 'bg-dark border-danger',
        'warning': 'bg-dark border-warning',
        'info': 'bg-dark border-info',
        'light': 'bg-dark border-light',
        'dark': 'bg-dark border-dark'
    } %}

    {% set alert_text_classes = {
        'primary': 'text-red',
        'secondary': 'text-light',
        'success': 'text-success',
        'danger': 'text-danger',
        'warning': 'text-warning',
        'info': 'text-info',
        'light': 'text-light',
        'dark': 'text-light'
    } %}

    <div class="alert {{ alert_bg_classes[type] }} {{ alert_text_classes[type] }} {% if dismissible %}alert-dismissible fade show{% endif %}" role="alert">
        {% if icon or alert_icons[type] %}
            <i class="{{ icon if icon else alert_icons[type] }} me-2"></i>
        {% endif %}
        {{ message }}
        {% if dismissible %}
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close"></button>
        {% endif %}
    </div>
{% endmacro %}

{# Badge Macro - DarkPan Style #}
{% macro badge(text, type='primary', pill=False, classes='') %}
    {% set badge_bg_classes = {
        'primary': 'bg-danger',
        'secondary': 'bg-secondary',
        'success': 'bg-success',
        'danger': 'bg-danger',
        'warning': 'bg-warning',
        'info': 'bg-info',
        'light': 'bg-light text-dark',
        'dark': 'bg-dark'
    } %}

    <span class="badge {{ badge_bg_classes[type] }} {{ 'rounded-pill' if pill else '' }} {{ classes }}">{{ text }}</span>
{% endmacro %}

{# Form Input Macro - DarkPan Style #}
{% macro input(type='text', name='', id='', label='', placeholder='', value='', required=False, help_text=None, error=None, classes='') %}
    <div class="mb-3">
        {% if label %}
            <label for="{{ id if id else name }}" class="form-label text-light">
                {{ label }}{% if required %} <span class="text-danger">*</span>{% endif %}
            </label>
        {% endif %}

        <input
            type="{{ type }}"
            class="form-control bg-dark border-dark text-light {{ 'is-invalid' if error }} {{ classes }}"
            id="{{ id if id else name }}"
            name="{{ name }}"
            placeholder="{{ placeholder }}"
            value="{{ value }}"
            {{ 'required' if required else '' }}
        >

        {% if help_text %}
            <div class="form-text text-light">{{ help_text }}</div>
        {% endif %}

        {% if error %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endif %}
    </div>
{% endmacro %}

{# Form Select Macro - DarkPan Style #}
{% macro select(name='', id='', label='', options=[], selected='', required=False, help_text=None, error=None, classes='', multiple=False) %}
    <div class="mb-3">
        {% if label %}
            <label for="{{ id if id else name }}" class="form-label text-light">
                {{ label }}{% if required %} <span class="text-danger">*</span>{% endif %}
            </label>
        {% endif %}

        <select
            class="form-select bg-dark border-dark text-light {{ 'is-invalid' if error }} {{ classes }}"
            id="{{ id if id else name }}"
            name="{{ name }}"
            {{ 'required' if required else '' }}
            {{ 'multiple' if multiple else '' }}
        >
            {% for option in options %}
                {% if option is mapping %}
                    <option value="{{ option.value }}" {{ 'selected' if option.value == selected or (multiple and option.value in selected) else '' }}>
                        {{ option.text }}
                    </option>
                {% else %}
                    <option value="{{ option }}" {{ 'selected' if option == selected or (multiple and option in selected) else '' }}>
                        {{ option }}
                    </option>
                {% endif %}
            {% endfor %}
        </select>

        {% if help_text %}
            <div class="form-text text-light">{{ help_text }}</div>
        {% endif %}

        {% if error %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endif %}
    </div>
{% endmacro %}

{# Form Textarea Macro - DarkPan Style #}
{% macro textarea(name='', id='', label='', placeholder='', value='', rows=3, required=False, help_text=None, error=None, classes='') %}
    <div class="mb-3">
        {% if label %}
            <label for="{{ id if id else name }}" class="form-label text-light">
                {{ label }}{% if required %} <span class="text-danger">*</span>{% endif %}
            </label>
        {% endif %}

        <textarea
            class="form-control bg-dark border-dark text-light {{ 'is-invalid' if error }} {{ classes }}"
            id="{{ id if id else name }}"
            name="{{ name }}"
            placeholder="{{ placeholder }}"
            rows="{{ rows }}"
            {{ 'required' if required else '' }}
        >{{ value }}</textarea>

        {% if help_text %}
            <div class="form-text text-light">{{ help_text }}</div>
        {% endif %}

        {% if error %}
            <div class="invalid-feedback">{{ error }}</div>
        {% endif %}
    </div>
{% endmacro %}

{# Pagination Macro - DarkPan Style #}
{% macro pagination(current_page, total_pages, url_pattern, align='center', size='md', show_first_last=True) %}
    {% set size_class = {
        'sm': 'pagination-sm',
        'md': '',
        'lg': 'pagination-lg'
    } %}

    {% set align_class = {
        'start': 'justify-content-start',
        'center': 'justify-content-center',
        'end': 'justify-content-end'
    } %}

    <nav aria-label="Page navigation">
        <ul class="pagination {{ size_class[size] }} {{ align_class[align] }}">
            {% if show_first_last and current_page > 1 %}
                <li class="page-item">
                    <a class="page-link bg-dark border-dark text-light" href="{{ url_pattern|replace('PAGE', '1') }}" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
            {% endif %}

            <li class="page-item {{ 'disabled' if current_page == 1 else '' }}">
                <a class="page-link bg-dark border-dark text-light" href="{{ url_pattern|replace('PAGE', (current_page - 1)|string) if current_page > 1 else '#' }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>

            {% for i in range(max(1, current_page - 2), min(total_pages + 1, current_page + 3)) %}
                <li class="page-item {{ 'active' if i == current_page else '' }}">
                    <a class="page-link {% if i == current_page %}bg-danger border-danger text-white{% else %}bg-dark border-dark text-light{% endif %}"
                       href="{{ url_pattern|replace('PAGE', i|string) if i != current_page else '#' }}">{{ i }}</a>
                </li>
            {% endfor %}

            <li class="page-item {{ 'disabled' if current_page == total_pages else '' }}">
                <a class="page-link bg-dark border-dark text-light" href="{{ url_pattern|replace('PAGE', (current_page + 1)|string) if current_page < total_pages else '#' }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>

            {% if show_first_last and current_page < total_pages %}
                <li class="page-item">
                    <a class="page-link bg-dark border-dark text-light" href="{{ url_pattern|replace('PAGE', total_pages|string) }}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endmacro %}

{# DarkPan Progress Bar Macro #}
{% macro progress_bar(value, max=100, height=10, color='primary') %}
    {% set color_classes = {
        'primary': 'bg-danger',
        'secondary': 'bg-secondary',
        'success': 'bg-success',
        'danger': 'bg-danger',
        'warning': 'bg-warning',
        'info': 'bg-info'
    } %}

    <div class="progress" style="height: {{ height }}px;">
        <div class="progress-bar {{ color_classes[color] }}" role="progressbar"
             style="width: {{ (value / max * 100)|round }}%;"
             aria-valuenow="{{ value }}" aria-valuemin="0" aria-valuemax="{{ max }}">
            {{ (value / max * 100)|round }}%
        </div>
    </div>
{% endmacro %}

{# DarkPan Back to Top Button Macro #}
{% macro back_to_top_button() %}
    <a href="#" class="btn btn-lg btn-darkpan-primary btn-lg-square back-to-top">
        <i class="bi bi-arrow-up"></i>
    </a>
{% endmacro %}
