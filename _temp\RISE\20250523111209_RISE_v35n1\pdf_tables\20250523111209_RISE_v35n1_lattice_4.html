<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Cropping \nCropping\nCycle \nCycle</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Crop \nCrop \nPlanted \nPlanted</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Duration of \nCropping \nCropping \nCycle \nCycle</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Duration of  | Frequency \nFrequency \nof rain \nof rain</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total \nTotal \namount of \namount of \nRain (mm) \nRain (mm)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Mean \nMean</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1st \n1st</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Corn \nCorn</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">July- \nJuly-\nDecember \nDecember \n2016 \n2016</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">22 \n22</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">837.28 \n837.28</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">38.06 \n38.06</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">2nd \n2nd</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Corn \nCorn</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">December \nDecember \n2016 - March \n2016 - March \n2017 \n2017</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">11 \n11</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">472.67 \n472.67</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">42.97 \n42.97</td>
    </tr>
  </tbody>
</table>