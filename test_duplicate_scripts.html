<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Duplicate Script Loading</title>
</head>
<body>
    <h1>Testing Duplicate Script Loading</h1>
    <div id="test-results"></div>
    
    <!-- Load utilities.js multiple times to test protection -->
    <script src="/static/js/utilities.js"></script>
    <script src="/static/js/utilities.js"></script>
    <script src="/static/js/utilities.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            
            // Test 1: Check if DMSUtils exists
            if (typeof window.DMSUtils !== 'undefined') {
                resultsDiv.innerHTML += '<p style="color: green;">✓ DMSUtils is defined</p>';
            } else {
                resultsDiv.innerHTML += '<p style="color: red;">✗ DMSUtils is not defined</p>';
            }
            
            // Test 2: Check if functions are available
            if (typeof window.DMSUtils.showToast === 'function') {
                resultsDiv.innerHTML += '<p style="color: green;">✓ DMSUtils.showToast function is available</p>';
            } else {
                resultsDiv.innerHTML += '<p style="color: red;">✗ DMSUtils.showToast function is not available</p>';
            }
            
            // Test 3: Check if dark mode functions are available
            if (typeof window.DMSUtils.initDarkMode === 'function') {
                resultsDiv.innerHTML += '<p style="color: green;">✓ DMSUtils.initDarkMode function is available</p>';
            } else {
                resultsDiv.innerHTML += '<p style="color: red;">✗ DMSUtils.initDarkMode function is not available</p>';
            }
            
            // Test 4: Try to use a function
            try {
                window.DMSUtils.initDarkMode();
                resultsDiv.innerHTML += '<p style="color: green;">✓ DMSUtils.initDarkMode() executed successfully</p>';
            } catch (error) {
                resultsDiv.innerHTML += '<p style="color: red;">✗ Error executing DMSUtils.initDarkMode(): ' + error.message + '</p>';
            }
            
            resultsDiv.innerHTML += '<p><strong>Test completed. Check browser console for any warnings about duplicate declarations.</strong></p>';
        });
    </script>
</body>
</html>
