#!/usr/bin/env python
"""
Test script for MaxMind GeoLite2 geolocation functionality.

This script demonstrates how to use the MaxMind GeoLite2 geolocation functionality
in development mode. It tests various IP addresses and shows the results.

Usage:
    python test_geolocation.py [ip_address]

If no IP address is provided, it will use a default test IP.

Note: You need to set MAXMIND_ACCOUNT_ID and MAXMIND_LICENSE_KEY environment variables
or add them to your .env file for this script to work properly.
"""

import os
import sys
import logging
from pprint import pprint
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# Set up development environment variables
os.environ["DEV_MODE"] = "true"
os.environ["DEV_LOG_LEVEL"] = "DEBUG"

# Import geolocation utilities
from geo_utils import (
    test_geolocation,
    get_development_info,
    DEV_MODE,
    TEST_IP,
    MAXMIND_ACCOUNT_ID,
    MAXMIND_LICENSE_KEY
)

# Check if MaxMind credentials are configured
if not MAXMIND_ACCOUNT_ID or not MAXMIND_LICENSE_KEY:
    print("WARNING: MaxMind credentials not configured.")
    print("Set MAXMIND_ACCOUNT_ID and MAXMIND_LICENSE_KEY environment variables.")
    print("Sign up at https://www.maxmind.com/en/geolite2/signup")
    print("This script will still run, but geolocation will be limited.")
    print("")

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Run geolocation tests."""
    # Print development environment info
    print("\n=== Development Environment Info ===")
    dev_info = get_development_info()
    pprint(dev_info)

    # Get test IP from command line or use default
    if len(sys.argv) > 1:
        test_ip = sys.argv[1]
    elif TEST_IP:
        test_ip = TEST_IP
        print(f"\nUsing TEST_IP from environment: {TEST_IP}")
    else:
        test_ip = "*******"  # Google's DNS as default
        print(f"\nNo IP provided, using default test IP: {test_ip}")

    # Test geolocation with the specified IP
    print(f"\n=== Testing Geolocation for IP: {test_ip} ===")
    result = test_geolocation(test_ip)

    # Print the result
    print("\n=== Geolocation Result ===")
    pprint(result)

    # Print a summary
    print("\n=== Summary ===")
    if result.get("city") or result.get("region") or result.get("country"):
        print(f"Location: {result.get('city') or 'Unknown city'}, "
              f"{result.get('region') or 'Unknown region'}, "
              f"{result.get('country') or 'Unknown country'}")
        print(f"Coordinates: {result.get('latitude')}, {result.get('longitude')}")
        print(f"Processing time: {result.get('processing_time'):.2f} seconds")
    else:
        print(f"No location data found for IP: {test_ip}")

    # Suggest some other IPs to test
    print("\n=== Try These Other IPs ===")
    print("*******         - Cloudflare DNS")
    print("*******         - Google DNS")
    print("**************  - OpenDNS")
    print("*************   - Norton ConnectSafe")
    print("*************** - CleanBrowsing")
    print("*********       - Yandex.DNS")

    print("\nUsage: python test_geolocation.py [ip_address]")

if __name__ == "__main__":
    main()
