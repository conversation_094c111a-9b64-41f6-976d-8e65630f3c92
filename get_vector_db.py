import os
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
import logging
import requests

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")
# Default to mxbai-embed-large:latest for consistency with app.py
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

# Fallback embedding models in order of preference
FALLBACK_EMBEDDING_MODELS = ["mxbai-embed-large:latest", "bge-m3:latest", "nomic-embed-text:latest"]

def check_embedding_model_availability(model_name):
    """Check if the specified embedding model is available in Ollama."""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            logger.warning(f"Ollama service returned status code: {response.status_code}")
            return False

        # Check if the embedding model is available
        models = response.json().get("models", [])
        model_names = [model.get("name") for model in models]

        if model_name in model_names:
            logger.info(f"Embedding model '{model_name}' is available")
            return True
        else:
            logger.warning(f"Embedding model '{model_name}' not found in available models: {', '.join(model_names)}")
            return False
    except Exception as e:
        logger.warning(f"Failed to check embedding model availability: {str(e)}")
        return False

# Cache for Chroma instances
_chroma_cache = {}

def get_vector_db(category: str) -> Chroma:
    """
    Get or create a Chroma vector database for the specified category.

    Args:
        category (str): The category name for the vector database.

    Returns:
        Chroma: The Chroma vector database instance.
    """
    try:
        # Return cached instance if available
        if category in _chroma_cache:
            logger.debug(f"Reusing cached Chroma DB for category: {category}")
            return _chroma_cache[category]

        # Try to initialize with the configured embedding model
        model_to_use = TEXT_EMBEDDING_MODEL

        # Check if the model is available
        if not check_embedding_model_availability(model_to_use):
            # Try fallback models if the primary one isn't available
            for fallback_model in FALLBACK_EMBEDDING_MODELS:
                if fallback_model != model_to_use and check_embedding_model_availability(fallback_model):
                    logger.info(f"Using fallback embedding model: {fallback_model}")
                    model_to_use = fallback_model
                    break

        logger.info(f"Initializing embedding function with model: {model_to_use}")
        try:
            embed_fn = OllamaEmbeddings(model=model_to_use)
            logger.info(f"Successfully initialized embedding function with model: {model_to_use}")
        except Exception as embed_error:
            logger.error(f"Failed to initialize embedding function with model {model_to_use}: {str(embed_error)}")
            # Last resort fallback to nomic-embed-text
            logger.info("Trying last resort fallback to nomic-embed-text:latest")
            try:
                embed_fn = OllamaEmbeddings(model="nomic-embed-text:latest")
                logger.info("Successfully initialized fallback embedding function with nomic-embed-text:latest")
            except Exception as fallback_error:
                logger.error(f"Failed to initialize fallback embedding model: {str(fallback_error)}")
                raise ValueError(f"Could not initialize any embedding model. Original error: {str(embed_error)}, Fallback error: {str(fallback_error)}")

        # Create the vector database
        persist_dir = os.path.join(CHROMA_PATH, category)
        os.makedirs(persist_dir, exist_ok=True)
        logger.info(f"Initializing Chroma vector DB for category: {category} at {persist_dir}")

        try:
            db = Chroma(
                collection_name=category,
                persist_directory=persist_dir,
                embedding_function=embed_fn
            )

            # Cache the instance
            _chroma_cache[category] = db
            return db

        except ValueError as ve:
            # Handle dimension mismatch error specifically
            error_msg = str(ve).lower()
            if "embedding dimension" in error_msg and "does not match collection dimensionality" in error_msg:
                # Extract dimensions from error message
                import re
                match = re.search(r'embedding dimension (\d+) does not match collection dimensionality (\d+)', error_msg)
                if match:
                    current_dim = match.group(1)
                    collection_dim = match.group(2)

                    detailed_error = (
                        f"Dimension mismatch error: Your current embedding model ({model_to_use}) "
                        f"produces {current_dim}-dimensional vectors, but the existing collection "
                        f"was created with {collection_dim}-dimensional vectors.\n\n"
                        f"To fix this issue, you have two options:\n"
                        f"1. Run 'python switch_embedding_model.py --auto' to automatically switch to a compatible model\n"
                        f"2. Run 'python reembed_documents.py' to re-embed all documents with your current model\n\n"
                        f"See the documentation for more details on these options."
                    )
                    logger.error(detailed_error)
                    raise ValueError(detailed_error) from ve

            # Re-raise the original error if it's not a dimension mismatch
            raise

    except Exception as e:
        logger.error(f"Failed to initialize vector DB for category {category}: {str(e)}")

        # Provide more detailed error information for common issues
        error_msg = str(e).lower()
        if "str object has no attribute get" in error_msg:
            logger.error("This appears to be an issue with model initialization. Check that Ollama is running and the embedding model is available.")
        elif "connection refused" in error_msg:
            logger.error("Could not connect to Ollama. Make sure the Ollama service is running.")
        elif "no such file or directory" in error_msg:
            logger.error(f"The directory for category {category} does not exist or is not accessible.")

        raise