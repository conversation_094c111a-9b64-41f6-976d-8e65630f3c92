# GeoIP Analytics

This feature adds an interactive map to the AI Analytics Dashboard to visualize the geographic distribution of users accessing your application. It tracks user locations based on their IP addresses and displays this data on a map with colored markers for different countries.

## Features

- **Interactive Map**: Displays user locations with colored markers in the AI Analytics Dashboard
- **Special Handling for Local Development**: Shows local development activity with a distinct marker
- **Country-Based Coloring**: Uses different colors for markers based on the user's country
- **Detailed Popups**: Shows city, region, country, and visitor count when clicking on markers
- **Map Legend**: Explains the different marker types and colors
- **Top Countries and Cities**: Lists the most common countries and cities of your users

## Setup Instructions

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```
# MaxMind GeoLite2 Web Service configuration
MAXMIND_ACCOUNT_ID=your_account_id
MAXMIND_LICENSE_KEY=your_license_key
GEOLOCATION_ENABLED=true

# Development mode settings (optional)
DEV_MODE=false
TEST_IP=*******  # Only used when DEV_MODE=true
```

### 2. MaxMind Account Setup

1. Sign up for a free GeoLite2 account at: https://www.maxmind.com/en/geolite2/signup
2. Generate a license key at: https://www.maxmind.com/en/accounts/current/license-key
3. Add your account ID and license key to the environment variables

### 3. Database Setup

The GeoIP analytics feature automatically creates the necessary database table (`geoip_analytics`) when the application starts.

## Usage

### Accessing the Geographic Distribution Map

1. Start the application: `python -m app`
2. Navigate to the admin dashboard: `http://localhost:8080/admin/dashboard`
3. Click on the "AI Analytics" card
4. The AI Analytics dashboard will be displayed with the Geographic Distribution of Visitors map

### Testing with Sample Data

To generate sample geolocation data for testing:

1. Run the test data generator: `python generate_test_geoip_data.py`
2. This will create 50 sample entries with various locations
3. Refresh the AI Analytics dashboard to see the data on the map

### Understanding the Map

- **Regular Markers**: Show real user locations with colors based on country
- **Gray Circle Marker**: Represents local development activity (localhost/127.0.0.1)
- **Popups**: Click on any marker to see details about that location
- **Legend**: Shows what each marker color represents

## How It Works

1. The application tracks user locations using the MaxMind GeoLite2 Web Service
2. When a user accesses a page, their IP address is used to determine their location
3. This location data is stored in the database along with device fingerprint and timestamp
4. The AI Analytics dashboard retrieves this data and displays it on an interactive map
5. Local development activity (localhost) is tracked separately with a special marker at Los Baños, Laguna, Philippines

## Customization

### Changing Marker Colors

Edit the `getColorForCountry` function in `templates/analytics.html` to change the colors assigned to different countries:

```javascript
const getColorForCountry = (country) => {
    const colors = ['#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#3498db', '#1abc9c', '#d35400'];
    // Add or change colors in the array above
    // ...
};
```

### Changing the Local Marker Position

The local development marker is currently set to appear at Los Baños, Laguna, Philippines. If you need to change this location, edit the `DEV_LOCATION` object in `geo_utils.py`:

```python
# Centralized development location configuration
DEV_LOCATION = {
    "city": "Los Baños",
    "region": "Laguna",
    "country": "Philippines",
    "country_display": "Philippines (Local Development)",
    "latitude": 14.1648,
    "longitude": 121.2413,
    "description": "Local Development"
}
```

### Adding More Sample Locations

Edit the `SAMPLE_LOCATIONS` array in `generate_test_geoip_data.py` to add more sample locations:

```python
SAMPLE_LOCATIONS = [
    # Add more locations here
    {
        "ip_address": "new_ip",
        "city": "City Name",
        "region": "Region",
        "country": "Country",
        "latitude": 0.0,
        "longitude": 0.0
    },
    # ...
]
```

## Troubleshooting

### Map Shows No Markers

- Check that `GEOLOCATION_ENABLED=true` in your environment variables
- Verify that your MaxMind credentials are correct
- Check the browser console for any JavaScript errors
- Try generating sample data with `python generate_test_geoip_data.py`

### MaxMind API Errors

- Free accounts have a limit of 1000 requests per day
- The implementation includes caching to reduce API calls
- Check your MaxMind account dashboard for usage statistics

### Local Development Not Showing

- Local development entries (localhost/127.0.0.1) are handled specially
- They appear as a special marker at Los Baños, Laguna, Philippines
- If not showing, try accessing the application from localhost

## Privacy Considerations

- IP geolocation data is approximate and not 100% accurate
- The application stores IP addresses and approximate locations
- Consider adding a privacy notice to inform users about location tracking
- MaxMind's GeoLite2 database provides city-level accuracy, not exact addresses
