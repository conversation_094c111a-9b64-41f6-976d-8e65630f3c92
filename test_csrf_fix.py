#!/usr/bin/env python3
"""
Test script to verify CSRF token functionality and expiration handling.

This script tests:
1. CSRF token generation endpoint
2. CSRF token expiration handling
3. Automatic token refresh mechanism
"""

import requests
import time
import json
from bs4 import BeautifulSoup

def test_csrf_functionality(base_url="http://localhost:8080"):
    """Test CSRF token functionality."""
    
    print("Testing CSRF Token Functionality")
    print("=" * 50)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # 1. Test getting the main page and extracting CSRF token
        print("1. Getting main page and extracting CSRF token...")
        response = session.get(base_url)
        if response.status_code != 200:
            print(f"❌ Failed to get main page: {response.status_code}")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_meta = soup.find('meta', {'name': 'csrf-token'})
        
        if not csrf_meta:
            print("❌ CSRF token meta tag not found in page")
            return False
            
        initial_token = csrf_meta.get('content')
        print(f"✅ Initial CSRF token extracted: {initial_token[:20]}...")
        
        # 2. Test CSRF token refresh endpoint
        print("\n2. Testing CSRF token refresh endpoint...")
        refresh_response = session.get(f"{base_url}/api/csrf-token")
        
        if refresh_response.status_code != 200:
            print(f"❌ CSRF refresh endpoint failed: {refresh_response.status_code}")
            return False
            
        refresh_data = refresh_response.json()
        if 'csrf_token' not in refresh_data:
            print("❌ CSRF token not found in refresh response")
            return False
            
        new_token = refresh_data['csrf_token']
        print(f"✅ New CSRF token received: {new_token[:20]}...")
        
        # 3. Verify tokens are different (they should be)
        if initial_token == new_token:
            print("⚠️  Warning: New token is same as initial token")
        else:
            print("✅ New token is different from initial token")
        
        # 4. Test making a request with the new token
        print("\n3. Testing request with new CSRF token...")
        
        # Try to get categories (this should work with valid token)
        categories_response = session.get(f"{base_url}/api/categories")
        if categories_response.status_code == 200:
            print("✅ API request successful with valid token")
        else:
            print(f"❌ API request failed: {categories_response.status_code}")
            
        # 5. Test with invalid token (simulate expired token)
        print("\n4. Testing request with invalid CSRF token...")
        
        invalid_headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': 'invalid_token_12345'
        }
        
        # Try to make a POST request with invalid token
        test_data = {'test': 'data'}
        invalid_response = session.post(
            f"{base_url}/api/csrf-token",  # This endpoint should reject invalid tokens
            headers=invalid_headers,
            json=test_data
        )
        
        if invalid_response.status_code == 400:
            print("✅ Invalid CSRF token properly rejected")
        else:
            print(f"⚠️  Unexpected response for invalid token: {invalid_response.status_code}")
        
        print("\n" + "=" * 50)
        print("✅ CSRF functionality test completed successfully!")
        return True
        
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {base_url}")
        print("Make sure the Flask application is running on port 8080")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def test_csrf_configuration():
    """Test CSRF configuration values."""
    
    print("\nTesting CSRF Configuration")
    print("=" * 30)
    
    try:
        import os
        from app import app
        
        # Check CSRF configuration
        csrf_enabled = app.config.get('WTF_CSRF_ENABLED', False)
        csrf_time_limit = app.config.get('WTF_CSRF_TIME_LIMIT', 0)
        csrf_ssl_strict = app.config.get('WTF_CSRF_SSL_STRICT', False)
        
        print(f"CSRF Enabled: {csrf_enabled}")
        print(f"CSRF Time Limit: {csrf_time_limit} seconds ({csrf_time_limit/3600:.1f} hours)")
        print(f"CSRF SSL Strict: {csrf_ssl_strict}")
        
        if csrf_enabled and csrf_time_limit >= 7200:
            print("✅ CSRF configuration looks good!")
            return True
        else:
            print("❌ CSRF configuration needs adjustment")
            return False
            
    except ImportError:
        print("⚠️  Could not import app module for configuration check")
        return False
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("CSRF Token Fix Verification Test")
    print("=" * 40)
    
    # Test configuration first
    config_ok = test_csrf_configuration()
    
    # Test functionality
    functionality_ok = test_csrf_functionality()
    
    print("\n" + "=" * 40)
    print("FINAL RESULTS:")
    print(f"Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"Functionality: {'✅ PASS' if functionality_ok else '❌ FAIL'}")
    
    if config_ok and functionality_ok:
        print("\n🎉 All tests passed! CSRF fix is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration and implementation.")
