# Technical Architecture Documentation

## System Architecture Overview

The Document Management System follows a modular, layered architecture designed for scalability, maintainability, and extensibility. The system is built using modern web technologies with AI integration at its core.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Frontend Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  Bootstrap 5 UI │ JavaScript Utils │ Jinja2 Templates │ CSS     │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Application Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  Flask Routes │ User Management │ Authentication │ Permissions   │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                       Business Logic Layer                      │
├─────────────────────────────────────────────────────────────────┤
│  Query Processing │ Document Processing │ AI Integration        │
│  Analytics Engine │ Session Management  │ Configuration Mgmt   │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Data Layer                               │
├─────────────────────────────────────────────────────────────────┤
│  SQLite DBs │ ChromaDB │ File System │ Temporary Storage       │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      External Services                          │
├─────────────────────────────────────────────────────────────────┤
│  Ollama AI Models │ MaxMind GeoIP │ Web Scraping APIs          │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Flask Application Server (`app.py`)

**Primary Responsibilities:**
- HTTP request handling and routing
- Session management and authentication
- API endpoint definitions
- Template rendering and response generation

**Key Features:**
- CSRF protection with Flask-WTF
- Rate limiting with Flask-Limiter
- File upload handling (25MB limit)
- Device fingerprinting for session tracking
- Geolocation tracking middleware

**Configuration:**
- Port 8080 (configurable via environment)
- Secret key management
- Maximum content length controls
- Temporary folder management

### 2. Database Architecture

#### A. User Management Database (`user_management.db`)
```sql
-- Core user table
users (
    user_id INTEGER PRIMARY KEY,
    username TEXT UNIQUE,
    password_hash TEXT,
    email TEXT UNIQUE,
    role TEXT CHECK(role IN ('admin', 'editor', 'viewer')),
    group_id INTEGER,
    account_status TEXT,
    created_at TIMESTAMP,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER,
    password_changed_at TIMESTAMP,
    profile_picture TEXT,
    full_name TEXT
)

-- Permission management
permission_groups (
    group_id INTEGER PRIMARY KEY,
    name TEXT UNIQUE,
    description TEXT,
    permissions TEXT -- JSON
)

permission_overrides (
    user_id INTEGER,
    function_name TEXT,
    enabled BOOLEAN
)

dashboard_permissions (
    user_id INTEGER,
    function_name TEXT,
    enabled BOOLEAN
)

-- Session tracking
user_sessions (
    session_id INTEGER PRIMARY KEY,
    user_id INTEGER,
    session_token TEXT UNIQUE,
    device_fingerprint TEXT,
    ip_address TEXT,
    user_agent TEXT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    is_active BOOLEAN
)
```

#### B. Chat History Database (`chat_history.db`)
```sql
-- Main chat interactions
chat_history (
    id INTEGER PRIMARY KEY,
    category TEXT,
    question TEXT,
    answer TEXT,
    sources TEXT, -- JSON
    timestamp DATETIME,
    images TEXT, -- JSON
    pdf_links TEXT, -- JSON
    metadata TEXT, -- JSON
    url_images TEXT, -- JSON
    pdf_images TEXT, -- JSON
    document_thumbnails TEXT, -- JSON
    client_name TEXT,
    session_id TEXT,
    session_start DATETIME,
    device_fingerprint TEXT
)

-- Analytics data
chat_analytics (
    id INTEGER PRIMARY KEY,
    session_id TEXT,
    chat_id INTEGER,
    category TEXT,
    client_name TEXT,
    question_length INTEGER,
    answer_length INTEGER,
    processing_time REAL,
    source_count INTEGER,
    image_count INTEGER,
    token_count INTEGER,
    model_name TEXT,
    embedding_model TEXT,
    vision_model TEXT,
    vision_enabled BOOLEAN,
    images_filtered INTEGER,
    total_images_extracted INTEGER,
    filter_sensitivity TEXT,
    hallucination_detected BOOLEAN,
    device_fingerprint TEXT,
    ip_address TEXT,
    city TEXT,
    region TEXT,
    country TEXT,
    latitude REAL,
    longitude REAL,
    timestamp DATETIME
)
```

#### C. Content Database (`content_db.sqlite`)
```sql
-- URL source management
source_urls (
    id INTEGER PRIMARY KEY,
    url TEXT UNIQUE,
    title TEXT,
    description TEXT,
    last_scraped TIMESTAMP,
    last_updated TIMESTAMP,
    status TEXT CHECK(status IN ('active', 'archived', 'error')),
    error_message TEXT,
    created_at TIMESTAMP
)

-- PDF document tracking
pdf_documents (
    id INTEGER PRIMARY KEY,
    filename TEXT,
    original_filename TEXT,
    category TEXT,
    upload_date TIMESTAMP,
    source_url_id INTEGER,
    created_at TIMESTAMP
)

-- URL content storage
url_content (
    id INTEGER PRIMARY KEY,
    source_url_id INTEGER,
    content_type TEXT CHECK(content_type IN ('text', 'image', 'link')),
    content TEXT,
    content_order INTEGER,
    metadata TEXT,
    created_at TIMESTAMP
)

-- Cover image management
cover_images (
    id INTEGER PRIMARY KEY,
    pdf_document_id INTEGER,
    image_path TEXT,
    image_url TEXT,
    source TEXT CHECK(source IN ('pdf_first_page', 'pdf_internal', 'url', 'default')),
    description TEXT,
    created_at TIMESTAMP
)

-- Greeting system
greeting_templates (
    id INTEGER PRIMARY KEY,
    template_type TEXT CHECK(template_type IN ('welcome', 'response', 'return_user', 'time_based')),
    greeting_text TEXT,
    context_conditions TEXT, -- JSON
    is_active BOOLEAN,
    weight INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

### 3. AI Model Integration

#### A. Language Models
**Primary Models:**
- **Llama 3.1 8B Instruct**: Default conversational AI model
- **Gemma 3 1B/4B**: Alternative lightweight models
- **Model Fallback**: Automatic fallback to stable models on failure

**Configuration:**
```python
# Model parameters
temperature: 0.7          # Response creativity
num_ctx: 4096            # Context window size
num_predict: 256         # Maximum response tokens
top_p: 0.9              # Nucleus sampling
top_k: 40               # Top-k sampling
repeat_penalty: 1.1     # Repetition penalty
```

#### B. Vision Models
**Supported Models:**
- **Llama 3.2 Vision 11B**: Primary vision analysis model
- **Gemma 3 4B/12B**: Alternative vision capabilities

**Vision Processing Pipeline:**
1. Image encoding to base64
2. Context-aware prompt generation
3. Vision model analysis
4. Response filtering and formatting
5. Relevance scoring and filtering

#### C. Embedding Models
**Primary Model:** `mxbai-embed-large:latest`
**Features:**
- High-dimensional vector representations
- Semantic similarity search
- Batch processing capabilities
- Configurable chunk sizes and overlap

### 4. Vector Database (ChromaDB)

**Architecture:**
```
chroma/
├── {category}/
│   ├── chroma.sqlite3
│   └── {collection_id}/
│       ├── data_level0.bin
│       ├── header.bin
│       ├── length.bin
│       └── link_lists.bin
```

**Features:**
- Category-based collections
- Metadata filtering
- Similarity search with configurable thresholds
- Automatic persistence
- Scalable storage

### 5. Document Processing Pipeline

#### A. PDF Processing (`pdf_processor.py`, `db_pdf_processor.py`)
```python
# Processing stages
1. PDF Upload & Validation
2. Text Extraction (PyMuPDF)
3. Image Extraction & Analysis
4. Table Detection (Camelot/Tabula)
5. OCR Processing (Tesseract)
6. Chunking & Embedding
7. Vector Storage
8. Metadata Indexing
```

#### B. Web Content Processing (`embed.py`, `db_embed.py`)
```python
# Scraping pipeline
1. URL Validation
2. Content Extraction (BeautifulSoup)
3. Image & Link Discovery
4. Depth-based Crawling
5. Content Cleaning & Formatting
6. Embedding Generation
7. Database Storage
```

### 6. Configuration Management

#### A. Query Configuration (`config/query_config.py`)
```python
@dataclass
class QueryConfiguration:
    # Document Retrieval
    retrieval_k: int = 12
    relevance_threshold: float = 0.15
    min_documents: int = 3
    max_documents: int = 8
    
    # Display Limits
    max_pdf_images_display: int = 5
    max_url_images_display: int = 5
    max_tables_display: int = 3
    
    # Hallucination Detection
    hallucination_threshold_strict: float = 0.6
    hallucination_threshold_balanced: float = 0.4
    enable_hallucination_detection: bool = True
```

#### B. Model Configuration (`default_models.json`)
```json
{
  "llm_model": "llama3.1:8b-instruct-q4_K_M",
  "embedding_model": "mxbai-embed-large:latest",
  "vision_model": "llama3.2-vision:11b-instruct-q4_K_M",
  "use_vision_model": true,
  "filter_pdf_images": true,
  "filter_sensitivity": "medium",
  "model_parameters": {
    "temperature": 0.7,
    "num_ctx": 4096,
    "system_prompt": "You are a helpful assistant for ERDB..."
  }
}
```

## Security Architecture

### 1. Authentication & Authorization
- **Flask-Login**: Session-based authentication
- **bcrypt**: Password hashing with salt
- **CSRF Protection**: Token-based CSRF prevention
- **Rate Limiting**: Request throttling per IP/user
- **Session Security**: Secure session management with device fingerprinting

### 2. Permission System
- **Role-Based Access Control (RBAC)**: Admin, Editor, Viewer roles
- **Function-Level Permissions**: Granular access control
- **Permission Groups**: Hierarchical permission inheritance
- **Permission Overrides**: Individual user permission customization
- **Audit Logging**: Comprehensive activity tracking

### 3. Data Security
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries
- **File Upload Security**: Type validation and size limits
- **Path Traversal Protection**: Secure file handling
- **XSS Prevention**: Template escaping and CSP headers

## Performance Considerations

### 1. Database Optimization
- **Indexing Strategy**: Optimized indexes for frequent queries
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Efficient SQL queries with proper joins
- **Batch Processing**: Bulk operations for large datasets

### 2. Caching Strategy
- **Vision Model Caching**: Cached image analysis results
- **Session Caching**: In-memory session data
- **Static Asset Caching**: Browser caching for static resources
- **Query Result Caching**: Optional response caching

### 3. Scalability Features
- **Modular Architecture**: Loosely coupled components
- **Async Processing**: Background task processing capabilities
- **Resource Management**: Configurable resource limits
- **Load Distribution**: Stateless design for horizontal scaling

This technical architecture provides a robust foundation for the Document Management System, ensuring scalability, security, and maintainability while supporting advanced AI-powered features.
