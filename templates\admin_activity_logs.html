{% extends "admin_base.html" %}

{% block title %}User Activity Logs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">User Activity Logs</h1>
            <p class="text-muted">Track all user activities in the system.</p>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Filter Logs</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{{ url_for('user.admin_activity_logs') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="user_id">User</label>
                                    <select class="form-control" id="user_id" name="user_id">
                                        <option value="">All Users</option>
                                        {% for u in users if u %}
                                        <option value="{{ u.user_id }}" {% if user_id == u.user_id %}selected{% endif %}>
                                            {{ u.username }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="action_type">Action Type</label>
                                    <select class="form-control" id="action_type" name="action_type">
                                        <option value="">All Actions</option>
                                        {% for type in action_types %}
                                        <option value="{{ type }}" {% if action_type == type %}selected{% endif %}>
                                            {{ type|replace('_', ' ')|title }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <div class="form-group mb-0">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Apply Filters
                                    </button>
                                    <a href="{{ url_for('user.admin_activity_logs') }}" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Activity Logs</h5>
                </div>
                <div class="card-body">
                    {% if logs %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>Action Type</th>
                                    <th>Details</th>
                                    <th>Resource Type</th>
                                    <th>Resource ID</th>
                                    <th>Status</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.timestamp }}</td>
                                    <td>
                                        {% if log.username %}
                                        <a href="{{ url_for('user.admin_user_details', user_id=log.user_id) }}">
                                            {{ log.username }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ log.action_type|replace('_', ' ')|title }}
                                        </span>
                                    </td>
                                    <td>{{ log.details }}</td>
                                    <td>{{ log.resource_type }}</td>
                                    <td>{{ log.resource_id }}</td>
                                    <td>
                                        {% if log.status == 'success' %}
                                        <span class="badge badge-success">Success</span>
                                        {% elif log.status == 'error' %}
                                        <span class="badge badge-danger">Error</span>
                                        {% elif log.status == 'warning' %}
                                        <span class="badge badge-warning">Warning</span>
                                        {% else %}
                                        <span class="badge badge-info">{{ log.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.ip_address }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mt-4">
                            {% if page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user.admin_activity_logs', page=page-1, user_id=user_id, action_type=action_type) }}">
                                    Previous
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            {% endif %}
                            
                            {% for p in range(1, pages + 1) %}
                            <li class="page-item {% if p == page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('user.admin_activity_logs', page=p, user_id=user_id, action_type=action_type) }}">
                                    {{ p }}
                                </a>
                            </li>
                            {% endfor %}
                            
                            {% if page < pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user.admin_activity_logs', page=page+1, user_id=user_id, action_type=action_type) }}">
                                    Next
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No activity logs found.
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted">
                    Showing {{ logs|length }} of {{ total }} logs
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
