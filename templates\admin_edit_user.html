{% extends "admin_base.html" %}

{% block title %}Edit User{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Edit User</h1>
            <div class="flex space-x-4">
                <a href="{{ url_for('user.admin_user_details', user_id=user.user_id) }}" class="text-blue-600 hover:underline">&larr; Back to User Details</a>
                <a href="{{ url_for('user.admin_users') }}" class="text-blue-600 hover:underline">All Users</a>
            </div>
        </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- User Profile Sidebar -->
                <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <div class="flex flex-col items-center text-center">
                        {% if user.profile_picture %}
                            <img src="{{ url_for('static', filename=user.profile_picture) }}" alt="Profile Picture" class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-md">
                        {% else %}
                            <div class="w-32 h-32 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 text-4xl font-bold border-4 border-white shadow-md">
                                {{ user.username[0].upper() }}
                            </div>
                        {% endif %}
                        <h2 class="mt-4 text-xl font-semibold">{{ user.full_name or user.username }}</h2>
                        <p class="text-gray-500">{{ user.email }}</p>

                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user.role == 'admin' %}bg-purple-100 text-purple-800
                                {% elif user.role == 'editor' %}bg-blue-100 text-blue-800
                                {% else %}bg-green-100 text-green-800{% endif %}">
                                {{ user.role.capitalize() }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user.account_status == 'active' %}bg-green-100 text-green-800
                                {% elif user.account_status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif user.account_status == 'locked' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ user.account_status.capitalize() }}
                            </span>
                        </div>
                    </div>

                    <div class="mt-6 border-t border-gray-200 pt-4">
                        <h3 class="text-lg font-semibold mb-2">Account Information</h3>
                        <ul class="space-y-2">
                            <li class="flex justify-between">
                                <span class="text-gray-600">Username:</span>
                                <span class="font-medium">{{ user.username }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">User ID:</span>
                                <span class="font-medium">{{ user.user_id }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span class="font-medium">{{ user.created_at.split('T')[0] if user.created_at else 'N/A' }}</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Edit User Form -->
                <div class="md:col-span-2">
                    <div class="bg-white p-6 rounded-lg border border-gray-200">
                        <h3 class="text-lg font-semibold mb-4">Edit User Information</h3>

                        <form method="POST" action="{{ url_for('user.admin_edit_user', user_id=user.user_id) }}">
                            {{ form.csrf_token }}

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label for="username" class="block text-gray-700 text-sm font-bold mb-2">Username</label>
                                    {{ form.username(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100", readonly=True) }}
                                    {% if form.username.errors %}
                                        <p class="text-red-500 text-xs italic mt-1">{{ form.username.errors[0] }}</p>
                                    {% endif %}
                                    <p class="text-gray-500 text-xs mt-1">Username cannot be changed</p>
                                </div>

                                <div>
                                    <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                                    {{ form.email(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                    {% if form.email.errors %}
                                        <p class="text-red-500 text-xs italic mt-1">{{ form.email.errors[0] }}</p>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="full_name" class="block text-gray-700 text-sm font-bold mb-2">Full Name</label>
                                {{ form.full_name(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                {% if form.full_name.errors %}
                                    <p class="text-red-500 text-xs italic mt-1">{{ form.full_name.errors[0] }}</p>
                                {% endif %}
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label for="role" class="block text-gray-700 text-sm font-bold mb-2">Role</label>
                                    {{ form.role(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline", onchange="showRoleGroupInfo(this.value)") }}
                                    {% if form.role.errors %}
                                        <p class="text-red-500 text-xs italic mt-1">{{ form.role.errors[0] }}</p>
                                    {% endif %}
                                    <p id="roleGroupInfo" class="text-gray-500 text-xs mt-1 hidden"></p>
                                </div>

                                <div>
                                    <label for="account_status" class="block text-gray-700 text-sm font-bold mb-2">Account Status</label>
                                    {{ form.account_status(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                    {% if form.account_status.errors %}
                                        <p class="text-red-500 text-xs italic mt-1">{{ form.account_status.errors[0] }}</p>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-4 border-t border-gray-200 pt-4">
                                <h4 class="text-md font-semibold mb-2">Change Password (Optional)</h4>
                                <p class="text-gray-500 text-sm mb-4">Leave blank to keep current password</p>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="password" class="block text-gray-700 text-sm font-bold mb-2">New Password</label>
                                        {{ form.password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                        {% if form.password.errors %}
                                            <p class="text-red-500 text-xs italic mt-1">{{ form.password.errors[0] }}</p>
                                        {% endif %}
                                        <p class="text-gray-500 text-xs mt-1">Minimum 8 characters with mixed case, numbers, and symbols</p>
                                    </div>

                                    <div>
                                        <label for="confirm_password" class="block text-gray-700 text-sm font-bold mb-2">Confirm Password</label>
                                        {{ form.confirm_password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                                        {% if form.confirm_password.errors %}
                                            <p class="text-red-500 text-xs italic mt-1">{{ form.confirm_password.errors[0] }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-between mt-6">
                                <a href="{{ url_for('user.admin_user_details', user_id=user.user_id) }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md text-center transition-colors">
                                    Cancel
                                </a>
                                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md text-center transition-colors">
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block scripts %}
    <script>
        // Function to show information about group assignment based on role
        function showRoleGroupInfo(role) {
            const infoElement = document.getElementById('roleGroupInfo');

            if (role === 'editor') {
                infoElement.textContent = 'User will be automatically assigned to the Editor Group with all Editor permissions.';
                infoElement.classList.remove('hidden');
                infoElement.classList.add('text-blue-600');
            } else if (role === 'viewer') {
                infoElement.textContent = 'User will be automatically assigned to the Viewer Group with all Viewer permissions.';
                infoElement.classList.remove('hidden');
                infoElement.classList.add('text-green-600');
            } else {
                infoElement.classList.add('hidden');
            }
        }

        // Show info on page load based on current selection
        document.addEventListener('DOMContentLoaded', function() {
            const roleSelect = document.querySelector('select[name="role"]');
            if (roleSelect) {
                showRoleGroupInfo(roleSelect.value);
            }
        });
    </script>
{% endblock %}
