import os
import logging
import requests
import json
import time
from datetime import datetime
import shutil
from bs4 import BeautifulSoup
import fitz  # PyMuPDF

# Import database utilities
import db_content_utils as db
from pdf_processor import (
    process_pdf as original_process_pdf,
    extract_cover_image_from_pdf,
    extract_images_from_pdf,
    extract_links_from_pdf
)
from embed import scrape_single_url, extract_images_and_links_from_html
from create_temp_dirs import create_pdf_directory_structure

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")

def process_pdf_db_first(pdf_path, category=None, source_url=None, extract_tables=True, save_images=True, save_tables=True,
                      use_vision=None, filter_sensitivity=None, max_images=None, force_update=False):
    """
    Process a PDF file with database-first retrieval approach.

    This function checks if source URL content exists in the database before scraping.
    It prioritizes database queries over network requests for better performance.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing extracted content
        source_url: Original URL where the PDF was obtained
        extract_tables: Whether to extract tables
        save_images: Whether to save extracted images to disk
        save_tables: Whether to save extracted tables to disk
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images
        max_images: Maximum number of images to save
        force_update: Force update of URL content even if it's fresh

    Returns:
        Dictionary containing extracted content and metadata
    """
    pdf_name = os.path.basename(pdf_path)
    pdf_base_name = os.path.splitext(pdf_name)[0]
    original_filename = pdf_base_name.split('_', 1)[1] if '_' in pdf_base_name else pdf_base_name

    # Initialize result structure
    result = {
        "text": [],
        "images": [],
        "tables": [],
        "links": [],
        "metadata": {
            "filename": pdf_name,
            "category": category,
            "source_url": source_url,
            "extraction_date": datetime.now().isoformat(),
            "database_retrieval": False
        }
    }

    # Process source URL if provided
    source_url_id = None
    url_content_retrieved = False

    if source_url:
        logger.info(f"Processing PDF {pdf_name} with source URL: {source_url}")

        # Check if URL exists in database
        url_record = db.get_source_url_by_url(source_url)

        # Determine if we should use database content or scrape
        if url_record and db.is_url_content_fresh(url_record) and not force_update:
            logger.info(f"Using fresh database content for URL: {source_url}")
            source_url_id = url_record['id']

            # Get URL content from database
            url_images = db.get_url_images(source_url_id)
            url_links = db.get_url_links(source_url_id)

            # Add to result
            result["metadata"]["database_retrieval"] = True
            result["metadata"]["url_last_scraped"] = url_record['last_scraped']

            # Convert database records to the format expected by the rest of the system
            for img in url_images:
                # Make sure we're using the actual URL string, not a dictionary
                img_url = img['url']
                if isinstance(img_url, str):
                    result["images"].append({
                        "url": img_url,
                        "source": "url",
                        "metadata": img['metadata']
                    })
                else:
                    logger.warning(f"Skipping invalid image URL: {img_url}")

            for link in url_links:
                # Make sure we're using the actual URL string, not a dictionary
                link_url = link['url']
                if isinstance(link_url, str):
                    result["links"].append({
                        "url": link_url,
                        "source": "url",
                        "metadata": link['metadata']
                    })
                else:
                    logger.warning(f"Skipping invalid link URL: {link_url}")

            url_content_retrieved = True
        else:
            # Need to scrape the URL
            logger.info(f"Scraping URL content for: {source_url}")
            try:
                # Scrape the URL
                scraped_data = scrape_single_url(source_url)

                if scraped_data and not scraped_data.get("error"):
                    # Insert or update source URL record
                    title = scraped_data.get("title", "")
                    description = scraped_data.get("description", "")
                    source_url_id = db.insert_source_url(
                        source_url,
                        title=title,
                        description=description,
                        status='active'
                    )

                    if source_url_id:
                        # Store text content
                        text_content = scraped_data.get("text", "")
                        if text_content:
                            # Split text into manageable chunks if it's very large
                            max_chunk_size = 10000  # Characters per chunk
                            if len(text_content) > max_chunk_size:
                                chunks = [text_content[i:i+max_chunk_size]
                                         for i in range(0, len(text_content), max_chunk_size)]
                                for i, chunk in enumerate(chunks):
                                    db.insert_url_content(source_url_id, 'text', chunk, i)
                            else:
                                db.insert_url_content(source_url_id, 'text', text_content, 0)

                        # Store images
                        for i, img_url in enumerate(scraped_data.get("images", [])):
                            # Validate that img_url is a string, not a dictionary
                            if isinstance(img_url, dict):
                                if 'url' in img_url:
                                    actual_url = img_url['url']
                                    logger.warning(f"Found dictionary instead of URL string for image, extracting URL: {actual_url}")
                                    img_url = actual_url
                                else:
                                    logger.error(f"Invalid image data (dictionary without 'url' key): {img_url}")
                                    continue
                            elif not isinstance(img_url, str):
                                logger.error(f"Invalid image URL type: {type(img_url)} - {img_url}")
                                continue

                            # Ensure it's a valid URL
                            if not img_url.startswith(('http://', 'https://')):
                                logger.warning(f"Skipping invalid image URL (missing protocol): {img_url}")
                                continue

                            metadata = {"index": i, "source": "url_scrape"}
                            db.insert_url_content(source_url_id, 'image', img_url, i, metadata)

                            # Add to result
                            result["images"].append({
                                "url": img_url,
                                "source": "url",
                                "metadata": metadata
                            })

                        # Store links
                        for i, link_url in enumerate(scraped_data.get("links", [])):
                            # Validate that link_url is a string, not a dictionary
                            if isinstance(link_url, dict):
                                if 'url' in link_url:
                                    actual_url = link_url['url']
                                    logger.warning(f"Found dictionary instead of URL string for link, extracting URL: {actual_url}")
                                    link_url = actual_url
                                else:
                                    logger.error(f"Invalid link data (dictionary without 'url' key): {link_url}")
                                    continue
                            elif not isinstance(link_url, str):
                                logger.error(f"Invalid link URL type: {type(link_url)} - {link_url}")
                                continue

                            # Ensure it's a valid URL
                            if not link_url.startswith(('http://', 'https://')):
                                logger.warning(f"Skipping invalid link URL (missing protocol): {link_url}")
                                continue

                            metadata = {"index": i, "source": "url_scrape"}
                            db.insert_url_content(source_url_id, 'link', link_url, i, metadata)

                            # Add to result
                            result["links"].append({
                                "url": link_url,
                                "source": "url",
                                "metadata": metadata
                            })

                        url_content_retrieved = True
                    else:
                        logger.error(f"Failed to insert source URL record for {source_url}")
                else:
                    error_msg = scraped_data.get("error", "Unknown error")
                    logger.error(f"Failed to scrape URL {source_url}: {error_msg}")

                    # Record the error in the database
                    source_url_id = db.insert_source_url(
                        source_url,
                        status='error',
                        error_message=error_msg
                    )
            except Exception as e:
                logger.error(f"Error processing URL {source_url}: {str(e)}")

                # Record the error in the database
                source_url_id = db.insert_source_url(
                    source_url,
                    status='error',
                    error_message=str(e)
                )

    # Process the PDF file using the original function
    pdf_result = original_process_pdf(
        pdf_path,
        category,
        source_url,
        extract_tables=extract_tables,
        save_images=save_images,
        save_tables=save_tables,
        use_vision=use_vision,
        filter_sensitivity=filter_sensitivity,
        max_images=max_images
    )

    # Merge PDF processing results
    result["text"] = pdf_result["text"]

    # Only add PDF images if they weren't already added from URL
    if not url_content_retrieved or not result["images"]:
        result["images"] = pdf_result["images"]

    result["tables"] = pdf_result["tables"]

    # Merge links, avoiding duplicates
    pdf_links = pdf_result["links"]
    if pdf_links:
        existing_urls = {link["url"] for link in result["links"]}
        for link in pdf_links:
            if link["url"] not in existing_urls:
                result["links"].append(link)
                existing_urls.add(link["url"])

    # Update metadata
    result["metadata"].update(pdf_result["metadata"])
    result["metadata"]["source_url_id"] = source_url_id

    # Associate PDF with source URL in the database
    if category:
        pdf_id = db.associate_pdf_with_url(pdf_name, category, original_filename, source_url_id)
        result["metadata"]["pdf_document_id"] = pdf_id

        # Process cover image
        if pdf_id and save_images:
            # Check if cover image already exists in database
            cover_image = db.get_cover_image_for_pdf(pdf_id)

            if not cover_image:
                # Try to extract cover image from PDF first
                thumbnail_info = extract_cover_image_from_pdf(pdf_path, category)

                if thumbnail_info:
                    # Save cover image to database
                    cover_id = db.insert_cover_image(
                        pdf_id,
                        thumbnail_info["path"],
                        thumbnail_info["url"],
                        thumbnail_info["source"],
                        thumbnail_info["description"]
                    )

                    if cover_id:
                        logger.info(f"Saved cover image for PDF {pdf_name} from PDF first page")
                        result["metadata"]["cover_image_id"] = cover_id
                        result["metadata"]["thumbnail_path"] = thumbnail_info["path"]
                        result["metadata"]["thumbnail_url"] = thumbnail_info["url"]
                        result["metadata"]["thumbnail_source"] = thumbnail_info["source"]
                elif source_url_id:
                    # If PDF extraction failed, try to get an image from the database
                    url_images = db.get_url_images(source_url_id, limit=1)

                    if url_images:
                        # Create directory structure for the cover image
                        dir_structure = create_pdf_directory_structure(category, pdf_name)
                        cover_image_dir = os.path.join(dir_structure["pdf_images_dir"], "cover_image")
                        os.makedirs(cover_image_dir, exist_ok=True)

                        # Generate thumbnail filename
                        thumbnail_filename = f"{pdf_base_name}_url_thumbnail.jpg"
                        thumbnail_path = os.path.join(cover_image_dir, thumbnail_filename)

                        try:
                            # Download and save the image
                            img_url = url_images[0]['url']
                            img_response = requests.get(img_url, timeout=10)

                            if img_response.status_code == 200:
                                with open(thumbnail_path, 'wb') as f:
                                    f.write(img_response.content)

                                # Create thumbnail info
                                thumbnail_info = {
                                    "source": "url",
                                    "path": thumbnail_path,
                                    "url": f"/{category}/{pdf_base_name}/pdf_images/cover_image/{thumbnail_filename}",
                                    "filename": thumbnail_filename,
                                    "description": f"Cover image from URL {source_url}"
                                }

                                # Save cover image to database
                                cover_id = db.insert_cover_image(
                                    pdf_id,
                                    thumbnail_path,
                                    thumbnail_info["url"],
                                    "url",
                                    thumbnail_info["description"]
                                )

                                if cover_id:
                                    logger.info(f"Saved cover image for PDF {pdf_name} from URL")
                                    result["metadata"]["cover_image_id"] = cover_id
                                    result["metadata"]["thumbnail_path"] = thumbnail_path
                                    result["metadata"]["thumbnail_url"] = thumbnail_info["url"]
                                    result["metadata"]["thumbnail_source"] = "url"
                        except Exception as e:
                            logger.error(f"Failed to download URL image for cover: {str(e)}")

    return result
