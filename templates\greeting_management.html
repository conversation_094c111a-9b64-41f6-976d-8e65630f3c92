{% extends "admin_base.html" %}

{% block title %}Greeting Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-comments me-2"></i>Greeting Management
                    </h4>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGreetingModal">
                        <i class="fas fa-plus me-1"></i>Add New Greeting
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <p class="text-muted mb-4">
                                Manage greeting templates used throughout the system. Greetings are selected randomly based on their weight and context.
                            </p>
                        </div>
                    </div>

                    <!-- Greeting Templates Tabs -->
                    <ul class="nav nav-tabs" id="greetingTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="welcome-tab" data-bs-toggle="tab" data-bs-target="#welcome" type="button" role="tab">
                                <i class="fas fa-hand-wave me-1"></i>Welcome ({{ grouped_templates.welcome|length }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="response-tab" data-bs-toggle="tab" data-bs-target="#response" type="button" role="tab">
                                <i class="fas fa-reply me-1"></i>Response ({{ grouped_templates.response|length }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="return-user-tab" data-bs-toggle="tab" data-bs-target="#return-user" type="button" role="tab">
                                <i class="fas fa-user-check me-1"></i>Return User ({{ grouped_templates.return_user|length }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="time-based-tab" data-bs-toggle="tab" data-bs-target="#time-based" type="button" role="tab">
                                <i class="fas fa-clock me-1"></i>Time-based ({{ grouped_templates.time_based|length }})
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content mt-3" id="greetingTabContent">
                        <!-- Welcome Greetings -->
                        <div class="tab-pane fade show active" id="welcome" role="tabpanel">
                            <div class="greeting-templates-container" data-type="welcome">
                                {% for template in grouped_templates.welcome %}
                                    {% include 'greeting_template_card.html' %}
                                {% endfor %}
                                {% if not grouped_templates.welcome %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-comments fa-3x mb-3"></i>
                                        <p>No welcome greetings found. Add one to get started!</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Response Greetings -->
                        <div class="tab-pane fade" id="response" role="tabpanel">
                            <div class="greeting-templates-container" data-type="response">
                                {% for template in grouped_templates.response %}
                                    {% include 'greeting_template_card.html' %}
                                {% endfor %}
                                {% if not grouped_templates.response %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-reply fa-3x mb-3"></i>
                                        <p>No response greetings found. Add one to get started!</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Return User Greetings -->
                        <div class="tab-pane fade" id="return-user" role="tabpanel">
                            <div class="greeting-templates-container" data-type="return_user">
                                {% for template in grouped_templates.return_user %}
                                    {% include 'greeting_template_card.html' %}
                                {% endfor %}
                                {% if not grouped_templates.return_user %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-user-check fa-3x mb-3"></i>
                                        <p>No return user greetings found. Add one to get started!</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Time-based Greetings -->
                        <div class="tab-pane fade" id="time-based" role="tabpanel">
                            <div class="greeting-templates-container" data-type="time_based">
                                {% for template in grouped_templates.time_based %}
                                    {% include 'greeting_template_card.html' %}
                                {% endfor %}
                                {% if not grouped_templates.time_based %}
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-clock fa-3x mb-3"></i>
                                        <p>No time-based greetings found. Add one to get started!</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Greeting Modal -->
<div class="modal fade" id="addGreetingModal" tabindex="-1" aria-labelledby="addGreetingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addGreetingModalLabel">Add New Greeting Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addGreetingForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="templateType" class="form-label">Template Type</label>
                        <select class="form-select" id="templateType" name="template_type" required>
                            <option value="">Select type...</option>
                            <option value="welcome">Welcome</option>
                            <option value="response">Response</option>
                            <option value="return_user">Return User</option>
                            <option value="time_based">Time-based</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="greetingText" class="form-label">Greeting Text</label>
                        <textarea class="form-control" id="greetingText" name="greeting_text" rows="3" required 
                                  placeholder="Enter greeting text. Use {name} as placeholder for user's name."></textarea>
                        <div class="form-text">Use {name} where you want the user's name to appear.</div>
                    </div>
                    <div class="mb-3">
                        <label for="weight" class="form-label">Weight</label>
                        <input type="number" class="form-control" id="weight" name="weight" value="1" min="1" max="10">
                        <div class="form-text">Higher weight means more likely to be selected (1-10).</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                            <label class="form-check-label" for="isActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Add Greeting
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Greeting Modal -->
<div class="modal fade" id="editGreetingModal" tabindex="-1" aria-labelledby="editGreetingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editGreetingModalLabel">Edit Greeting Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editGreetingForm">
                <input type="hidden" id="editTemplateId" name="template_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editTemplateType" class="form-label">Template Type</label>
                        <select class="form-select" id="editTemplateType" name="template_type" required>
                            <option value="welcome">Welcome</option>
                            <option value="response">Response</option>
                            <option value="return_user">Return User</option>
                            <option value="time_based">Time-based</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editGreetingText" class="form-label">Greeting Text</label>
                        <textarea class="form-control" id="editGreetingText" name="greeting_text" rows="3" required></textarea>
                        <div class="form-text">Use {name} where you want the user's name to appear.</div>
                    </div>
                    <div class="mb-3">
                        <label for="editWeight" class="form-label">Weight</label>
                        <input type="number" class="form-control" id="editWeight" name="weight" min="1" max="10">
                        <div class="form-text">Higher weight means more likely to be selected (1-10).</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive" name="is_active">
                            <label class="form-check-label" for="editIsActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Update Greeting
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/greeting_management.js"></script>
{% endblock %}
