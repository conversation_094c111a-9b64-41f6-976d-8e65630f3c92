{% extends "auth_base.html" %}
{% from "macros/ui_components.html" import button, input %}

{% block title %}Login{% endblock %}

{% block card_title %}Document Management System{% endblock %}
{% block card_subtitle %}Please log in to continue{% endblock %}

{% block content %}

    <form method="POST" action="{{ url_for('user.login') }}">
        {{ form.csrf_token }}

        <div class="mb-3">
            <label for="username" class="form-label">Username</label>
            {{ form.username(class="form-control", placeholder="Enter your username") }}
            {% if form.username.errors %}
                <div class="text-danger small mt-1">{{ form.username.errors[0] }}</div>
            {% endif %}
        </div>

        <div class="mb-4">
            <label for="password" class="form-label">Password</label>
            {{ form.password(class="form-control", placeholder="Enter your password") }}
            {% if form.password.errors %}
                <div class="text-danger small mt-1">{{ form.password.errors[0] }}</div>
            {% endif %}
        </div>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="form-check">
                {{ form.remember(class="form-check-input") }}
                <label for="remember" class="form-check-label">Remember Me</label>
            </div>
            <a href="{{ url_for('user.forgot_password') }}" class="text-primary small">Forgot Password?</a>
        </div>

        <div class="d-grid">
            {{ button("Log In", "primary", "md", "fas fa-sign-in-alt", None, None, "w-100") }}
        </div>
    </form>
{% endblock %}

{% block footer_links %}
    <div class="text-center mt-4">
        <p class="text-muted small">
            Don't have an account?
            <a href="{{ url_for('user.register') }}" class="text-primary">Register</a>
        </p>
    </div>

    <div class="text-center mt-3">
        <a href="/" class="text-muted small"><i class="fas fa-arrow-left me-1"></i>Back to Home</a>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Store device fingerprint if available
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');

            form.addEventListener('submit', function(e) {
                // Check if fingerprint is available in localStorage
                const fingerprint = localStorage.getItem('deviceFingerprint');

                if (fingerprint) {
                    // Add fingerprint to form
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'device_fingerprint';
                    input.value = fingerprint;
                    form.appendChild(input);
                }
            });
        });
    </script>
{% endblock %}
