<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unified Config Sliders</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2>Test Unified Config Threshold Sliders</h2>
                <div id="query-config-container">
                    <!-- This will be populated by AJAX -->
                    <p>Loading query configuration...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simulate the unified config loading process
        async function loadQueryConfig() {
            try {
                console.log('Loading query config...');
                const response = await fetch('/admin/query_config_partial');
                if (response.ok) {
                    const html = await response.text();
                    document.getElementById('query-config-container').innerHTML = html;
                    console.log('Query config loaded, setting up functionality...');
                    
                    // Setup query config specific functionality
                    setupQueryConfigFunctionality();
                } else {
                    console.error('Failed to load query configuration content');
                    document.getElementById('query-config-container').innerHTML = '<p class="text-danger">Failed to load query configuration content</p>';
                }
            } catch (error) {
                console.error(`Error loading query configuration: ${error.message}`);
                document.getElementById('query-config-container').innerHTML = `<p class="text-danger">Error loading query configuration: ${error.message}</p>`;
            }
        }

        function setupQueryConfigFunctionality() {
            console.log('Setting up query config functionality...');
            // Setup enhanced threshold sliders with a small delay to ensure DOM is ready
            setTimeout(() => {
                setupEnhancedThresholdSliders();
            }, 100);
        }

        // Enhanced Threshold Slider Setup
        function setupEnhancedThresholdSliders() {
            console.log('Setting up enhanced threshold sliders...');
            
            const sliders = [
                {
                    id: 'hallucination_threshold_strict',
                    type: 'strict',
                    valueId: 'hallucination_threshold_strict_value',
                    badgeId: 'strict_mode_badge',
                    indicatorId: 'strict_indicator',
                    descriptionId: 'strict_description',
                    trackId: 'strict_track'
                },
                {
                    id: 'hallucination_threshold_balanced',
                    type: 'balanced',
                    valueId: 'hallucination_threshold_balanced_value',
                    badgeId: 'balanced_mode_badge',
                    indicatorId: 'balanced_indicator',
                    descriptionId: 'balanced_description',
                    trackId: 'balanced_track'
                },
                {
                    id: 'hallucination_threshold_default',
                    type: 'default',
                    valueId: 'hallucination_threshold_default_value',
                    badgeId: 'default_mode_badge',
                    indicatorId: 'default_indicator',
                    descriptionId: 'default_description',
                    trackId: 'default_track'
                }
            ];

            sliders.forEach(config => {
                const slider = document.getElementById(config.id);
                const valueDisplay = document.getElementById(config.valueId);
                const badge = document.getElementById(config.badgeId);
                const indicator = document.getElementById(config.indicatorId);
                const description = document.getElementById(config.descriptionId);
                const track = slider ? slider.parentNode.querySelector('.slider-track-fill') : null;

                console.log(`Checking slider ${config.id}:`, {
                    slider: !!slider,
                    valueDisplay: !!valueDisplay,
                    badge: !!badge,
                    indicator: !!indicator,
                    description: !!description,
                    track: !!track
                });

                if (slider && valueDisplay) {
                    console.log(`Setting up slider: ${config.id}`);
                    
                    // Simple test setup
                    slider.addEventListener('input', function() {
                        console.log(`Slider ${config.id} changed to: ${this.value}`);
                        valueDisplay.textContent = parseFloat(this.value).toFixed(2);
                        
                        if (badge) {
                            const value = parseFloat(this.value);
                            if (value <= 0.4) {
                                badge.textContent = 'Lenient';
                                badge.className = 'badge bg-success threshold-badge';
                            } else if (value <= 0.7) {
                                badge.textContent = 'Moderate';
                                badge.className = 'badge bg-warning text-dark threshold-badge';
                            } else {
                                badge.textContent = 'Strict';
                                badge.className = 'badge bg-danger threshold-badge';
                            }
                        }
                        
                        if (track) {
                            const percentage = ((parseFloat(this.value) - parseFloat(this.min)) / (parseFloat(this.max) - parseFloat(this.min))) * 100;
                            track.style.width = percentage + '%';
                        }
                        
                        if (indicator) {
                            const percentage = ((parseFloat(this.value) - parseFloat(this.min)) / (parseFloat(this.max) - parseFloat(this.min))) * 100;
                            indicator.style.width = percentage + '%';
                        }
                    });
                    
                    // Trigger initial update
                    slider.dispatchEvent(new Event('input'));
                } else {
                    console.warn(`Missing elements for slider ${config.id}`);
                }
            });
        }

        // Load the config when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting query config load...');
            loadQueryConfig();
        });
    </script>
</body>
</html>
