{% extends "admin_base.html" %}

{% block title %}Permission Groups{% endblock %}

{% block head %}
<style>
    /* Styles for permission toggle rows */
    tr.updating {
        background-color: rgba(0, 123, 255, 0.1);
    }

    /* Improve toggle switch visibility */
    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    /* Add transition effects */
    .badge, .form-check-input {
        transition: all 0.3s ease;
    }

    /* Tab styling */
    .nav-tabs .nav-link {
        cursor: pointer;
    }

    /* System groups styling */
    .system-group .card-header {
        background-color: #6c757d;
    }

    /* Ensure tab content is visible */
    .tab-pane.fade.show {
        opacity: 1;
    }

    /* Add visual feedback for tab switching */
    .tab-pane.fade {
        transition: opacity 0.15s linear;
    }

    /* Fix for Bootstrap 5 tab content */
    .tab-content > .active {
        display: block !important;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">Permission Groups</h1>
            <p class="text-muted">Manage permission groups to easily assign sets of permissions to users.</p>

            <!-- Session Expiration Warning -->
            <div id="sessionWarning" class="alert alert-warning d-none">
                <i class="fas fa-exclamation-triangle"></i> Your session may have expired. <a href="/admin" class="alert-link">Click here to log in again</a>.
            </div>

            <!-- Success Message -->
            <div id="successMessage" class="alert alert-success d-none">
                <i class="fas fa-check-circle"></i> <span id="successText">Permissions updated successfully!</span>
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="alert alert-danger d-none">
                <i class="fas fa-times-circle"></i> <span id="errorText">An error occurred.</span>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Permission groups allow you to define sets of permissions that can be assigned to multiple users.
                When a user is assigned to a group, they inherit all the permissions of that group. Individual permission overrides can still be set on a per-user basis.
            </div>

            <!-- Quick Access Tabs for System Groups -->
            <ul class="nav nav-tabs mb-4" id="systemGroupTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-groups-tab" data-bs-toggle="tab" data-bs-target="#all-groups" type="button" role="tab" aria-controls="all-groups" aria-selected="true">
                        <i class="fas fa-users"></i> All Groups
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="editor-tab" data-bs-toggle="tab" data-bs-target="#editor-group" type="button" role="tab" aria-controls="editor-group" aria-selected="false">
                        <i class="fas fa-edit"></i> Editor Group
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="viewer-tab" data-bs-toggle="tab" data-bs-target="#viewer-group" type="button" role="tab" aria-controls="viewer-group" aria-selected="false">
                        <i class="fas fa-eye"></i> Viewer Group
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="systemGroupTabsContent">
                <!-- All Groups Tab -->
                <div class="tab-pane fade show active" id="all-groups" role="tabpanel" aria-labelledby="all-groups-tab">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Available Groups</h5>
                        </div>
                        <div class="card-body">
                            <div class="accordion" id="groupsAccordion">
                                {% for group in groups %}
                                <div class="card mb-2 {% if group.name in ['Editor', 'Viewer'] %}system-group{% endif %}" id="group-card-{{ group.group_id }}">
                                    <div class="card-header d-flex justify-content-between align-items-center" id="heading{{ group.group_id }}">
                                        <h5 class="mb-0">
                                            <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ group.group_id }}" aria-expanded="false" aria-controls="collapse{{ group.group_id }}">
                                                {{ group.name }} <span class="badge bg-info ms-2">{{ group.user_count }} users</span>
                                            </button>
                                        </h5>
                                        <div class="d-flex">
                                            <button class="btn btn-sm btn-outline-primary edit-group-btn me-2" data-group-id="{{ group.group_id }}" data-group-name="{{ group.name }}" data-group-description="{{ group.description }}">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-group-btn" data-group-id="{{ group.group_id }}" data-group-name="{{ group.name }}">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                    <div id="collapse{{ group.group_id }}" class="collapse" aria-labelledby="heading{{ group.group_id }}" data-bs-parent="#groupsAccordion">
                                        <div class="card-body">
                                            <p class="text-muted">{{ group.description }}</p>
                                            <h6 class="mt-3 mb-2">Permissions:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>Function</th>
                                                            <th>Description</th>
                                                            <th class="text-center">Status</th>
                                                            <th class="text-center">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for function in dashboard_functions %}
                                                        <tr data-function="{{ function.id }}">
                                                            <td>{{ function.name }}</td>
                                                            <td>{{ function.description }}</td>
                                                            <td class="text-center">
                                                                {% if group.permissions.get(function.id, False) %}
                                                                <span class="badge bg-success">Enabled</span>
                                                                {% else %}
                                                                <span class="badge bg-danger">Disabled</span>
                                                                {% endif %}
                                                            </td>
                                                            <td class="text-center">
                                                                <div class="form-check form-switch d-flex justify-content-center">
                                                                    <input type="checkbox" class="form-check-input group-permission-toggle"
                                                                           id="group{{ group.group_id }}_{{ function.id }}"
                                                                           data-group-id="{{ group.group_id }}"
                                                                           data-function-name="{{ function.id }}"
                                                                           data-group-name="{{ group.name }}"
                                                                           {% if group.permissions.get(function.id, False) %}checked{% endif %}>
                                                                    <label class="form-check-label" for="group{{ group.group_id }}_{{ function.id }}"></label>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="mt-3">
                                                <button class="btn btn-success save-all-btn" data-group-id="{{ group.group_id }}">
                                                    <i class="fas fa-save"></i> Save All Changes
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-success" id="addGroupBtn">
                                <i class="fas fa-plus"></i> Add New Group
                            </button>
                            <button class="btn btn-info ms-2" id="syncPermissionsBtn">
                                <i class="fas fa-sync"></i> Sync New Module Permissions
                            </button>
                            <button class="btn btn-warning ms-2" id="checkMissingBtn">
                                <i class="fas fa-search"></i> Check Missing Permissions
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Editor Group Tab -->
                <div class="tab-pane fade" id="editor-group" role="tabpanel" aria-labelledby="editor-tab">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Editor Group Permissions</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="editorPermissionsTable">
                                    <thead>
                                        <tr>
                                            <th>Function</th>
                                            <th>Description</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="editorPermissionsBody">
                                        <!-- Will be populated by JavaScript -->
                                        <tr>
                                            <td colspan="4" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-success" id="saveEditorBtn">
                                <i class="fas fa-save"></i> Save All Editor Permissions
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Viewer Group Tab -->
                <div class="tab-pane fade" id="viewer-group" role="tabpanel" aria-labelledby="viewer-tab">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Viewer Group Permissions</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="viewerPermissionsTable">
                                    <thead>
                                        <tr>
                                            <th>Function</th>
                                            <th>Description</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="viewerPermissionsBody">
                                        <!-- Will be populated by JavaScript -->
                                        <tr>
                                            <td colspan="4" class="text-center">
                                                <div class="spinner-border text-info" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-success" id="saveViewerBtn">
                                <i class="fas fa-save"></i> Save All Viewer Permissions
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Group Modal -->
<div class="modal fade" id="groupModal" tabindex="-1" aria-labelledby="groupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="groupModalLabel">Add New Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <input type="hidden" id="groupId" value="">
                    <div class="mb-3">
                        <label for="groupName" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="groupName" required>
                    </div>
                    <div class="mb-3">
                        <label for="groupDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="groupDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveGroupBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteGroupModal" tabindex="-1" aria-labelledby="deleteGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteGroupModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the group "<span id="deleteGroupName"></span>"?</p>
                <p class="text-danger">This action cannot be undone. All users assigned to this group will have their group assignment removed.</p>
                <input type="hidden" id="deleteGroupId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Session Expiration Warning -->
<div class="alert alert-warning alert-dismissible fade show d-none" role="alert" id="sessionWarning">
    <strong>Session Expired!</strong> Your session may have expired. Please refresh the page and try again.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Make dashboard functions available to JavaScript
    window.dashboardFunctions = {{ dashboard_functions|tojson|safe }};

    // Store permission changes for batch updates
    let permissionChanges = {};
    let editorGroup = null;
    let viewerGroup = null;
    let dashboardFunctions = [];

    $(document).ready(function() {
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;

        // Initialize permission changes storage
        initializePermissionChanges();

        // Load Editor and Viewer group data for the tabs
        loadSystemGroups();

        // Initialize tabs
        initializeTabs();

        // Show loading spinner
        function showLoading(element) {
            $(element).addClass('position-relative');
            $(element).append('<div class="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-white bg-opacity-75"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        }

        // Hide loading spinner
        function hideLoading(element) {
            $(element).removeClass('position-relative');
            $(element).find('.position-absolute').remove();
        }

        // Show global loading overlay
        function showGlobalLoading() {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            `;
            document.body.appendChild(overlay);
            return overlay;
        }

        // Hide global loading overlay
        function hideGlobalLoading(overlay) {
            if (overlay && document.body.contains(overlay)) {
                document.body.removeChild(overlay);
            }
        }

        // Show message
        function showMessage(type, message) {
            // Hide all messages first
            $('#successMessage, #errorMessage, #sessionWarning').addClass('d-none');

            if (type === 'success') {
                $('#successText').text(message);
                $('#successMessage').removeClass('d-none');

                // Auto-hide success messages after 5 seconds
                setTimeout(() => {
                    $('#successMessage').addClass('d-none');
                }, 5000);
            } else if (type === 'error') {
                $('#errorText').text(message);
                $('#errorMessage').removeClass('d-none');
            } else if (type === 'session') {
                $('#sessionWarning').removeClass('d-none');
            }
        }

        // Handle session expiration
        function handleSessionExpiration() {
            showMessage('session');
            setTimeout(function() {
                window.location.href = '/admin';
            }, 3000);
        }

        // Handle AJAX errors
        function handleAjaxError(xhr, status, error, defaultMessage) {
            // Check if the response is HTML (likely a redirect to login page)
            if (xhr.responseText && xhr.responseText.trim().startsWith('<!doctype')) {
                handleSessionExpiration();
                return;
            }

            // Try to parse error message if possible
            let errorMsg = defaultMessage || 'An error occurred';
            try {
                const errorResponse = JSON.parse(xhr.responseText);
                if (errorResponse.error) {
                    errorMsg = errorResponse.error;
                }
            } catch (e) {
                // If parsing fails, use the default error message
                console.error('Error parsing response:', e);
            }

            toastr.error(errorMsg);
            showMessage('error', errorMsg);
        }

        // Safe API call function with proper error handling
        async function safeApiCall(url, options = {}) {
            const overlay = showGlobalLoading();

            try {
                // Set timeout for request
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                if (!options.signal) {
                    options.signal = controller.signal;
                }

                const response = await fetch(url, options);
                clearTimeout(timeoutId);

                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    hideGlobalLoading(overlay);
                    handleSessionExpiration();
                    return null;
                }

                // Check for HTTP errors
                if (!response.ok) {
                    const errorMessage = `Server error: ${response.status} ${response.statusText}`;
                    console.error(errorMessage);
                    hideGlobalLoading(overlay);
                    showMessage('error', errorMessage);

                    // Try to get more details from response
                    try {
                        const errorData = await response.json();
                        if (errorData && errorData.error) {
                            showMessage('error', errorData.error);
                        }
                    } catch (e) {
                        // Ignore JSON parsing errors for error responses
                    }

                    return null;
                }

                // Try to parse as JSON
                try {
                    const responseText = await response.text();

                    // Try to parse the text as JSON
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (jsonError) {
                        console.error('JSON parsing error:', jsonError, 'Response text:', responseText);
                        hideGlobalLoading(overlay);
                        showMessage('error', `Failed to parse server response: ${jsonError.message}`);
                        return null;
                    }

                    hideGlobalLoading(overlay);
                    return { ok: response.ok, status: response.status, data };
                } catch (parseError) {
                    console.error('Response processing error:', parseError);
                    hideGlobalLoading(overlay);
                    showMessage('error', 'Failed to process server response.');
                    return null;
                }
            } catch (fetchError) {
                // Handle abort error separately
                if (fetchError.name === 'AbortError') {
                    console.error('Request timed out');
                    hideGlobalLoading(overlay);
                    showMessage('error', 'Request timed out. Please try again.');
                    return null;
                }

                console.error('Fetch error:', fetchError);
                hideGlobalLoading(overlay);
                showMessage('error', `Network error: ${fetchError.message}`);
                return null;
            }
        }

        // Initialize permission changes storage
        function initializePermissionChanges() {
            permissionChanges = {};

            // Initialize for each group
            $('.group-permission-toggle').each(function() {
                const groupId = $(this).data('group-id');
                if (!permissionChanges[groupId]) {
                    permissionChanges[groupId] = {};
                }
            });
        }

        // Load system groups (Editor and Viewer) for the tabs
        async function loadSystemGroups() {
            const result = await safeApiCall('/admin/permission_groups?format=json', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!result || !result.data.success) {
                showMessage('error', 'Failed to load group data');
                return;
            }

            const groups = result.data.groups;
            dashboardFunctions = window.dashboardFunctions || [];

            // Find Editor and Viewer groups
            editorGroup = groups.find(g => g.name === 'Editor');
            viewerGroup = groups.find(g => g.name === 'Viewer');

            // Render the tables
            if (editorGroup) {
                renderPermissionsTable('editor', editorGroup, dashboardFunctions);
            }

            if (viewerGroup) {
                renderPermissionsTable('viewer', viewerGroup, dashboardFunctions);
            }
        }

        // Render permissions table for a specific group
        function renderPermissionsTable(groupType, group, functions) {
            const tableBody = $(`#${groupType}PermissionsBody`);
            tableBody.empty();

            if (!group) {
                tableBody.html(`<tr><td colspan="4" class="text-center">Group not found</td></tr>`);
                return;
            }

            // Ensure permissions object exists
            if (!group.permissions) {
                group.permissions = {};
            }

            functions.forEach(func => {
                const isEnabled = group.permissions[func.id] || false;
                const badgeClass = isEnabled ? 'bg-success' : 'bg-danger';

                const row = `
                    <tr data-function="${func.id}">
                        <td>${func.name}</td>
                        <td>${func.description}</td>
                        <td class="text-center">
                            <span class="badge ${badgeClass}">
                                ${isEnabled ? 'Enabled' : 'Disabled'}
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="form-check form-switch d-flex justify-content-center">
                                <input type="checkbox" class="form-check-input system-permission-toggle"
                                       id="${groupType}_${func.id}"
                                       data-group-type="${groupType}"
                                       data-group-id="${group.group_id}"
                                       data-function-name="${func.id}"
                                       ${isEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="${groupType}_${func.id}"></label>
                            </div>
                        </td>
                    </tr>
                `;

                tableBody.append(row);
            });

            // Attach event listeners to toggles
            $(`.system-permission-toggle[data-group-type="${groupType}"]`).change(function() {
                const functionName = $(this).data('function-name');
                const enabled = $(this).prop('checked');
                const groupId = $(this).data('group-id');
                const permissionElement = $(this).closest('tr');

                // Add updating class to show it's being processed
                permissionElement.addClass('updating');

                // Store the change
                if (!permissionChanges[groupId]) {
                    permissionChanges[groupId] = {};
                }
                permissionChanges[groupId][functionName] = enabled;

                // Update the badge
                const badge = $(this).closest('tr').find('.badge');
                badge.removeClass('bg-success bg-danger')
                     .addClass(enabled ? 'bg-success' : 'bg-danger')
                     .text(enabled ? 'Enabled' : 'Disabled');

                // Remove updating class
                permissionElement.removeClass('updating');
            });
        }

        // Initialize tabs
        function initializeTabs() {
            // Create tab instances using Bootstrap 5 Tab API
            const tabElements = document.querySelectorAll('#systemGroupTabs [data-bs-toggle="tab"]');
            tabElements.forEach(tabElement => {
                const tab = new bootstrap.Tab(tabElement);

                // Add event listener for tab show event
                tabElement.addEventListener('shown.bs.tab', function (event) {
                    const activeTab = event.target.getAttribute('data-bs-target').replace('#', '');

                    // If we're switching to a specific group tab, expand that group's accordion
                    if (activeTab === 'editor-group' && editorGroup) {
                        // Re-render the editor permissions table to ensure it's up to date
                        renderPermissionsTable('editor', editorGroup, dashboardFunctions);
                    } else if (activeTab === 'viewer-group' && viewerGroup) {
                        // Re-render the viewer permissions table to ensure it's up to date
                        renderPermissionsTable('viewer', viewerGroup, dashboardFunctions);
                    } else if (activeTab === 'all-groups') {
                        // If we're going back to all groups, refresh the page to show latest changes
                        // This is a simple approach - a more sophisticated one would update the accordion without reload
                        // window.location.reload();
                    }
                });
            });
        }

        // Toggle group permission with batch update capability
        $('.group-permission-toggle').change(function() {
            const toggleElement = $(this);
            const row = toggleElement.closest('tr');
            const groupId = toggleElement.data('group-id');
            const functionName = toggleElement.data('function-name');
            const enabled = toggleElement.prop('checked');
            const groupName = toggleElement.data('group-name');

            // Add visual feedback
            row.addClass('table-active');

            // Store the change for batch update
            if (!permissionChanges[groupId]) {
                permissionChanges[groupId] = {};
            }
            permissionChanges[groupId][functionName] = enabled;

            // Update badge immediately for visual feedback
            const badge = row.find('.badge');
            badge.removeClass('bg-success bg-danger')
                 .addClass(enabled ? 'bg-success' : 'bg-danger')
                 .text(enabled ? 'Enabled' : 'Disabled');

            // Remove visual feedback
            row.removeClass('table-active');

            // Show toast notification
            toastr.info(`Permission change queued for ${groupName}. Click "Save All Changes" to apply.`);
        });

        // Save all permission changes for a group
        async function saveGroupPermissions(groupId) {
            // Check if there are any changes to save
            if (!permissionChanges[groupId] || Object.keys(permissionChanges[groupId]).length === 0) {
                toastr.info('No changes to save');
                return;
            }

            const result = await safeApiCall('/admin/permission_groups/batch_update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    group_id: groupId,
                    permissions: permissionChanges[groupId]
                })
            });

            if (result && result.data.success) {
                showMessage('success', 'Permissions updated successfully');
                toastr.success('Permissions updated successfully');

                // Clear changes for this group
                permissionChanges[groupId] = {};

                // Refresh system groups data if needed
                if ((editorGroup && editorGroup.group_id == groupId) ||
                    (viewerGroup && viewerGroup.group_id == groupId)) {
                    loadSystemGroups();
                }
            } else {
                const errorMsg = result?.data?.error || 'Failed to update permissions';
                showMessage('error', errorMsg);
                toastr.error(errorMsg);
            }
        }

        // Save all changes for Editor group
        $('#saveEditorBtn').click(function() {
            if (editorGroup) {
                saveGroupPermissions(editorGroup.group_id);
            } else {
                showMessage('error', 'Editor group not found');
            }
        });

        // Save all changes for Viewer group
        $('#saveViewerBtn').click(function() {
            if (viewerGroup) {
                saveGroupPermissions(viewerGroup.group_id);
            } else {
                showMessage('error', 'Viewer group not found');
            }
        });

        // Save all changes for any group
        $('.save-all-btn').click(function() {
            const groupId = $(this).data('group-id');
            saveGroupPermissions(groupId);
        });

        // Open add group modal
        $('#addGroupBtn').click(function() {
            $('#groupModalLabel').text('Add New Group');
            $('#groupId').val('');
            $('#groupName').val('');
            $('#groupDescription').val('');

            // Use Bootstrap 5 modal method
            const modal = new bootstrap.Modal(document.getElementById('groupModal'));
            modal.show();
        });

        // Open edit group modal
        $(document).on('click', '.edit-group-btn', function() {
            const groupId = $(this).data('group-id');
            const groupName = $(this).data('group-name');
            const groupDescription = $(this).data('group-description');

            $('#groupModalLabel').text('Edit Group');
            $('#groupId').val(groupId);
            $('#groupName').val(groupName);
            $('#groupDescription').val(groupDescription);

            // Use Bootstrap 5 modal method
            const modal = new bootstrap.Modal(document.getElementById('groupModal'));
            modal.show();
        });

        // Open delete confirmation modal
        $(document).on('click', '.delete-group-btn', function() {
            const groupId = $(this).data('group-id');
            const groupName = $(this).data('group-name');

            $('#deleteGroupId').val(groupId);
            $('#deleteGroupName').text(groupName);

            // Use Bootstrap 5 modal method
            const modal = new bootstrap.Modal(document.getElementById('deleteGroupModal'));
            modal.show();
        });

        // Save group
        $('#saveGroupBtn').click(function() {
            const groupId = $('#groupId').val();
            const groupName = $('#groupName').val();
            const groupDescription = $('#groupDescription').val();

            if (!groupName) {
                toastr.error('Group name is required');
                return;
            }

            const url = groupId ? `/admin/permission_groups/${groupId}` : '/admin/permission_groups';
            const modalBody = $('#groupModal .modal-body');

            // Show loading spinner
            showLoading(modalBody);

            $.ajax({
                url: url,
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: JSON.stringify({
                    name: groupName,
                    description: groupDescription
                }),
                success: function(response) {
                    // Hide loading spinner
                    hideLoading(modalBody);

                    if (response.success) {
                        toastr.success('Group saved successfully');

                        // Hide modal using Bootstrap 5 method
                        const modal = bootstrap.Modal.getInstance(document.getElementById('groupModal'));
                        modal.hide();

                        // Reload page to show changes
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.error || 'Failed to save group');
                    }
                },
                error: function(xhr, status, error) {
                    // Hide loading spinner
                    hideLoading(modalBody);

                    handleAjaxError(xhr, status, error, 'An error occurred while saving the group');
                }
            });
        });

        // Delete group
        $('#confirmDeleteBtn').click(function() {
            const groupId = $('#deleteGroupId').val();
            const modalBody = $('#deleteGroupModal .modal-body');

            // Show loading spinner
            showLoading(modalBody);

            $.ajax({
                url: `/admin/permission_groups/${groupId}/delete`,
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Hide loading spinner
                    hideLoading(modalBody);

                    if (response.success) {
                        toastr.success('Group deleted successfully');

                        // Hide modal using Bootstrap 5 method
                        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteGroupModal'));
                        modal.hide();

                        // Reload page to show changes
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.error || 'Failed to delete group');
                    }
                },
                error: function(xhr, status, error) {
                    // Hide loading spinner
                    hideLoading(modalBody);

                    handleAjaxError(xhr, status, error, 'An error occurred while deleting the group');
                }
            });
        });

        // Sync permissions for new modules
        $('#syncPermissionsBtn').click(function() {
            const button = $(this);
            const originalText = button.html();

            // Show loading state
            button.prop('disabled', true);
            button.html('<i class="fas fa-spinner fa-spin"></i> Syncing...');

            $.ajax({
                url: '/admin/permission_groups/sync',
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Restore button state
                    button.prop('disabled', false);
                    button.html(originalText);

                    if (response.success) {
                        let message = response.message;

                        // Show details of what was added
                        if (response.added_permissions && Object.keys(response.added_permissions).length > 0) {
                            let details = '\n\nAdded permissions:';
                            for (const [groupName, permissions] of Object.entries(response.added_permissions)) {
                                if (permissions.length > 0) {
                                    details += `\n• ${groupName}: ${permissions.join(', ')}`;
                                }
                            }
                            message += details;
                        }

                        toastr.success(message);
                        showMessage('success', message);

                        // Reload page to show changes
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        toastr.error(response.error || 'Failed to sync permissions');
                        showMessage('error', response.error || 'Failed to sync permissions');
                    }
                },
                error: function(xhr, status, error) {
                    // Restore button state
                    button.prop('disabled', false);
                    button.html(originalText);

                    handleAjaxError(xhr, status, error, 'An error occurred while syncing permissions');
                }
            });
        });

        // Check missing permissions
        $('#checkMissingBtn').click(function() {
            const button = $(this);
            const originalText = button.html();

            // Show loading state
            button.prop('disabled', true);
            button.html('<i class="fas fa-spinner fa-spin"></i> Checking...');

            $.ajax({
                url: '/admin/permission_groups/missing',
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Restore button state
                    button.prop('disabled', false);
                    button.html(originalText);

                    if (response.success) {
                        const missingPermissions = response.missing_permissions;

                        if (Object.keys(missingPermissions).length === 0) {
                            toastr.success('All permission groups are up to date!');
                            showMessage('success', 'All permission groups are up to date!');
                        } else {
                            let message = 'Missing permissions found:\n';
                            for (const [groupName, permissions] of Object.entries(missingPermissions)) {
                                message += `\n• ${groupName}: ${permissions.join(', ')}`;
                            }

                            // Show in a modal or alert
                            alert(message + '\n\nUse "Sync New Module Permissions" to add these automatically.');
                        }
                    } else {
                        toastr.error(response.error || 'Failed to check missing permissions');
                        showMessage('error', response.error || 'Failed to check missing permissions');
                    }
                },
                error: function(xhr, status, error) {
                    // Restore button state
                    button.prop('disabled', false);
                    button.html(originalText);

                    handleAjaxError(xhr, status, error, 'An error occurred while checking missing permissions');
                }
            });
        });
    });
</script>
{% endblock %}
